import React, { useEffect, useState } from "react";
import { Container } from "react-bootstrap";
// import { layersSetting } from "../helper/layers";
import Extent from "@arcgis/core/geometry/Extent";
import {
  faCaretDown,
  faCaretLeft,
  faCaretRight,
} from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTranslation } from "react-i18next";
import i18n from "../i18n";

import zoomInGoogle from "../assets/icons/zoomInGoogle.svg";
import arrow_drop_down from "../assets/icons/arrow_drop_down.svg";
import arrow_drop_up from "../assets/icons/arrow_drop_up.svg";
import { executeGPTool, showLoading } from "../helper/common_func";

export default function KmlGoogle(props) {
  const { t } = useTranslation("common");

  const [legends, setLegends] = useState(props.map.__mapInfo.info.$legends);

  useEffect(() => {
    window.DisableActiveTool();
  }, []);

  const changeLayer = (layer, key) => {
    legends[key].isExportGoogle = !legends[key].isExportGoogle;
    setLegends([...legends]);
  };

  const generateKml = async () => {
    showLoading(true);

    let exportedLayers = legends
      .filter((layer) => layer.isExportGoogle)
      .map((d) => d);

    try {
      if (exportedLayers.length) {
        for (let index = 0; index < exportedLayers.length; index++) {
          const layer = exportedLayers[index];

          const layerUrl = `${window.mapUrl}/${layer.layerId}/query`;
          const queryParams = new URLSearchParams({
            where: "1=1",
            outFields: "*",
            f: "KMZ",
          });

          const fullUrl = `${layerUrl}?${queryParams.toString()}`;
          const desiredFilename = `${props.mainData.layers[layer.layerName].arabicName}.kmz`; // 🔁 Change this as needed

          let blob = await fetch(fullUrl).then((response) => {
            if (!response.ok) throw new Error("Download failed");
            return response.blob();
          });
          const url = URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.href = url;
          a.download = desiredFilename; // ✅ Custom filename here
          document.body.appendChild(a);
          a.click();
          a.remove();
          URL.revokeObjectURL(url);

          // debugger;
          // executeGPTool(
            
          //   `${window.kmlToJSONGPUrl}?token=Fk_NFVtb227dbizh6MzDpMs-J4F1rdP1NORP9Cc85Vo.`,
          //   {
          //     KML_File_Name: "sub5-2021/uploadMultifiles/5313399c-352a-41e8-a5fb-013e8570705e.kmz",
          //   },
          //   (result) => {
          //     debugger;
          //   },
          //   (error) => {
          //     debugger;
          //   },
          //   "output_value",
          //   "submitJob",
          //   "Fk_NFVtb227dbizh6MzDpMs-J4F1rdP1NORP9Cc85Vo."
          // )
        }
      }
    } catch (error) {
    } finally {
      showLoading(false);
    }
  };

  const saveFile = (res, fileName) => {
    // Get file name from url.
    //var filename = res.substring(res.lastIndexOf("/") + 1).split("?")[0];
    var xhr = new XMLHttpRequest();

    xhr.responseType = "blob";
    xhr.onload = function () {
      if (xhr.status == 200) {
        var a = document.createElement("a");
        a.href = window.URL.createObjectURL(xhr.response); // xhr.response is a blob
        a.download = fileName + ".kml"; // Set the file name.
        a.style.display = "none";
        document.body.appendChild(a);
        a.click();
      } else {
        //$rootScope.notifySystem("error", "mapViewer.ERROR");
      }
    };
    xhr.open("GET", res);
    xhr.send();
  };

  // const expand = (layer, key) => {
  //   legends[key].expand = !legends[key].expand;
  //   setLegends([...legends]);
  // };

  const expand = (layer, key) => {
    const updatedLegends = legends.map((item, index) => ({
      ...item,
      expand: index === key ? !item.expand : false,
    }));
    setLegends(updatedLegends);
  };

  return (
    <Container style={{ textAlign: "center" }}>
      <section>
        <div style={{ marginTop: "10px" }}>
          <ul style={{ padding: "5px" }}>
            {legends.map((layer, key) => {
              return (
                !layer.isHidden &&
                props.mainData.layers[layer.layerName] && (
                  <div
                    style={{
                      direction: i18n.language === "ar" ? "rtl" : "ltr",
                    }}
                    key={key}
                  >
                    <div className="toc-gallery">
                      <div className="toc-gallery-content">
                        <div
                          onClick={() => expand(layer, key)}
                          style={{ cursor: "pointer" }}
                        >
                          {layer.expand ? (
                            <img src={arrow_drop_up} alt="" />
                          ) : (
                            <img src={arrow_drop_down} alt="" />
                          )}
                        </div>
                        {/* <input
                        type="checkbox"
                        style={{ marginTop: "-10px" }}
                        defaultChecked={layer.isExportGoogle}
                        onChange={(e) => changeLayer(layer, key)}
                      />
                      <label
                        style={{
                          fontSize: "13px",
                          fontWeight: "normal",
                          whiteSpace: "break-spaces",
                        }}
                      >
                        {i18n.language === "ar" &&
                        props.mainData.layers[layer.layerName]
                          ? props.mainData.layers[layer.layerName].arabicName
                          : layer.layerName}
                      </label> */}
                        <input
                          type="checkbox"
                          defaultChecked={layer.isExportGoogle}
                          onChange={(e) => changeLayer(layer, key)}
                          className="toc-gallery-content_checkbox"
                        />
                        <label className="toc-gallery-content_Label">
                          {i18n.language === "ar" &&
                          props.mainData.layers[layer.layerName]
                            ? props.mainData.layers[layer.layerName].arabicName
                            : layer.layerName}
                        </label>
                      </div>
                      <div style={{ cursor: "pointer" }}>
                        <img
                          src={zoomInGoogle}
                          alt=""
                          onClick={() => {
                            debugger;
                            const { view, __mapInfo } = props.map;
                            let layerData =
                              __mapInfo.info.$layers.layers[layer.layerId];
                            view.goTo(
                              new Extent({
                                xmin: layerData.extent.xmin - 10,
                                ymin: layerData.extent.ymin - 10,
                                xmax: layerData.extent.xmax + 10,
                                ymax: layerData.extent.ymax + 10,
                                spatialReference:
                                  layerData.extent.spatialReference,
                              })
                            );
                          }}
                        />
                      </div>
                    </div>
                    <div
                      // style={{
                      //   maxHeight: "200px",
                      //   overflowY: "scroll",
                      //   background: "#f1f1f1",
                      //   padding: "4px",
                      //   borderRadius: "20px",
                      // }}
                      className={`${layer.expand && "nestedLayerExpand"}`}
                    >
                      {layer.expand &&
                        layer.legend.map((legend, key) => {
                          return (
                            <ul
                              key={key}
                              style={{
                                display: "flex",
                                alignItems: "center",
                                paddingRight: "10px",
                                // marginTop: "8px",
                                // marginBottom: "10px",
                                textWrap: "initial",
                              }}
                            >
                              <img
                                src={
                                  "data:image/jpeg;base64," + legend.imageData
                                }
                              />
                              <div
                                style={{
                                  marginRight: "5px",
                                }}
                                className="toc-gallery-content_Label"
                              >
                                {legend.label}
                              </div>
                            </ul>
                          );
                        })}
                    </div>
                  </div>
                )
              );
            })}
          </ul>
        </div>
      </section>

      <button
        className="SearchBtn mt-3 w-25"
        size="large"
        htmlType="submit"
        onClick={() => generateKml()}
      >
        {t("extractKML")}{" "}
      </button>
    </Container>
  );
}
