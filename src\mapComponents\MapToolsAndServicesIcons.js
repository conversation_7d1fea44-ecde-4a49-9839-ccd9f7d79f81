import React, { useState, useEffect, useRef } from "react";
import eventBus from "../helper/EventBus";
import leftIcon from "../assets/images/leftMenuIcon.svg";
import Handles from "@arcgis/core/core/Handles.js";
import { FaBagShopping } from "react-icons/fa6";
import * as watchUtils from "@arcgis/core/core/watchUtils";
import Fade from "react-reveal/Fade";
import Draw from "@arcgis/core/views/draw/Draw";
import Extent from "@arcgis/core/geometry/Extent";
import Graphic from "@arcgis/core/Graphic";
import Zoom from "@arcgis/core/widgets/Zoom";
import { Tooltip } from "@mui/material";
import { useNavigate, useLocation } from "react-router-dom";
import compareLayers from "../assets/images/tools/tool1.svg";
import { toJpeg, toPng, toSvg } from "html-to-image";
import help from "../assets/images/tools/help.svg";
import inquiry from "../assets/images/tools/tool2.svg";
import googleMaps from "../assets/images/tools/tool3.svg";
import smallMap from "../assets/images/tools/tool4.svg";
import layersMenu from "../assets/images/tools/tool5.svg";
import print_img from "../assets/icons/left-bar/print.svg"; //====================================
import legend from "../assets/images/tools/tool1.svg";
import traffic from "../assets/images/tools/tool7.svg";
import screenshot from "../assets/images/tools/tool7.svg";
import hashIcon from "../assets/images/tools/tool9.svg";
import gas from "../assets/images/services/gas.svg";
import water from "../assets/images/services/water.svg";
import less from "../assets/images/services/less.svg";
import homeIcon from "../assets/images/services/homeIcon.svg";
// import fullScreenIcon from "../assets/images/services/fullScreenIcon.svg";
import more from "../assets/images/services/more.svg";
import pharmacy from "../assets/images/services/pharmacy.svg";
import foodcart from "../assets/images/services/foodcart.svg";
import hospital from "../assets/images/services/hospital.svg";
import setting from "../assets/images/services/setting.svg";
import refresh from "../assets/icons/Refresh.svg";
import ServicesSearch from "./Services/ServicesSearch";
import AllTools from "./tools/AllTools";
import { RiFileListFill } from "react-icons/ri";
import {
  CustomTileLayer,
  clearGraphicLayer,
  getLayerId,
  getFeatureDomainName,
} from "../helper/common_func";
import { useTranslation } from "react-i18next";
import html2canvas from "html2canvas";
import { BsArrowsAngleExpand } from "react-icons/bs";
import { CgArrowsExpandLeft } from "react-icons/cg";
import { MdOutlineTune } from "react-icons/md";
import { RiFullscreenFill, RiGoogleLine, RiImageAddFill } from "react-icons/ri";
import { FiArrowUpRight, FiRefreshCw } from "react-icons/fi";
import { CiEdit, CiTrash, CiZoomOut } from "react-icons/ci";
import UpdateLand from "./tools/UpdateLand";
import {
  GoArrowLeft,
  GoArrowRight,
  GoInfo,
  GoProjectRoadmap,
} from "react-icons/go";
import { IoKeyOutline, IoLayersOutline } from "react-icons/io5";
import {
  FaExchangeAlt,
  FaArrowsAlt,
  FaRoute,
  FaArrowRight,
  FaArrowLeft,
  FaRegMap,
  FaRegEdit,
} from "react-icons/fa";
import SketchViewModel from "@arcgis/core/widgets/Sketch/SketchViewModel";
import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer";
import SimpleFillSymbol from "@arcgis/core/symbols/SimpleFillSymbol";
import SimpleLineSymbol from "@arcgis/core/symbols/SimpleLineSymbol";
import SimpleMarkerSymbol from "@arcgis/core/symbols/SimpleMarkerSymbol";
import TextSymbol from "@arcgis/core/symbols/TextSymbol";
import Query from "@arcgis/core/rest/support/Query";
import * as query from "@arcgis/core/rest/query";
import Polygon from "@arcgis/core/geometry/Polygon";
import Point from "@arcgis/core/geometry/Point";

import trash_img from "../assets/icons/left-bar/trash.svg";
import help_img from "../assets/icons/left-bar/help.svg";
import move_img from "../assets/icons/left-bar/move.svg";

// import full_screen_img from "../assets/icons/left-bar/fullscreen.png";
import full_map_img from "../assets/icons/left-bar/fullMap.svg";
import google_maps_img from "../assets/icons/left-bar/googleMaps.svg";
import inquiry_img from "../assets/icons/left-bar/inquiry.svg";
import layers_menu_img from "../assets/icons/left-bar/layersMenu.svg";
import next_img from "../assets/icons/left-bar/next.svg";
import prev_img from "../assets/icons/left-bar/prev.svg";
import screenshot_img from "../assets/icons/left-bar/screenshot.svg";
import small_map_img from "../assets/icons/left-bar/smallMap.svg";
import zoom_in_img from "../assets/icons/left-bar/zoom.svg";
import zoom_out_img from "../assets/icons/left-bar/zoom-out.svg";
import traffic_img from "../assets/icons/left-bar/traffic.svg";
import map_key_img from "../assets/icons/left-bar/map_key.svg";
import compare_layer_img from "../assets/icons/left-bar/compare_layers.svg";
import collapse_img from "../assets/icons/left-bar/collapse.svg";
import full_screen_img from "../assets/icons/left-bar/Full Screen.svg";
import update_img from "../assets/icons/left-bar/update.svg";
// import full_screen_img from "../assets/icons/left-bar/15.svg"

import CompareLayers from "./tools/CompareLayers";
import { TbScanEye } from "react-icons/tb";
import { GiEarthAmerica } from "react-icons/gi";
import { LuZoomIn, LuZoomOut } from "react-icons/lu";
import { FaPrint, FaCameraRetro } from "react-icons/fa";
import { cleanup } from "@testing-library/react";
import { modulesIDs } from "../helper/constants";
//import compare_layers_img from "../assets/images/compare_layers.svg";//==========================
export default function MapToolsAndServicesIcons(props) {
  const navigate = useNavigate();
  const location = useLocation();
  const handleClick = () => {
    //   //    debugger;
    if (location.pathname != "/") {
      navigate("/");
    }
  };
  const { setClearMapEvents, clearMapEvents } = props;
  const { t } = useTranslation("map");
  const [openServSearch, setServSearch] = useState(false);
  const [activeService, setActiveService] = useState(0);
  const [activeServiceItem, setActiveServiceItem] = useState(null);
  const [openToolData, setToolData] = useState(false);
  const [activeTool, setActiveTool] = useState(0);
  const [sketch, setSketch] = useState(null);
  const [selectedFeatures, setSelectedFeatures] = useState([]);
  const [showUpdateLand, setShowUpdateLand] = useState(false);
  const [privilegeIcons, setPrivilegeIcons] = useState([]);
  const [landsAreas, setLandsAreas] = useState({ lands: [], totalArea: 0 });
  //=======================================================
  const [isShow, setIsShow] = useState(false);
  const [deleteModalVisible, setdeleteModalVisible] = useState(false);
  const eventListeners = useRef();
  const graphicsHistory = useRef();
  const [map, setMap] = useState();
  const [undoCount, setUndoCount] = useState(0);
  const [redoCount, setRedoCount] = useState(0);
  useEffect(() => {
    eventBus.on("setShowInteractivEditBar", (data) => {
      console.log("setShowInteractivEditBar", data);

      setIsShow(data.message);
      eventListeners.current = data.eventListeners
        ? data.eventListeners.current
        : null;
      graphicsHistory.current = data.graphicsHistory
        ? data.graphicsHistory.current
        : null;
      let map = data.map ? data.map : null;
      setMap(map);
    });
  }, []);
  // useEffect(() => {
  //   console.log('landsAreas updated in ParentComponent:', landsAreas, setLandsAreas);
  // }, [landsAreas, setLandsAreas]);

  const handleStopDrawing = () => {
    if (!eventListeners.current) {
      console.warn("eventListeners.current is null");
      return;
    }
    eventListeners.current.disableSelectedTool(null);
    eventListeners.current.disableGraphicState(undefined);
    eventListeners.current.sketch.current.cancelDraw();
    if (eventListeners.current.animation?.length > 0) {
      eventListeners.current.animation.forEach((element) => {
        element.current?.remove();
      });
    }
  };

  console.log("props", props);

  const [compareLayer, setCompareLayer] = useState(false);
  const enablePolygonSelectionMode = (map, onResult) => {
    if (!map) {
      console.error("Map is undefined");
      return;
    }

    const graphicsLayer = map.findLayerById("highlightGraphicLayer");

    if (!graphicsLayer) {
      console.error("Graphics layer not found!");
      return;
    }

    // Create a separate layer for measurements and labels
    let measurementsLayer = map.findLayerById("MeasurementsLayer");
    if (!measurementsLayer) {
      measurementsLayer = new GraphicsLayer({ id: "MeasurementsLayer" });
      map.add(measurementsLayer);
    }

    let SketchLayer = map.findLayerById("SelectGraphicLayer");
    console.log("SketchLayer", SketchLayer);

    if (!SketchLayer) {
      SketchLayer = new GraphicsLayer({ id: "SelectGraphicLayer" });
      map.add(SketchLayer);
    }

    const view = window.__view || map.view;
    if (!view) {
      console.error("View is undefined");
      return;
    }

    const sketchViewModel = new SketchViewModel({
      view: view,
      layer: SketchLayer,
      creationMode: "update",
      availableCreateTools: ["polygon"],
      visibleElements: {
        createTools: { polygon: true },
        undoRedoMenu: true,
      },
      polygonSymbol: {
        type: "simple-fill",
        color: [255, 255, 255, 0.4],
        outline: {
          color: [255, 165, 0],
          width: 2,
        },
      },
    });

    setSketch(sketchViewModel);
    sketchViewModel.create("polygon");

    const createBufferPolygon = (point, bufferSize = 2) => {
      // Create a small square polygon around the point
      const x = point.x;
      const y = point.y;
      const half = bufferSize / 2;

      return new Polygon({
        rings: [
          [
            [x - half, y - half], // top left
            [x + half, y - half], // top right
            [x + half, y + half], // bottom right
            [x - half, y + half], // bottom left
            [x - half, y - half], // close the polygon
          ],
        ],
        spatialReference: point.spatialReference,
      });
    };

    // Function to calculate polygon area in square meters
    // const calculateArea = (geometry) => {
    //   // Check if the geometry has a built-in method for area calculation
    //   if (geometry.area) {
    //     return geometry.area;
    //   }

    //   // Manual calculation for polygon area if needed
    //   // This is a simplified version - for actual implementation,
    //   // use appropriate geodesic calculations based on spatial reference
    //   return Math.abs(
    //     geometry.rings[0].reduce((area, point, i, points) => {
    //       const nextPoint = points[(i + 1) % points.length];
    //       return area + (point[0] * nextPoint[1] - nextPoint[0] * point[1]);
    //     }, 0) / 2
    //   );
    // };

    // // Function to calculate length between two points in map units
    // const calculateDistance = (point1, point2) => {
    //   const dx = point2[0] - point1[0];
    //   const dy = point2[1] - point1[1];
    //   return Math.sqrt(dx * dx + dy * dy);
    // };

    // // Function to convert map point to geographic coordinates (latitude, longitude)
    // const toLatLong = async (point, spatialReference) => {
    //   try {
    //     // Use ArcGIS projection functions to convert to geographic coordinates
    //     // This requires importing additional modules like geometryEngine or projection

    //     // For demonstration, assuming webMercatorUtils is available
    //     if (
    //       window.webMercatorUtils &&
    //       window.webMercatorUtils.webMercatorToGeographic
    //     ) {
    //       const geoPoint = window.webMercatorUtils.webMercatorToGeographic({
    //         x: point[0],
    //         y: point[1],
    //         spatialReference: spatialReference,
    //       });
    //       return { lat: geoPoint.y.toFixed(6), long: geoPoint.x.toFixed(6) };
    //     }

    //     // Fallback to original coordinates if conversion utils aren't available
    //     return {
    //       x: point[0].toFixed(2),
    //       y: point[1].toFixed(2),
    //       spatialReference: spatialReference,
    //     };
    //   } catch (error) {
    //     console.error("Error converting to lat/long:", error);
    //     return { x: point[0].toFixed(2), y: point[1].toFixed(2) };
    //   }
    // };

    // // Add measurements and labels to the polygon
    // const addMeasurementsToPolygon = async (geometry) => {
    //   try {
    //     //  measurementsLayer.removeAll();

    //     if (!geometry || !geometry.rings || geometry.rings.length === 0) return;

    //     const rings = geometry.rings[0];
    //     const spatialReference = geometry.spatialReference;

    //     // Calculate and display edge lengths
    //     for (let i = 0; i < rings.length - 1; i++) {
    //       const point1 = rings[i];
    //       const point2 = rings[i + 1];

    //       // Calculate midpoint for placing the label
    //       const midPoint = [
    //         (point1[0] + point2[0]) / 2,
    //         (point1[1] + point2[1]) / 2,
    //       ];

    //       // Calculate distance between points
    //       const distance = calculateDistance(point1, point2);

    //       // Create text symbol for the distance
    //       const lengthLabel = new TextSymbol({
    //         color: "black",
    //         haloColor: "white",
    //         haloSize: 1,
    //         text: `${distance.toFixed(2)} م`,
    //         font: {
    //           size: 10,
    //           family: "sans-serif",
    //           weight: "bold",
    //         },
    //       });

    //       // Add length label at midpoint
    //       measurementsLayer.add(
    //         new Graphic({
    //           geometry: new Point({
    //             x: midPoint[0],
    //             y: midPoint[1],
    //             spatialReference: spatialReference,
    //           }),
    //           symbol: lengthLabel,
    //         })
    //       );
    //     }

    //     // Add corner coordinates (lat/long)
    //     for (let i = 0; i < rings.length - 1; i++) {
    //       const corner = rings[i];
    //       const coordinates = await toLatLong(corner, spatialReference);

    //       // Create marker for corner point
    //       const markerSymbol = new SimpleMarkerSymbol({
    //         style: "circle",
    //         color: "red",
    //         size: 6,
    //         outline: {
    //           color: "white",
    //           width: 1,
    //         },
    //       });

    //       // Add corner point
    //       measurementsLayer.add(
    //         new Graphic({
    //           geometry: new Point({
    //             x: corner[0],
    //             y: corner[1],
    //             spatialReference: spatialReference,
    //           }),
    //           symbol: markerSymbol,
    //         })
    //       );

    //       // Create text for coordinates
    //       const coordLabel = new TextSymbol({
    //         color: "blue",
    //         haloColor: "white",
    //         haloSize: 1,
    //         text: ` ${coordinates.x}, ${coordinates.y}`,
    //         font: {
    //           size: 8,
    //           family: "sans-serif",
    //         },
    //         yoffset: 15,
    //       });

    //       // Add coordinate label
    //       measurementsLayer.add(
    //         new Graphic({
    //           geometry: new Point({
    //             x: corner[0],
    //             y: corner[1],
    //             spatialReference: spatialReference,
    //           }),
    //           symbol: coordLabel,
    //         })
    //       );
    //     }

    //     // Calculate centroid (simplified)
    //     let sumX = 0,
    //       sumY = 0;
    //     for (let i = 0; i < rings.length - 1; i++) {
    //       sumX += rings[i][0];
    //       sumY += rings[i][1];
    //     }

    //     const centroidX = sumX / (rings.length - 1);
    //     const centroidY = sumY / (rings.length - 1);

    //     // Calculate area
    //     const area = calculateArea(geometry);

    //     // Create area label at centroid
    //     const areaLabel = new TextSymbol({
    //       color: "darkgreen",
    //       haloColor: "white",
    //       haloSize: 2,
    //       text: ` ${area.toFixed(2)} `,
    //       font: {
    //         size: 12,
    //         family: "sans-serif",
    //         weight: "bold",
    //       },
    //     });

    //     // Add area label at centroid
    //     measurementsLayer.add(
    //       new Graphic({
    //         geometry: new Point({
    //           x: centroidX,
    //           y: centroidY,
    //           spatialReference: spatialReference,
    //         }),
    //         symbol: areaLabel,
    //       })
    //     );
    //   } catch (error) {
    //     console.error("Error adding measurements:", error);
    //   }
    // };

    const handleQuery = (geometry) => {
      try {
        const queryObject = new Query({
          geometry,
          spatialRelationship: "intersects",
          outFields: ["*"],
          returnGeometry: true,
        });

        // Using the imported query module directly
        return query
          .executeQueryJSON(
            `${window.mapUrl}/${getLayerId(map.__mapInfo, "Landbase_Parcel")}`,
            queryObject
          )
          .then((result) => {
            if (result.features?.length > 0) {
              return getFeatureDomainName(
                result.features,
                getLayerId(map.__mapInfo, "Landbase_Parcel")
              ).then((rfeatures) => {
                const highlightSymbol = new SimpleFillSymbol({
                  color: [0, 0, 0, 0],
                  outline: new SimpleLineSymbol({
                    color: [0, 0, 0],
                    width: 3,
                  }),
                });

                graphicsLayer.removeAll();

                result.features.forEach((land) => {
                  const highlightGraphic = new Graphic({
                    geometry: land.geometry,
                    symbol: highlightSymbol,
                  });
                  graphicsLayer.add(highlightGraphic);

                  // Add measurements to the selected land parcel
                  // addMeasurementsToPolygon(land.geometry);
                });

                return rfeatures;
              });
            } else {
              console.log("No intersecting lands found.");
              measurementsLayer.removeAll();
              return [];
            }
          })
          .catch((error) => {
            console.error("Error executing query:", error);
            throw error;
          });
      } catch (error) {
        console.error("Error in query setup:", error);
        throw error;
      }
    };

    sketchViewModel.on("create", (event) => {
      if (event.state === "start") {
        try {
          // Create a buffer polygon around the initial point
          const point = new Point({
            x: event.graphic.geometry.rings[0][0][0],
            y: event.graphic.geometry.rings[0][0][1],
            spatialReference: event.graphic.geometry.spatialReference,
          });

          const bufferPolygon = createBufferPolygon(point);
          handleQuery(bufferPolygon)
            .then((rfeatures) => {
              console.log("Start point query result:", rfeatures);
              if (onResult) onResult(rfeatures);
            })
            .catch((error) => {
              console.error("Error during start point query:", error);
            });
        } catch (error) {
          console.error("Error creating buffer polygon:", error);
        }
      } else if (
        event.state === "complete" ||
        event.toolEventInfo?.type === "vertex-add"
      ) {
        try {
          handleQuery(event.graphic.geometry)
            .then((rfeatures) => {
              console.log("Create result:", rfeatures);
              if (onResult) onResult(rfeatures);

              // Also add measurements to the sketch polygon itself
              //     addMeasurementsToPolygon(event.graphic.geometry);
            })
            .catch((error) => {
              console.error("Error during create:", error);
            });
        } catch (error) {
          console.error("Error during create setup:", error);
        }
      }
    });

    sketchViewModel.on("update", (event) => {
      if (event.state === "complete" && !event.aborted) {
        try {
          handleQuery(event.graphics[0].geometry)
            .then((rfeatures) => {
              console.log("Update result:", rfeatures);
              if (onResult) onResult(rfeatures);
            })
            .catch((error) => {
              console.error("Error during update:", error);
            });
        } catch (error) {
          console.error("Error during update setup:", error);
        }
      }
    });
  };
  const enablePolygonSelectionModeWithoutCallback = (
    map,
    onResult,
    setLandsAreas
  ) => {
    if (!map) {
      console.error("Map is undefined");
      return;
    }

    const graphicsLayer = map.findLayerById("highlightGraphicLayer");

    if (!graphicsLayer) {
      console.error("Graphics layer not found!");
      return;
    }

    let measurementsLayer = map.findLayerById("MeasurementsLayer");
    if (!measurementsLayer) {
      measurementsLayer = new GraphicsLayer({ id: "MeasurementsLayer" });
      map.add(measurementsLayer);
    }

    let SketchLayer = map.findLayerById("SelectGraphicLayer");
    console.log("SketchLayer", SketchLayer);

    if (!SketchLayer) {
      SketchLayer = new GraphicsLayer({ id: "SelectGraphicLayer" });
      map.add(SketchLayer);
    }

    const view = window.__view || map.view;
    if (!view) {
      console.error("View is undefined");
      return;
    }

    const sketchViewModel = new SketchViewModel({
      view: view,
      layer: SketchLayer,
      creationMode: "update",
      availableCreateTools: ["polygon"],
      visibleElements: {
        createTools: { polygon: true },
        undoRedoMenu: true,
      },
      polygonSymbol: {
        type: "simple-fill",
        color: [255, 255, 255, 0.4],
        outline: {
          color: [255, 165, 0],
          width: 2,
        },
      },
    });

    setSketch(sketchViewModel);
    sketchViewModel.create("polygon");

    const createBufferPolygon = (point, bufferSize = 2) => {
      const x = point.x;
      const y = point.y;
      const half = bufferSize / 2;

      return new Polygon({
        rings: [
          [
            [x - half, y - half],
            [x + half, y - half],
            [x + half, y + half],
            [x - half, y + half],
            [x - half, y - half],
          ],
        ],
        spatialReference: point.spatialReference,
      });
    };

    const calculateArea = (geometry) => {
      if (geometry.area) {
        return geometry.area;
      }
      return Math.abs(
        geometry.rings[0].reduce((area, point, i, points) => {
          const nextPoint = points[(i + 1) % points.length];
          return area + (point[0] * nextPoint[1] - nextPoint[0] * point[1]);
        }, 0) / 2
      );
    };

    const calculateDistance = (point1, point2) => {
      const dx = point2[0] - point1[0];
      const dy = point2[1] - point1[1];
      return Math.sqrt(dx * dx + dy * dy);
    };

    const toLatLong = async (point, spatialReference) => {
      try {
        if (
          window.webMercatorUtils &&
          window.webMercatorUtils.webMercatorToGeographic
        ) {
          const geoPoint = window.webMercatorUtils.webMercatorToGeographic({
            x: point[0],
            y: point[1],
            spatialReference: spatialReference,
          });
          return { lat: geoPoint.y.toFixed(6), long: geoPoint.x.toFixed(6) };
        }
        return {
          x: point[0].toFixed(2),
          y: point[1].toFixed(2),
          spatialReference: spatialReference,
        };
      } catch (error) {
        console.error("Error converting to lat/long:", error);
        return { x: point[0].toFixed(2), y: point[1].toFixed(2) };
      }
    };

    const addMeasurementsToPolygon = async (geometry) => {
      try {
        if (!geometry || !geometry.rings || geometry.rings.length === 0) return;

        const rings = geometry.rings[0];
        const spatialReference = geometry.spatialReference;

        for (let i = 0; i < rings.length - 1; i++) {
          const point1 = rings[i];
          const point2 = rings[i + 1];

          const midPoint = [
            (point1[0] + point2[0]) / 2,
            (point1[1] + point2[1]) / 2,
          ];

          const distance = calculateDistance(point1, point2);

          const lengthLabel = new TextSymbol({
            color: "black",
            haloColor: "white",
            haloSize: 1,
            text: `${distance.toFixed(2)} م`,
            font: {
              size: 10,
              family: "sans-serif",
              weight: "bold",
            },
          });

          measurementsLayer.add(
            new Graphic({
              geometry: new Point({
                x: midPoint[0],
                y: midPoint[1],
                spatialReference: spatialReference,
              }),
              symbol: lengthLabel,
            })
          );
        }

        for (let i = 0; i < rings.length - 1; i++) {
          const corner = rings[i];
          const coordinates = await toLatLong(corner, spatialReference);

          const markerSymbol = new SimpleMarkerSymbol({
            style: "circle",
            color: "red",
            size: 6,
            outline: {
              color: "white",
              width: 1,
            },
          });

          measurementsLayer.add(
            new Graphic({
              geometry: new Point({
                x: corner[0],
                y: corner[1],
                spatialReference: spatialReference,
              }),
              symbol: markerSymbol,
            })
          );

          const coordLabel = new TextSymbol({
            color: "blue",
            haloColor: "white",
            haloSize: 1,
            text: `${coordinates.x}, ${coordinates.y}`,
            font: {
              size: 8,
              family: "sans-serif",
            },
            yoffset: 15,
          });

          measurementsLayer.add(
            new Graphic({
              geometry: new Point({
                x: corner[0],
                y: corner[1],
                spatialReference: spatialReference,
              }),
              symbol: coordLabel,
            })
          );
        }

        let sumX = 0,
          sumY = 0;
        for (let i = 0; i < rings.length - 1; i++) {
          sumX += rings[i][0];
          sumY += rings[i][1];
        }

        const centroidX = sumX / (rings.length - 1);
        const centroidY = sumY / (rings.length - 1);

        const area = calculateArea(geometry);

        const areaLabel = new TextSymbol({
          color: "darkgreen",
          haloColor: "white",
          haloSize: 2,
          text: `${area.toFixed(2)}`,
          font: {
            size: 12,
            family: "sans-serif",
            weight: "bold",
          },
          yoffset: 15,
        });

        measurementsLayer.add(
          new Graphic({
            geometry: new Point({
              x: centroidX,
              y: centroidY,
              spatialReference: spatialReference,
            }),
            symbol: areaLabel,
          })
        );
      } catch (error) {
        console.error("Error adding measurements:", error);
      }
    };

    const handleQuery = (geometry) => {
      try {
        const queryObject = new Query({
          geometry,
          spatialRelationship: "intersects",
          outFields: ["*"],
          returnGeometry: true,
        });

        return query
          .executeQueryJSON(
            `${window.mapUrl}/${getLayerId(map.__mapInfo, "Landbase_Parcel")}`,
            queryObject
          )
          .then((result) => {
            if (result.features?.length > 0) {
              return getFeatureDomainName(
                result.features,
                getLayerId(map.__mapInfo, "Landbase_Parcel")
              ).then((rfeatures) => {
                const highlightSymbol = new SimpleFillSymbol({
                  color: [0, 0, 0, 0],
                  outline: new SimpleLineSymbol({
                    color: [0, 0, 0],
                    width: 3,
                  }),
                });

                graphicsLayer.removeAll();

                let totalArea = 0;
                const landInfo = result.features.map((land) => {
                  const area = calculateArea(land.geometry);
                  totalArea += area;
                  return {
                    PARCEL_PLAN_NO:
                      land.attributes?.PARCEL_PLAN_NO || "Unknown",
                    area: area.toFixed(2),
                  };
                });

                result.features.forEach((land) => {
                  const highlightGraphic = new Graphic({
                    geometry: land.geometry,
                    symbol: highlightSymbol,
                  });
                  graphicsLayer.add(highlightGraphic);
                  addMeasurementsToPolygon(land.geometry);
                });

                const state = {
                  lands: landInfo,
                  totalArea: totalArea.toFixed(2),
                };

                console.log("Updating landsAreas in handleQuery:", state);
                if (setLandsAreas) {
                  // setLandsAreas(state);
                  return state;
                }

                return { features: rfeatures, state };
              });
            } else {
              console.log("No intersecting lands found.");
              measurementsLayer.removeAll();
              const state = { lands: [], totalArea: 0 };
              console.log("Updating landsAreas (empty) in handleQuery:", state);
              if (setLandsAreas) {
                //  setLandsAreas(state);
                return state;
              }
              return { features: [], state };
            }
          })
          .catch((error) => {
            console.error("Error executing query:", error);
            throw error;
          });
      } catch (error) {
        console.error("Error in query setup:", error);
        throw error;
      }
    };

    sketchViewModel.on("create", (event) => {
      if (event.state === "start") {
        try {
          const point = new Point({
            x: event.graphic.geometry.rings[0][0][0],
            y: event.graphic.geometry.rings[0][0][1],
            spatialReference: event.graphic.geometry.spatialReference,
          });

          const bufferPolygon = createBufferPolygon(point);
          handleQuery(bufferPolygon)
            .then(({ features, state }) => {
              console.log("Start point query result:", { features, state });
              if (onResult) onResult({ features, state });
            })
            .catch((error) => {
              console.error("Error during start point query:", error);
            });
        } catch (error) {
          console.error("Error creating buffer polygon:", error);
        }
      } else if (
        event.state === "complete" ||
        event.toolEventInfo?.type === "vertex-add"
      ) {
        try {
          handleQuery(event.graphic.geometry)
            .then(({ features, state }) => {
              console.log("Create result:", { features, state });
              if (onResult) onResult({ features, state });
            })
            .catch((error) => {
              console.error("Error during create:", error);
            });
        } catch (error) {
          console.error("Error during create setup:", error);
        }
      }
    });

    sketchViewModel.on("update", (event) => {
      if (event.state === "complete" && !event.aborted) {
        try {
          handleQuery(event.graphics[0].geometry)
            .then(({ features, state }) => {
              console.log("Update result:", { features, state });
              if (onResult) onResult({ features, state });
            })
            .catch((error) => {
              console.error("Error during update:", error);
            });
        } catch (error) {
          console.error("Error during update setup:", error);
        }
      }
    });
  };
  const Cleanup = () => {
    if (sketch) {
      sketch.destroy();
      setSketch(null);
      setSelectedFeatures([]);
      setLandsAreas({ lands: [], totalArea: 0 });
    }

    // Clean up measurements layer when component unmounts or tool changes
    if (props.map) {
      const measurementsLayer = props.map.findLayerById("MeasurementsLayer");
      const SketchLayer = props.map.findLayerById("SelectGraphicLayer");
      const graphicsLayer = props.map.findLayerById("highlightGraphicLayer");
      if (measurementsLayer) {
        measurementsLayer.removeAll();
      }
      if (SketchLayer) {
        SketchLayer.removeAll();
      }
      if (graphicsLayer) {
        graphicsLayer.removeAll();
      }
    }
  };
  const extentChangeHandler = (evt) => {
    if (window.__prevExtent || window.__nextExtent) {
      window.__currentExtent = evt;
    } else {
      window.__preExtent = window.__currentExtent;
      window.__currentExtent = evt;
      window.__extentHistory = window.__extentHistory || [];
      window.__extentHistory.push({
        preExtent: window.__preExtent,
        currentExtent: window.__currentExtent,
      });
      window.__extentHistoryIndx = window.__extentHistory.length - 1;
    }
    window.__prevExtent = window.__nextExtent = false;
    //console.log('extent--------',_extentHistory);
    //extentHistoryChange();
  };

  useEffect(() => {
    watchUtils.whenTrue(props.map.view, "ready", () => {
      window.__fullExtent = props.map.view.extent.clone();
      window.__draw = new Draw({
        view: props.map.view,
      });
      watchUtils.whenOnce(props.map.view, "extent", () => {
        watchUtils.when(props.map.view, "stationary", (evt) => {
          if (evt) {
            extentChangeHandler(props.map.view.extent);
          }
        });
      });
    });
  }, []);

  useEffect(() => {
    if (props?.mainData) {
      let mainFunctions = props?.mainData?.mainFunctions || [];
      let reqMainFuncIDs = [];

      if (mainFunctions.length) {
        for (let index = 0; index < mainFunctions.length; index++) {
          let item = mainFunctions[index];
          let moduleIds = item.groups_permissions.map((i) => i.module_id);
          reqMainFuncIDs = [...reqMainFuncIDs, ...moduleIds];
        }
      }

      let privilegeTools = [
        {
          id: 1,
          label: "mapToolsServices.select",
          name: "select",
          icon: update_img,
          to: "",
          module_id: modulesIDs.updatingRequests,
          onClick: () => {
            if (sketch) {
              sketch.destroy();
              setSketch(null);
              Cleanup();
            }

            enablePolygonSelectionMode(props.map, (rfeatures) => {
              // Handle the results here
              setSelectedFeatures(rfeatures);
              console.log("Selected features:", rfeatures);
            });
          },
        },
      ];

      let filteredIcons = privilegeTools.filter((sL) =>
        reqMainFuncIDs.includes(sL.module_id)
      );

      let icons = [...filteredIcons].sort((a, b) => (a.id < b.id ? -1 : 1));

      setPrivilegeIcons(icons);
    }
  }, []);

  const openServiceSearch = (e) => {
    setServSearch(true);
    setActiveService(e.id);
    setActiveServiceItem(e);
    setToolData(false);
    setActiveTool("");
  };

  const closeServiceSearch = () => {
    setServSearch(false);
    setActiveService(0);
  };

  const removeAllGraphicsOnMap = () => {
    props.map.view.graphics.removeAll();

    props.map.layers.items.forEach((layer) => {
      clearGraphicLayer(layer.id, props.map);
    });

    if (sketch) {
      sketch.destroy();
      Cleanup();
      setSketch(null);
    }
  };

  const toggleFullScreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch((err) => {
        console.error(
          `Error attempting to enable full-screen mode: ${err.message}`
        );
      });
    } else {
      document.exitFullscreen();
    }
  };

  const removeCurrentSelTool = () => {
    props.map.view.popup.close();
  };

  window.unSubscribeMapEvents = () => {
    // if (window.__evtViewDragHandler) {
    //   window.__evtViewDragHandler.remove();
    //   window.__evtViewDragHandler = null;
    // }
    // if (window.__evtViewKeyDownHandler) {
    //   window.__evtViewKeyDownHandler.remove();
    //   window.__evtViewKeyDownHandler = null;
    // }
    if (window.handles.length) {
      window.handles.forEach((handler) => {
        //    debugger;
        handler.remove();
        handler = null;
      });
      window.handles = [];
    }
  };

  const displayDefaultCursor = () => {
    props.map.view &&
      props.map.view.container &&
      props.map.view.container.style &&
      "default" !== props.map.view.container.style.cursor &&
      (props.map.view.container.style.cursor = "default");
  };

  const disableActiveTool = () => {
    //    debugger;
    // you should put a condition here to disable the active tool
    setActiveTool("dis");
    removeCurrentSelTool();

    window.unSubscribeMapEvents();
    displayDefaultCursor();
    if (!window.__draw) {
      window.__draw = new Draw({
        view: props.map.view,
      });
    }
    window.__draw.reset();
  };

  window.DisableActiveTool = () => {
    disableActiveTool();
  };

  const disableViewPanning = () => {
    // if (window.__evtViewDragHandler) {
    //   window.__evtViewDragHandler.remove();
    //   window.__evtViewDragHandler = null;
    // }
    // if (window.__evtViewKeyDownHandler) {
    //   window.__evtViewKeyDownHandler.remove();
    //   window.__evtViewKeyDownHandler = null;
    // }
    window.unSubscribeMapEvents();
    window.handles.push(
      props.map.view.on("drag", (event) => {
        // prevents panning with the mouse drag event
        if (activeTool != "dis") event.stopPropagation();
      })
    );

    window.handles.push(
      props.map.view.on("key-down", (event) => {
        // prevents panning with the arrow keys
        var keyPressed = event.key;
        if (keyPressed.slice(0, 5) === "Arrow") {
          if (activeTool != "dis") event.stopPropagation();
        }
      })
    );
  };

  const displayCrosshairCursor = () => {
    props.map.view.container.style.cursor = "crosshair";
  };

  const activeZoomIn = (e) => {
    handleClick();
    if (true) {
      setActiveTool("zoomIn");
      removeCurrentSelTool();
      disableViewPanning();
      props.map.view.graphics.removeAll();
      if (!window.__draw) {
        window.__draw = new Draw({
          view: props.map.view,
        });
      }
      var action = window.__draw.create("rectangle");
      displayCrosshairCursor();
      //props.map.view.focus();
      action.on("vertex-add", drawRect);
      action.on("draw-complete", zoomIn);
      action.on("cursor-update", drawRect);
    } else {
      var zoom = new Zoom({
        view: props.map.view,
        visible: false,
      });

      zoom.zoomIn();
    }
  };

  const getExtentfromVertices = (vertices) => {
    var sx = vertices[0][0],
      sy = vertices[0][1];
    var ex = vertices[1][0],
      ey = vertices[1][1];
    var rect = {
      x: Math.min(sx, ex),
      y: Math.max(sy, ey),
      width: Math.abs(sx - ex),
      height: Math.abs(sy - ey),
      spatialReference: props.map.view.spatialReference,
    };
    if (rect.width !== 0 || rect.height !== 0) {
      return new Extent({
        xmin: parseFloat(rect.x),
        ymin: parseFloat(rect.y) - parseFloat(rect.height),
        xmax: parseFloat(rect.x) + parseFloat(rect.width),
        ymax: parseFloat(rect.y),
        spatialReference: rect.spatialReference,
      });
    } else {
      return null;
    }
  };

  const drawRect = (event) => {
    var vertices = event.vertices;
    //remove existing graphic
    props.map.view.graphics.removeAll();
    if (vertices.length < 2) {
      return;
    }

    // create a new extent
    var extent = getExtentfromVertices(vertices);

    var graphic = new Graphic({
      geometry: extent,
      symbol: {
        type: "simple-fill", // autocasts as SimpleFillSymbol
        color: [0, 0, 0, 0.3],
        style: "solid",
        outline: {
          // autocasts as SimpleLineSymbol
          color: [255, 0, 0],
          width: 1,
        },
      },
    });

    props.map.view.graphics.add(graphic);
  };

  const goToFullExtent = () => {
    setActiveTool("fullMap");
    props.map.view.goTo(window.__fullExtent);
    if (props.setIndicatorFullExtent) {
      props.setIndicatorFullExtent();
    }
  };

  function zoomOut(evt) {
    var vertices = evt.vertices;
    if (!window.__draw) {
      window.__draw = new Draw({
        view: props.map.view,
      });
    }
    window.__draw.reset();
    props.map.view.graphics.removeAll();
    var action = window.__draw.create("rectangle");
    //view.focus();
    action.on("vertex-add", drawRect);
    action.on("draw-complete", zoomOut);
    action.on("cursor-update", drawRect);
    if (evt.vertices.length === 1) {
      props.map.view.goTo({ scale: props.map.view.scale * 2 });
      return;
    }
    var sx = vertices[0][0],
      sy = vertices[0][1];
    var ex = vertices[1][0],
      ey = vertices[1][1];
    var rect = {
      x: Math.min(sx, ex),
      y: Math.max(sy, ey),
      width: Math.abs(sx - ex),
      height: Math.abs(sy - ey),
      spatialReference: props.map.view.spatialReference,
    };
    if (rect.width !== 0 || rect.height !== 0) {
      var scrPnt1 = props.map.view.toScreen(rect);
      var scrPnt2 = props.map.view.toScreen({
        x: rect.x + rect.width,
        y: rect.y,
        spatialReference: rect.spatialReference,
      });
      var mWidth = props.map.view.extent.width;
      var delta =
        ((mWidth * props.map.view.width) / Math.abs(scrPnt2.x - scrPnt1.x) -
          mWidth) /
        2;
      var vExtent = props.map.view.extent;
      props.map.view.goTo(
        new Extent({
          xmin: vExtent.xmin - delta,
          ymin: vExtent.ymin - delta,
          xmax: vExtent.xmax + delta,
          ymax: vExtent.ymax + delta,
          spatialReference: vExtent.spatialReference,
        })
      );
    }
  }

  const zoomIn = (evt) => {
    if (!window.__draw) {
      window.__draw = new Draw({
        view: props.map.view,
      });
    }
    window.__draw.reset();
    props.map.view.graphics.removeAll();
    var action = window.__draw.create("rectangle");
    //props.map.view.focus();
    action.on("vertex-add", drawRect);
    action.on("draw-complete", zoomIn);
    action.on("cursor-update", drawRect);
    if (evt.vertices.length === 1) {
      props.map.view.goTo({ scale: props.map.view.scale * 0.5 });
      return;
    }
    var extent = getExtentfromVertices(evt.vertices);
    if (extent?.width !== 0 || extent?.height !== 0) {
      props.map.view.goTo(extent);
    }
  };
  const activeZoomOut = () => {
    handleClick();
    if (true) {
      setActiveTool("zoomOut");
      removeCurrentSelTool();
      disableViewPanning();
      props.map.view.graphics.removeAll();
      if (!window.__draw) {
        window.__draw = new Draw({
          view: props.map.view,
        });
      }
      var action = window.__draw.create("rectangle");
      displayCrosshairCursor();
      //view.focus();
      action.on("vertex-add", drawRect);
      action.on("draw-complete", zoomOut);
      action.on("cursor-update", drawRect);
    } else {
      var zoom = new Zoom({
        view: props.map.view,
        visible: false,
      });

      zoom.zoomOut();
    }
  };

  const deactivateZoomOut = () => {
    // setActiveTool(null);
    removeCurrentSelTool();
    window.unSubscribeMapEvents();
    window.__draw.reset();
    props.map.view.container.style.cursor = "default";
  };

  const goToPreviousExtent = () => {
    setActiveTool("prev");
    if (window.__extentHistory[window.__extentHistoryIndx].preExtent) {
      window.__prevExtent = true;
      if (window.__extentHistoryIndx > 0) {
        props.map.view.goTo(
          window.__extentHistory[window.__extentHistoryIndx].preExtent
        );
        window.__extentHistoryIndx--;
      }
    }
  };

  const goToNextExtent = () => {
    setActiveTool("next");
    window.__nextExtent = true;
    if (window.__extentHistory.length > window.__extentHistoryIndx + 1) {
      window.__extentHistoryIndx++;
      props.map.view.goTo(
        window.__extentHistory[window.__extentHistoryIndx].currentExtent
      );
    }
  };

  const openToolsData = (e, name) => {
    //setClearMapEvents(true);
    if (name == "screenshot") {
      //  //    debugger;
      async function convertIconsToImages() {
        const icons = document.querySelectorAll(".fas");
        //   //    debugger;
        for (const icon of icons) {
          const rect = icon.getBoundingClientRect();
          const canvas = document.createElement("canvas");
          canvas.width = rect.width;
          canvas.height = rect.height;

          const ctx = canvas.getContext("2d");

          // Draw the icon onto the canvas
          await html2canvas(icon, {
            backgroundColor: null,
          }).then((iconCanvas) => {
            ctx.drawImage(iconCanvas, 0, 0);
          });

          // Replace the icon with the canvas
          const img = document.createElement("img");
          img.src = canvas.toDataURL();
          img.style.width = `${rect.width}px`;
          img.style.height = `${rect.height}px`;
          icon.parentNode.replaceChild(img, icon);
        }
      }
      const style = document.createElement("style");
      style.innerHTML = `
  * {
    scrollbar-width: none !important;      /* Firefox */
    -ms-overflow-style: none !important;   /* IE 10+ */
  }
  *::-webkit-scrollbar {
    display: none !important;              /* Chrome, Safari, Opera */
  }
`;

      // Append the style to the head
      document.head.appendChild(style);
      let tooltip = document.querySelector('[role="tooltip"]');
      if (tooltip) {
        tooltip.style.visibility = "hidden";
      }
      props.map.view.takeScreenshot().then(async (screenshot) => {
        const mapImg = new Image();
        mapImg.src = screenshot.dataUrl;
        mapImg.style.position = "absolute";
        mapImg.style.top = "0";
        mapImg.style.left = "0";
        mapImg.style.zIndex = "0.5"; // Place it behind the UI

        // Temporarily replace the map canvas with the image
        const mapContainer = document.querySelector("#mapDiv");
        mapContainer.appendChild(mapImg);
        // const arcgisStyles = Array.from(document.styleSheets).filter(
        //   (sheet) => sheet.href && sheet.href.includes("@arcgis")
        // );

        // // Temporarily disable them
        // arcgisStyles.forEach((sheet) => {
        //   sheet.disabled = true;
        // });

        //await convertIconsToImages();
        toJpeg(document.querySelector("body"), {
          cacheBust: true,
          // skipFonts: false,
          quality: 1,
          style: {
            direction: "rtl",
            textAlign: "right",
          },
          filter: (node) => {
            return node.id != "landDataIconsModal";
          },
        })
          .then((dataUrl) => {
            // arcgisStyles.forEach((sheet) => {
            //   sheet.disabled = false;
            // });

            const link = document.createElement("a");
            link.download = "المستكشف الجغرافي.jpeg";
            link.href = dataUrl;
            link.click();
            document.head.removeChild(style);
            mapContainer.removeChild(mapImg);
          })
          .catch((err) => {
            //    debugger;
            console.error(err);
            document.head.removeChild(style);
            mapContainer.removeChild(mapImg);
          });
      });
    } else {
      window.DisableActiveTool();
      setServSearch(false);
      sketch?.destroy();
      setActiveService(0);
      setToolData(true);
      e !== undefined && e.target !== undefined
        ? setActiveTool(name)
        : setActiveTool("");
      //deactivateZoomOut();
      handleStopDrawing();
      console.log(e);
      // const customEvent = new CustomEvent("showPrintBox", {
      //   detail: { show: false },
      // });
      // document.dispatchEvent(customEvent);

      // Remove the special case for compareLayers to allow normal modal flow

      //   if (name !== "zoomIn" && name !== "zoomOut") {
      //     console.log("deactivateZoomOut");

      //     deactivateZoomOut();
      // }
      if (name == "next") {
        //  deactivateZoomOut();
        goToNextExtent();
      } else if (name == "prev") {
        //  deactivateZoomOut();
        goToPreviousExtent();
      } else if (name == "zoomIn") {
        activeZoomIn();
      } else if (name == "zoomOut") {
        activeZoomOut();
      } else if (name == "fullMap") {
        //  deactivateZoomOut();
        goToFullExtent();
      } else if (name == "traffic") {
        // deactivateZoomOut();
        if (!props.map.findLayerById("trafficLayerId")) {
          let layer = new CustomTileLayer({
            urlTemplate: window.trafficUrl,
            id: "trafficLayerId",
          });

          props.map.layers.add(layer);
        } else {
          props.map.remove(props.map.findLayerById("trafficLayerId"));
        }
      } else if (name === "select" && props.map) {
        // deactivateZoomOut();
        if (sketch) {
          sketch.destroy();
          Cleanup();
          setSketch(null);
        }

        enablePolygonSelectionModeWithoutCallback(props.map, (state) => {
          setLandsAreas(state);
        });
      } else if (name == "inquiry") {
        handleClick();
      }
    }
  };
  const [openToolservice, setToolService] = useState(true);
  const openToolsMenu = () => {
    if (openToolservice) {
      setToolService(false);
      setToolData(false);
      setActiveTool("");
      setActiveService(0);
      setServSearch(false);
    } else {
      setToolService(true);
      setToolData(false);
      setActiveTool("");
      setActiveService(0);
      setServSearch(false);
    }
  };
  const closeToolsData = (e) => {
    setToolData(false);
    setActiveTool("");
  };
  /*   const [tools] = useState([
    {
      id: 2,
      icon: inquiry,
      name: "inquiry",
      tooltip: "mapToolsServices.inquiry",
    },
    {
      id: 5,
      icon: layersMenu,
      name: "layersMenu",
      tooltip: "mapToolsServices.layersMenu",
    },
    {
      id: 3,
      icon: googleMaps,
      name: "googleMaps",
      tooltip: "mapToolsServices.googleMaps",
      className: "googleMapToolClass",
    },
    {
      id: 4,
      icon: smallMap,
      name: "smallMap",
      tooltip: "mapToolsServices.smallMap",
    },

    {
      id: 7,
      icon: traffic,
      name: "traffic",
      tooltip: "mapToolsServices.traffic",
    },

    //=============================================================================  button print Left
    {
      id: 6,
      icon: print,
      name: "print",
      tooltip: "mapToolsServices.print",
    },
  ]); */
  const [tools] = useState([
    {
      id: 2,
      icon: inquiry_img,
      name: "inquiry",
      tooltip: "mapToolsServices.inquiry",
    },
    {
      id: 5,
      icon: layers_menu_img,
      name: "layersMenu",
      tooltip: "mapToolsServices.layersMenu",
    },
    {
      id: 3,
      icon: google_maps_img,
      name: "googleMaps",
      tooltip: "mapToolsServices.googleMaps",
      className: "googleMapToolClass",
    },
    {
      id: 4,
      icon: small_map_img,
      name: "smallMap",
      tooltip: "mapToolsServices.smallMap",
    },

    {
      id: 7,
      icon: traffic_img,
      name: "traffic",
      tooltip: "mapToolsServices.traffic",
    },

    //=============================================================================  button print Left
    {
      id: 6,
      icon: screenshot_img,
      name: "screenshot",
      tooltip: "mapToolsServices.screenshot",
    },
    {
      id: 8,
      icon: full_map_img,
      name: "fullMap",
      tooltip: "mapTools.fullMap",
    },
    {
      id: 9,
      icon: zoom_in_img,
      name: "zoomIn",
      tooltip: "mapTools.zoomIn",
    },
    {
      id: 10,
      icon: zoom_out_img,
      name: "zoomOut",
      tooltip: "mapTools.zoomOut",
    },
    {
      id: 11,
      icon: next_img,
      name: "next",
      tooltip: "mapTools.next",
    },
    {
      id: 12,
      icon: prev_img,
      name: "prev",
      tooltip: "mapTools.prev",
    },
    {
      id: 13,
      icon: print_img,
      name: "print",
      tooltip: "mapToolsServices.print",
      // fallbackIcon: FaPrint,
    },
    // {
    //   id: 14,
    //   icon: FaCameraRetro,
    //   name: "screenshot",
    //   tooltip: "mapToolsServices.screenshot",
    // },
    // {
    //   //=========================
    //   id: 14,
    //   name: "compareLayers",
    //   tooltip: "mapToolsServices.compareLayers",
    //   icon: compare_layer_img,
    // },
    // <Tooltip
    //         title={t("mapToolsServices.compareLayers")}
    //         placement="top"
    //       >
    //         <li
    //           className="serviceLi "
    //           onClick={(event) => openToolsData(event, "compareLayers")}
    //           name="compareLayers"
    //         >
    //           <img src={compare_layer_img} alt="" />
    //         </li>
    //       </Tooltip>
    // move icon
    {
      id: 15,
      name: "move",
      tooltip: "mapTools.move",
      icon: move_img,
    },
    {
      id: 15, //========================================== legend
      icon: map_key_img,
      name: "legend",
      tooltip: "mapToolsServices.legend",
    },
    {
      id: 16,
      icon: full_screen_img,
      name: "select",
      tooltip: "mapToolsServices.selectWithCoordinates",
      // fallbackIcon: RiFullscreenFill,
    },
    // {
    //   id: 17,
    //   icon: traffic_img,
    //   name: "selectToUpdate",
    //   tooltip: "mapToolsServices.selectToUpdate",
    //   fallbackIcon: RiFullscreenFill,
    // },
  ]);

  // const [moreservices] = useState([
  //   {
  //     id: 4,
  //     icon: setting,
  //     tooltip: "mapToolsServices.maintenance",
  //     where: " SRVC_SUBTYPE = '10015' ",
  //   },
  //   {
  //     id: 5,
  //     icon: hospital,
  //     tooltip: "mapToolsServices.hospitals",
  //     where: " SRVC_TYPE = '700' ",
  //   },
  //   {
  //     id: 6,
  //     icon: foodcart,
  //     tooltip: "mapToolsServices.catering",
  //     where: " SRVC_SUBTYPE = '10005' ",
  //   },
  // ]);
  // const [openMoreSer, setOpenMoreSer] = useState(false);
  // const openMoreServices = () => {
  //   setOpenMoreSer(true);
  // };
  // const closeMoreServices = () => {
  //   setOpenMoreSer(false);
  // };
  //=================================================================

  const openHelp = (e) => {
    e.preventDefault();
    setServSearch(false);
    setActiveService(0);
    setToolData(true);
    e !== undefined && e.target !== undefined
      ? setActiveTool(e.target.name)
      : setActiveTool("");
    localStorage.removeItem("showHelp");
    localStorage.removeItem("showMetaHelp");
    localStorage.removeItem("showOpenSideHelp");
    localStorage.removeItem("showCardsResultHelp");
    localStorage.removeItem("showCardDetailsHelp");
    setTimeout(() => {
      props.setHelpShow(false);
      props.setHelpShow(true);
    }, 1);
    props.setHelpShow(false);
    console.log("zzz");
  };

  return (
    <div>
      <Tooltip
        title={
          openToolservice ? t("mapTools.CloseTools") : t("mapTools.OpenTools")
        }
        placement="top"
      >
        <div
          className="leftIconMenu openCloseToolServHelp"
          onClick={openToolsMenu}
        >
          {/* <img src={leftIcon} alt="" /> */}
          {/* <FaBagShopping className="hashTest" /> */}
          <img
            src={collapse_img}
            alt=""
            style={{ width: "100%", height: "100%", filter: "unset" }}
          />
        </div>
      </Tooltip>

      <Fade left delay={500}>
        <div
          className={
            openToolservice
              ? "openedservicesMenu servicesHelp"
              : "closedservicesMenu servicesHelp"
          }
        >
          <ul>
            {/* {openMoreSer ? (
              <Tooltip title={t("mapToolsServices.lessServ")} placement="top">
                <li onClick={closeMoreServices} className="moreLessIcon">
                  <img
                    src={less}
                    style={{ transform: "rotate(180deg" }}
                    alt="lessServices"
                  />
                </li>
              </Tooltip>
            ) : (
              <Tooltip title={t("mapToolsServices.moreServ")} placement="top">
                <li onClick={openMoreServices} className="moreLessIcon">
                  <img src={more} alt="moreServices" />
                </li>
              </Tooltip>
            )}
            {openMoreSer
              ? moreservices.map((s, index) => (
                  <Tooltip title={t(s.tooltip)} placement="top" key={index}>
                    <li
                      id={s.id}
                      onClick={() => openServiceSearch(s)}
                      className={
                        Number(activeService) === Number(s.id)
                          ? "activeService"
                          : "serviceLi"
                      }
                    >
                      <img src={s.icon} alt="servicesIcon" id={s.id} />
                    </li>
                  </Tooltip>
                ))
              : null} */}{" "}
            {/* <Tooltip
              title={t("mapToolsServices.compareLayers")}
              placement="top"
            >
              <li
                name="compareLayers"
                onClick={openToolsData}
                className="serviceLi"
              >
                <img
                  src={compareLayers}
                  name="compareLayers"
                  className="openedservicesMenuImg"
                  alt="servicesIcon"
                />
              </li>
            </Tooltip> */}
            {/* <Tooltip title={t("mapTools.fullScreen")} placement="top">
              <li
                className="fullscreenServHelp serviceLi"
                onClick={props.handle.enter}
              >
                <img
                  src={fullScreenIcon}
                  className="openedservicesMenuImg"
                  alt="fullScreenIcon"
                />
              </li>
            </Tooltip> */}
            {/* <Tooltip title={t("mapToolsServices.home")} placement="top">
              <li className=" serviceLi">
                <a
                  href={`${window.hostURL}/home/<USER>
                  target="_blank"
                  rel="noreferrer"
                >
                  <img
                    src={homeIcon}
                    alt="homeIcon"
                    className="openedservicesMenuImg"
                  />
                </a>
              </li>
            </Tooltip> */}
            {/* <Tooltip title={t("mapToolsServices.help")} placement="top">
              <li className="serviceLi " onClick={openHelp} name="openhelp">
                <img
                  src={help}
                  alt="servicesIcon"
                  name="openhelp"
                  className="openedservicesMenuImg"
                />
              </li>
            </Tooltip> */}
            <Tooltip title={t("mapTools.removeAll")} placement="top">
              <li
                className="serviceLi "
                onClick={removeAllGraphicsOnMap}
                name="openhelp"
              >
                {/* <CiTrash className="hashTest " /> */}
                <img src={trash_img} alt="" />
              </li>
            </Tooltip>
            {/* <Tooltip title={t("mapTools.move")} placement="top">
              <li
                className="serviceLi "
                onClick={disableActiveTool}
                name="openhelp"
              >
                <img src={move_img} alt="" />
              </li>
            </Tooltip> */}
            {privilegeIcons.find((icon) => icon.name == "select") &&
              selectedFeatures?.length > 0 && (
                <>
                  <Tooltip title={t("mapTools.update")} placement="top">
                    <li
                      className="serviceLi"
                      onClick={() => setShowUpdateLand(true)}
                      name="update"
                    >
                      <img src={refresh} alt="" />
                    </li>
                  </Tooltip>
                </>
              )}
            {privilegeIcons.length > 0 &&
              privilegeIcons.map((icon) => (
                <Tooltip title={t(icon.label)} placement="top">
                  <li
                    className="serviceLi "
                    onClick={icon.onClick}
                    name={icon.name}
                  >
                    {/* <FaArrowsAlt className="hashTest " /> */}
                    {/* <img src={move_img} alt="" /> */}
                    {/* <RiFullscreenFill className="hashTest" name={"select"} /> */}

                    <img src={icon.icon} alt="" style={{ width: "20px" }} />
                  </li>
                </Tooltip>
              ))}
            {/* <Tooltip title={t("mapToolsServices.select")} placement="top">
              <li
                className="serviceLi"
                name="select"
                onClick={(event) => {
                  openToolsData(event, "select");
                }}
              >
                <RiFullscreenFill
                  style={{ borderRadius: "30px" }}
                  className="hashTest "
                />
              </li>
            </Tooltip> */}
            {/* <Tooltip title={t("mapToolsServices.help")} placement="top">
              <li className="serviceLi " onClick={openHelp} name="openhelp">
                <MdOutlineTune className="hashTest CgArrowsExpandLeft" />
              </li>
            </Tooltip> */}
            <Tooltip
              title={t("mapToolsServices.compareLayers")}
              placement="top"
            >
              <li
                className="serviceLi "
                onClick={() => setCompareLayer(true)}
                name="compareLayers"
              >
                <img src={compare_layer_img} alt="" />
              </li>
            </Tooltip>
            <Tooltip title={t("mapToolsServices.fullscreen")} placement="top">
              <li
                className="serviceLi "
                onClick={toggleFullScreen}
                name="openhelp"
              >
                {/* <CgArrowsExpandLeft className="hashTest CgArrowsExpandLeft" /> */}
                <img src={help_img} alt="" />
              </li>
            </Tooltip>
          </ul>

          {showUpdateLand && (
            <UpdateLand
              languageState={props.languageState}
              mainData={props.mainData}
              map={props.map}
              activeTool={activeTool}
              closeToolsData={closeToolsData}
              openToolsData={openToolsData}
              openToolData={openToolData}
              selectedFeatures={selectedFeatures}
              Cleanup={Cleanup}
              onClose={() => setShowUpdateLand(false)}
              // setClearMapEvents={setClearMapEvents}
              // clearMapEvents={clearMapEvents}
            />
          )}

          {/* ================================== */}
          {compareLayer && (
            <CompareLayers
              languageState={props.languageState}
              mainData={props.mainData}
              map={props.map}
              activeTool={activeTool}
              closeToolsData={closeToolsData}
              openToolsData={openToolsData}
              openToolData={openToolData}
              onClose={() => setCompareLayer(false)}
              // setClearMapEvents={setClearMapEvents}
              // clearMapEvents={clearMapEvents}
            />
          )}
        </div>
      </Fade>
      <Fade top delay={500}>
        <div
          className={
            openToolservice
              ? "openedToolsMenu toolsHelp"
              : "closedToolsMenu toolsHelp"
          }
        >
          <ul>
            {tools.map((tool, index) => {
              console.log(
                "---------openToolData && String(activeTool) === String(tool.name)",
                openToolData && String(activeTool) === String(tool.name)
              );

              return (
                <>
                  {tool.name !== "openHelp" ? (
                    tool.name === "selectToUpdate" ? (
                      selectedFeatures?.length > 0 && (
                        <Tooltip
                          title={t(tool.tooltip)}
                          placement="right"
                          key={index + "tright"}
                        >
                          <li
                            onClick={(event) => {
                              openToolsData(event, tool.name);
                            }}
                            name={tool.name}
                            id={openToolservice ? "openedToolsMenuLi" : ""}
                            className={`serviceButton ${
                              String(activeTool) === String(tool.name)
                                ? "activeService"
                                : ""
                            }`.trim()}
                          >
                            {tool.icon ? (
                              // <tool.icon
                              //   className="hashTest"
                              //   id={tool.id}
                              //   name={tool.name}
                              // />
                              <img src={tool.icon} alt="" />
                            ) : (
                              <tool.fallbackIcon
                                className="hashTest"
                                id={tool.id}
                                name={tool.name}
                              />
                            )}
                          </li>
                        </Tooltip>
                      )
                    ) : (
                      <Tooltip
                        style={{
                          background:
                            tool.name === "print" || tool.name === "select"
                              ? "#ffffffab"
                              : "#ffffff73",
                        }}
                        title={t(tool.tooltip || "Default Tooltip")}
                        placement="right"
                        key={`${index}-right`}
                        className="darksouls"
                        slotProps={{
                          popper: {
                            modifiers: [
                              {
                                name: "offset",
                                options: {
                                  // offset: [0, 10],
                                },
                              },
                            ],
                          },
                          tooltip: {
                            sx: {
                              // bgcolor: "#111", // Tooltip background
                              // color: "#ffff", // Tooltip text color
                              // fontSize: "14px",
                              // // padding: "8px 12px",
                              // borderRadius: "8px",
                              // boxShadow: 3,
                              // background: "#338C9A",
                              // fontWeight: "400",
                            },
                          },
                        }}
                      >
                        <li
                          onClick={(event) => openToolsData?.(event, tool.name)}
                          name={tool.name}
                          id={openToolservice ? "openedToolsMenuLi" : ""}
                          className={`serviceButton ${
                            String(activeTool) === String(tool.name)
                              ? "activeService"
                              : ""
                          }`.trim()}
                        >
                          {tool.icon ? (
                            <img
                              className="openedservicesMenuImg"
                              src={tool.icon}
                              alt={tool.name || "toolsIcon"}
                              id={tool.id || `tool-${index}`}
                              name={tool.name}
                              style={{
                                width:
                                  tool.name === "select" ||
                                  tool.name === "print"
                                    ? "20px"
                                    : "auto",
                              }}
                              onError={(e) =>
                                (e.target.src = "/path/to/fallback-image.png")
                              } // Fallback image
                            />
                          ) : (
                            tool.fallbackIcon && (
                              <tool.fallbackIcon
                                className="hashTest"
                                id={tool.id || `tool-${index}`}
                                name={tool.name}
                              />
                            )
                          )}
                        </li>
                      </Tooltip>
                    )
                  ) : (
                    <Tooltip
                      key={index + "t"}
                      title={t(tool.tooltip)}
                      placement="right"
                    >
                      <li
                        onClick={openHelp}
                        // id={tool.id}
                        name={tool.name}
                        id={openToolservice ? "openedToolsMenuLi" : ""}
                        className={
                          String(activeTool) === String(tool.name)
                            ? "activeService "
                            : ""
                        }
                      >
                        <img
                          className="openedservicesMenuImg"
                          src={tool.icon}
                          alt="toolsIcon"
                          id={tool.id}
                          name={tool.name}
                        />
                      </li>
                    </Tooltip>
                  )}

                  {}
                  {openToolData && String(activeTool) === String(tool.name) ? (
                    <AllTools
                      languageState={props.languageState}
                      mainData={props.mainData}
                      setPopupInfo={props.setPopupInfo}
                      popupInfo={props.popupInfo}
                      activeTool={activeTool}
                      map={props.map}
                      closeToolsData={closeToolsData}
                      openToolsData={openToolsData}
                      setSketch={setSketch}
                      sketch={sketch}
                      openToolData={openToolData}
                      selectedFeatures={selectedFeatures}
                      setSelectedFeatures={setSelectedFeatures}
                      Cleanup={Cleanup}
                      landsAreas={landsAreas}
                      setLandsAreas={setLandsAreas}
                      // setClearMapEvents={setClearMapEvents}
                      // clearMapEvents={clearMapEvents}
                    />
                  ) : null}
                </>
              );
            })}
          </ul>
        </div>
      </Fade>
      {openServSearch ? (
        <ServicesSearch
          mainData={props.mainData}
          outerResultMenuShown={props.outerResultMenuShown}
          outerOpenResultMenu={props.outerOpenResultMenu}
          handleDrawerOpen={props.handleDrawerOpen}
          setFilteredResult={props.setFilteredResult}
          activeService={activeServiceItem}
          map={props.map}
          closeServiceSearch={closeServiceSearch}
        />
      ) : null}
    </div>
  );
}
