@font-face {
  font-family: "Droid Arabic Kufi";
  src: url("./assets/fonts/alfont_com_AlFont_com_DroidKufi-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

body {
  margin: 0;
  /* font-family: "Sans" !important; */
  font-family: "Droid Arabic Kufi", sans-serif !important;
}

html {
  font-family: "Droid Arabic Kufi", sans-serif !important;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
  font-family: "Droid Arabic Kufi", sans-serif !important;
}

* {
  font-family: "Droid Arabic Kufi", sans-serif !important;
}

.esri-view .esri-view-surface--inset-outline:focus::after {
  outline: none !important;
}

.esri-widget:focus-visible,
.esri-widget:focus,
.esri-widget *:focus-visible,
.esri-widget *:focus {
  outline: none !important;
}

/* .select-cust {
  display: grid !important;
  border: 1px solid #c9c3c3 !important;
  padding: 8px !important;
  border-radius: 22px !important;
  font-size: 12px;
} */



.ant-select-selector,
input.ant-input.ant-input-rtl {
  /* border: none !important; */
}

.ant-select-selection-item,
.ant-select-selector,
input.ant-input.ant-input-rtl {
  color: #284587 !important;
  /* border-color: transparent !important; */
  /* font-weight: bold !important; */
}

.ant-select-item-option-selected {
  background-color: #284587 !important
}

.checkbox_cust .ant-checkbox {
  order: 2;
}

.ant-checkbox-checked .ant-checkbox-inner,
.ant-checkbox-checked .ant-checkbox-inner:hover {
  background-color: #284587 !important;
  border: 2px solid #284587 !important
}

.ant-form-item-label {
  margin-right: 10px;

}

.ant-select-dropdown-rtl {
  border-radius: 20px !important;
}

hr:not([size]) {
  height: 2px;
  width: 86px;
}

.measurmentToolCopy p {
  padding: 0 !important;
}