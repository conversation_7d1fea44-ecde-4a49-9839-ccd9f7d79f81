import React, { useEffect, useState } from "react";
import axios from "axios";
import { useTranslation } from "react-i18next";
import Media from "react-media";
import { Routes } from "react-router";
import { BrowserRouter as Router, Route, useNavigate } from "react-router-dom";

import ExportPdf from "./tables/ExportsFeatures/ExportPdf";
import MarsedStatisticsPrint from "./tables/MarsedStatisticsPrint";
import AttrTblStatisticsPrint from "./tables/StatisticsOfAttrTbl/AttrTblStatisticsPrint";
import * as intl from "@arcgis/core/intl";
import Loader from "./containers/Loader";
import LoaderProvider from "./Contexts/LoaderContext";
import { appIds, modulesIDs } from "./helper/constants";
import {
  loginLayersSetting,
  TempobjectLayer,
  blockLandsLayerObj,
  OLD_BLOCK_LAYER_NAME,
  nonDisplayedLayers,
} from "./helper/layers";
import {
  isSomeArrItemsIncludedInsideAnother,
  notificationMessage,
} from "./helper/utilsFunc";
import interceptor from "./interceptors";
// import DashboardPage from "./screens/dashboard/DashboardPage";
import ArchivePage from "./screens/archive/ArchivePage";
import DashboardPage from "./screens/dashboard/DashboardPage";
import NotFoundPage404 from "./screens/ErrorsPages/404";
import ServerErrPage from "./screens/ErrorsPages/ServerErrPage";
import UnauthPage from "./screens/ErrorsPages/UnauthPage";
import MainPage from "./screens/MainPage";
import MobileAppScreen from "./screens/MobileAppScreen";
import ParcelOwnershipReport from "./screens/reports/ParcelOwnershipReport";
import GisParcelOwnership from "./screens/reports/GisParcelOwnership";
import GISNewDashboardPage from "./screens/GISDashboard/DashboardPage";
import esriInterceptor from "./esriInterceptors";

interceptor();
esriInterceptor();

//
export default function App() {
  window.dojoConfig = { locale: "ar" };
  const navigate = useNavigate();
  const { i18n, t } = useTranslation("layers");
  const [languageState, setChangeLang] = useState("ar");
  const toggleCurrentLang = () => {
    const styleLink = document.getElementById("cssStyleLink");
    if (languageState == "ar") {
      setChangeLang("en");
      i18n.changeLanguage("en");
      localStorage.setItem("lang", "en");
      styleLink.href = process.env.PUBLIC_URL + "/css/english.css";
      intl.setLocale("en");
      //window.location.reload();
    } else {
      setChangeLang("ar");
      i18n.changeLanguage("ar");
      localStorage.setItem("lang", "ar");
      styleLink.href = process.env.PUBLIC_URL + "/css/App.css";
      intl.setLocale("ar");
      //window.location.reload();
    }
  };
  // use Style
  useEffect(() => {
    let lang = localStorage.getItem("lang");
    setChangeLang(lang);
    const styleLink = document.getElementById("cssStyleLink");

    if (lang === "en") {
      styleLink.href = process.env.PUBLIC_URL + "/css/english.css";
      i18n.changeLanguage("en");
      intl.setLocale("en");
      setChangeLang("en");
      localStorage.setItem("lang", "en");
      // window.location.reload();
    } else {
      setChangeLang("ar");
      styleLink.href = process.env.PUBLIC_URL + "/css/App.css";
      localStorage.setItem("lang", "ar");
      i18n.changeLanguage("ar");
      intl.setLocale("ar");
      // window.location.reload();
    }
  }, [i18n.language]);

  const [loading, setLoading] = useState(true);
  const [mainData, setMainData] = useState({});
  useEffect(() => {
    //check login user
    try {
      checkAuth((data) => {
        setMainData(data);
        setLoading(false);
        if (!data.logged) navigate("/");
      }).catch((err) => {
        console.log(err);
        setLoading(false);
        notificationMessage("حدث خطأ أثناء استرجاع البيانات");
        navigate("/serverErr");
      });
    } catch (error) {
      console.log(error);
      setLoading(false);
      notificationMessage("حدث خطأ أثناء استرجاع البيانات");
      navigate("/serverErr");
    }
  }, []);
  const navigateToServerErrPage = () => navigate("/serverErr");
  const checkAuth = async (callBackFunc) => {
    let isUserInLocalStorage = localStorage.getItem("user");
    //logged user case
    if (isUserInLocalStorage) {
      isUserInLocalStorage = JSON.parse(isUserInLocalStorage);
      if (isUserInLocalStorage?.groups?.length) {
        //app_id = 5 for mapExpolerer
        //module_id == 53 of employee-users, 52 of public-users
        //get map explorer group from user object
        debugger;
        let mapExplorerGroup = isUserInLocalStorage?.groups?.filter(
          (gp) =>
            gp?.groups_permissions?.filter(
              (gper) => gper?.app_id === appIds.mapExpolerer
            )?.length
        );
        // empGroups is groups for logged in user not guest
        let empGroups = mapExplorerGroup?.filter((gp) => gp.layers); // all groups that contains layers
        //layers==> the layers must be displayed for logged users
        let empReqLayers = [];
        empGroups?.forEach((group) => {
          //each group includes: group permission, layers (there are more, but those are the most important)
          group?.layers.forEach((layGr) => {
            //each layer group includes: dependencies, outfields, id, arname, name
            //check if this layer is added before in empReqLayers array or not
            let isLayerExist = empReqLayers.find(
              (ll) => ll.name === layGr.name
            );
            //if so: get the not added dependencies and add it to layer(isLayerExist)
            if (isLayerExist) {
              layGr.dependencies.forEach((dep) => {
                let existDepsNames = isLayerExist?.dependencies?.map(
                  (depExist) => depExist?.name
                );
                if (existDepsNames?.includes(dep?.name)) return;
                else isLayerExist.dependencies.push(dep);
              });
            } else {
              empReqLayers.push(layGr);
            }
          });
        });
        // fill the empReqLayers with the required data like outFields alias, search fields .... etc from layers object into layers.js
        let layersWithReqData = fillLayersWithReqData(empReqLayers);
        /**
         * mainFunction includes the sidebar icons that will be displayed to logged user  
         * module_id =47 -> المرصد
          module_id=49 -> استيراد الملفات   //this one became for public user
          module_id=50 -> تصدير جوجل
          module_id = 48 -> البحث بالبيانات الوصفية
         */
        let mainFunctions = isUserInLocalStorage?.groups?.filter((g) => {
          if (
            g.groups_permissions.filter((gp) =>
              [
                modulesIDs.marsadModule,
                modulesIDs.exportFilesModule,
                modulesIDs.importGoogle,
                modulesIDs.searchByAttrModule,
                modulesIDs.incidentsModule,
                modulesIDs.landsKPIsDashboard,
                modulesIDs.layersMap,
                modulesIDs.interactiveMap,
                modulesIDs.updatingRequests,
              ].includes(gp.module_id) && gp?.app_id === appIds.mapExpolerer
            ).length
          )
            return g;
          else return undefined;
        });
        callBackFunc({
          layers: layersWithReqData,
          mainFunctions,
          logged: true,
          user: isUserInLocalStorage,
        });
      }
    } else {
      // guest user case --> get public privilage layers to display
      try {
        // set the public user map url in case of guest users
        window.mapUrl = window.publicUserMapUrl;

        let res = await axios(window.ApiUrl + "public-privilege");
        let data = res.data;
        let layers = data?.layers || [];
        let layersWithReqData = fillLayersWithReqData(layers, true);

        callBackFunc({
          layers: layersWithReqData,
          logged: false,
          user: null,
        });
      } catch (error) {
        throw error;
        // setLoading(false);
        // notificationMessage("حدث خطأ أثناء استرجاع البيانات");
        //todo: redirect to Error 404 page or contact with server page
      }
    }
  };
  const fillLayersWithReqData = (loggedOrGuestLayers, isGuest) => {
    let layersWithReqData = {};
    let layerNames = loggedOrGuestLayers.map((l) => l.name);
    //todo: hide the configred layers in nonDisplayedLayers
    loggedOrGuestLayers = loggedOrGuestLayers.map((layer) => {
      if (nonDisplayedLayers.includes(layer.name)) layer.hideFromSearchs = true; // hideFromSearchs is to hide this layer from general search or attribute table
      return layer;
    });
    // configured layersSettings that is in layers.js file
    let configuredLayersSettings = { ...loginLayersSetting };
    loggedOrGuestLayers?.forEach((lay) => {
      //keys are the layers name
      //in case of (configured layers)
      if (Object.keys(configuredLayersSettings).includes(lay.name)) {
        let reqOutFields = [],
          reqAliasOutFields = [],
          fields = [];
        lay.englishName = lay.name;
        lay.arabicName =
          configuredLayersSettings[lay.name].name || lay.arname || lay.name;
        // configured out fields that are in layer.js
        let configuredOutFields =
          configuredLayersSettings[lay.name]?.outFields || [];
        // configured alias of outfields that are in layer.js
        let configuredAliasOutFields =
          configuredLayersSettings[lay.name].aliasOutFields || [];
        // remove plan_name, sak number from plan_data layer [this code in master ]
        //TODO: need to put this code to prod
        if (lay.name === "Plan_Data" && isGuest) {
          let reqRemovedFields = ["PLAN_SAK_NO", "PLAN_NAME"];
          reqRemovedFields.forEach((f) => {
            let idx = configuredOutFields.findIndex((i) => i === f);
            configuredOutFields.splice(idx, 1);
            configuredAliasOutFields.splice(idx - 2, 1);
          });
        }
        //fetchedOutFields is from user object in case of logged user, public privilage in case of guest
        //get out field after trimming as there is some wrong spaces in name like: "DISTRICT_NAME "
        let outfieldsLayer =
          lay.name === OLD_BLOCK_LAYER_NAME
            ? lay.outfields.reverse()
            : lay.outfields;
        let fetchedOutFields = outfieldsLayer.map((f) => {
          let outF = f.name;
          if (typeof outF === "string") outF = outF.trim();
          return outF;
        });
        let fetchedAliasOutFields = outfieldsLayer.map((f) => {
          let outF = f.arname;
          if (typeof outF === "string") outF = outF.trim();
          return outF;
        });
        // let isSpatialIdExistInFetchedOutFields =  fetchedOutFields?.find(item=>item.includes("_SPATIAL_ID"));
        //TODO: need to edit this code in prod
        let isSpatialIdExistInConfiguredOutFields = configuredOutFields?.find(
          (item) => item.includes("_SPATIAL_ID")
        );

        configuredOutFields.forEach((fi, index) => {
          let isFieldSpatialId = fi.toString()?.includes("_SPATIAL_ID");
          reqOutFields.push(fi);
          if (fi !== "OBJECTID" && !isFieldSpatialId) {
            let reqAlias = "";
            //for alias --> check this: fetchedAliasOutFields then if not exist, check configuredAliasOutFields, then if not, get english name
            let configuredOutFieldIndex = configuredOutFields.indexOf(fi);
            let aliasNotTranslated =
              configuredAliasOutFields[
                isSpatialIdExistInConfiguredOutFields
                  ? configuredOutFieldIndex - 2
                  : configuredOutFieldIndex - 1
              ];
            //here there is a bug: order fields in fetchedAliasOutFields is not the same of configuredAliasOutFields
            let layOutFieldsIndex = fetchedOutFields.indexOf(fi);
            if (fetchedAliasOutFields[layOutFieldsIndex])
              reqAlias = fetchedAliasOutFields[layOutFieldsIndex];
            else if (aliasNotTranslated) {
              reqAlias = aliasNotTranslated;
            } else {
              reqAlias = fi;
            }
            reqAliasOutFields.push(reqAlias);

            fields.push({
              alias: reqAlias,
              fieldName: fi,
            });
          }
        });

        // configuredOutFields.forEach((f, index) => {
        //   //loop on configuredOutFields to add the common fields bet. configuredOutFields and fetchedOutFields in reqOutFields. is it the required approach ?!
        //   if (fetchedOutFields.includes(f)) {
        //     reqOutFields.push(f);
        //     if (f !== "OBJECTID" && !isSpatialIdExist) {
        //       reqAliasOutFields.push(configuredAliasOutFields[isSpatialIdExist?index-2 :index - 1]);
        //       fields.push({
        //         alias: configuredAliasOutFields[index - 1],
        //         fieldName: f,
        //       });
        //     }
        //   }
        // });

        lay = {
          ...lay,
          ...configuredLayersSettings[lay.name],
          outFields: reqOutFields.length
            ? reqOutFields
            : [...outfieldsLayer].map((f) => f.name.trim()),
          aliasOutFields: reqAliasOutFields,
          fields,
        };
        delete lay.outfields;
        let haveDependencies =
          lay?.dependecies?.filter((dep) => {
            if (lay?.dependencies?.map((d) => d.name).includes(dep.depName))
              return dep;
            //if (dep.depName === "gisAkarReportBtn") return dep; // Always keep gisAkarReportBtn
          }) || [];
        lay.dependecies = haveDependencies;
        //copy dep data to lay
        haveDependencies?.forEach((dep) => {
          if (layerNames.includes(dep.name)) return;
          else {
            if (layersWithReqData[dep.name]) return;
            else
              layersWithReqData[dep.name] = configuredLayersSettings[dep.name];
          }
        });
      }
      // in case of non configured layers
      else {
        let reqOutFields = [],
          reqAliasOutFields = [],
          fields = [];
        lay.englishName = lay.name;
        lay.arabicName = lay.arname || lay.englishName; //make it arName when arName is stored in db

        let fetchedOutFields = lay.outfields.map((f) => {
          let outF = f.name;
          if (typeof outF === "string") outF = outF.trim();
          return outF;
        });
        let fetchedAliasOutFields = lay.outfields.map((f) => {
          let outF = f.arname;
          if (typeof outF === "string") outF = outF.trim();
          return outF;
        });

        fetchedOutFields.forEach((fi, index) => {
          let isSpatialIdExist = fi.toString()?.includes("_SPATIAL_ID");
          reqOutFields.push(fi);
          if (fi !== "OBJECTID" && !isSpatialIdExist) {
            let reqAlias = "";
            //for alias --> check this: fetchedAliasOutFields then if not exist, then if not, get english name

            if (fetchedAliasOutFields[index])
              reqAlias = fetchedAliasOutFields[index];
            else {
              reqAlias = fi;
            }
            reqAliasOutFields.push(reqAlias);

            fields.push({
              alias: reqAlias,
              fieldName: fi,
            });
          }
        });
        let searchFieldsInTempObj = [...TempobjectLayer.searchFields].map(
          (item) => {
            return { ...item };
          }
        );
        let isThereCommonFields = isSomeArrItemsIncludedInsideAnother(
          searchFieldsInTempObj,
          reqOutFields
        );
        if (isThereCommonFields)
          lay = {
            ...lay,
            outFields: reqOutFields,
            aliasOutFields: reqAliasOutFields,
            searchFields: [...isThereCommonFields].map((item) => {
              return { ...item };
            }),
            isPublicSearchable: true,
            notInConfig: true,
            fields,
          };
        else
          lay = {
            ...lay,
            outFields: reqOutFields,
            aliasOutFields: reqAliasOutFields,
            notInConfig: true,
            searchFields: [],
            isPublicSearchable: true,
            fields,
          };
        //we need fill searchFields so
        //if is_search is existing in data --> use it as searchFields, if not use first field in reqOutFields
        let isFieldWithIsSearchExisting = lay?.outfields?.find(
          (f) => f?.is_search
        );
        console.log({
          layerName: lay.layerName,
          searchFields: lay?.outfields?.filter((i) => i.is_search),
        });
        if (isFieldWithIsSearchExisting) {
          lay?.outfields
            ?.filter((f) => f?.is_search)
            .forEach((i) => {
              if (!lay.searchFields.map((i) => i.field).includes(i.name))
                lay.searchFields.push({
                  field: i.name,
                  alias: i.arname ? i.arname : i.name,
                  // isSearch: true,
                  isServerSideSearch: true,
                });
            });
        } else if (!isFieldWithIsSearchExisting && reqOutFields.length) {
          if (reqOutFields[0] !== "OBJECTID")
            lay.searchFields.push({
              field: reqOutFields[0],
              alias: reqAliasOutFields[0]
                ? reqAliasOutFields[0]
                : reqOutFields[0],
              isSearch: true,
            });
        }
        let fieldIsSearch = lay.searchFields?.find((f) => f.isSearch);
        lay.displayField = fieldIsSearch?.field || lay?.outFields[0];
        delete lay.outfields;
      }
      if (!(lay?.outFields?.length || lay?.searchFields?.length)) {
        lay.isPublicSearchable = false;
        lay.isSearchable = false;
      }

      layersWithReqData[lay.englishName] = lay;
    });
    // to check if royal and ownership deps are existing
    let landbaseLayer = layersWithReqData?.Landbase_Parcel;
    if (landbaseLayer) {
      let isRoyalExist = landbaseLayer?.dependecies?.find(
        (dep) => dep.name === "LGR_ROYAL"
      );
      let isSalesLandExist = landbaseLayer?.dependecies?.find(
        (dep) => dep.name === "SALES_LANDS"
      );
      let isParcelPrivacyOExist = landbaseLayer?.dependecies?.find(
        (dep) => dep.name === "PARCEL_PRIVACY"
      );
      if (isRoyalExist) {
        let lay = { ...layersWithReqData["LGR_ROYAL"] };

        layersWithReqData["LGR_ROYAL"] = {
          ...lay,
          outFields: lay.outFields.map((i) => i.name),
          aliasOutFields: lay.outFields.map((i) => i.alias),
          fields: lay.outFields,
        };
      }
      if (isSalesLandExist) {
        let lay = { ...layersWithReqData["SALES_LANDS"] };

        layersWithReqData["SALES_LANDS"] = {
          ...lay,
          outFields: lay.outFields.map((i) => i.name),
          aliasOutFields: lay.outFields.map((i) => i.alias),
          fields: lay.outFields,
        };
      }
      if (isParcelPrivacyOExist) {
        let lay = { ...layersWithReqData["PARCEL_PRIVACY"] };

        layersWithReqData["PARCEL_PRIVACY"] = {
          ...lay,
          outFields: lay.outFields.map((i) => i.name),
          aliasOutFields: lay.outFields.map((i) => i.alias),
          fields: lay.outFields,
        };
      }
    }
    let oldBlockLayer = layersWithReqData?.OLD_BLOCK;
    if (oldBlockLayer) {
      let lay = { ...layersWithReqData["Landbase_Parcel"] };

      layersWithReqData["OLD_Landbase_Parcel"] = {
        ...lay,
        ...blockLandsLayerObj,
      };
      let reqDeletedKeys = [
        "fromAnotherLayer",
        "statistics",
        "depenedFilter",
        "isSearchable",
        "area",
        "groupByFields",
        "dashboardCharts",
        "dependecies",
        "dependencies",
      ];
      reqDeletedKeys.forEach((k) => {
        delete layersWithReqData["OLD_Landbase_Parcel"][k];
      });
    }
    return layersWithReqData;
  };

  if (loading || !mainData) return <Loader />;
  else
    return (
      <LoaderProvider>
        <div className="App">
          <Media
            queries={{
              small: "(max-width: 599px)",
              medium: "(min-width: 600px) and (max-width: 1199px)",
              large: "(min-width: 1200px)",
            }}
          >
            {(matches) =>
              matches.small ? (
                <MobileAppScreen />
              ) : (
                <Routes>
                  <Route
                    exact
                    path="/"
                    element={
                      <MainPage
                        mainData={mainData}
                        languageState={languageState}
                        setMainData={setMainData}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/Archive"
                    element={
                      <ArchivePage
                        toggleCurrentLang={toggleCurrentLang}
                        user={mainData?.user}
                        setMainData={setMainData}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/coordinateSearch"
                    element={
                      <MainPage
                        mainData={mainData}
                        languageState={languageState}
                        setMainData={setMainData}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/generalSearch"
                    element={
                      <MainPage
                        mainData={mainData}
                        setMainData={setMainData}
                        languageState={languageState}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/nearestService"
                    element={
                      <MainPage
                        mainData={mainData}
                        setMainData={setMainData}
                        languageState={languageState}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/layersMenu"
                    element={
                      <MainPage
                        mainData={mainData}
                        setMainData={setMainData}
                        languageState={languageState}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/contact"
                    element={
                      <MainPage
                        mainData={mainData}
                        setMainData={setMainData}
                        languageState={languageState}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/measurement"
                    element={
                      <MainPage
                        mainData={mainData}
                        languageState={languageState}
                        setMainData={setMainData}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/updatingRequests"
                    element={
                      <MainPage
                        mainData={mainData}
                        languageState={languageState}
                        setMainData={setMainData}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/interactiveMap"
                    element={
                      <MainPage
                        mainData={mainData}
                        languageState={languageState}
                        setMainData={setMainData}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/geoRange"
                    element={
                      <MainPage
                        mainData={mainData}
                        setMainData={setMainData}
                        languageState={languageState}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/painting"
                    element={
                      <MainPage
                        mainData={mainData}
                        setMainData={setMainData}
                        languageState={languageState}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/print"
                    element={
                      <MainPage
                        mainData={mainData}
                        setMainData={setMainData}
                        languageState={languageState}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/kml"
                    element={
                      <MainPage
                        mainData={mainData}
                        setMainData={setMainData}
                        languageState={languageState}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/bookmark"
                    element={
                      <MainPage
                        mainData={mainData}
                        setMainData={setMainData}
                        languageState={languageState}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/setting"
                    element={
                      <MainPage
                        mainData={mainData}
                        setMainData={setMainData}
                        languageState={languageState}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/marsed"
                    element={
                      <MainPage
                        mainData={mainData}
                        setMainData={setMainData}
                        languageState={languageState}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/import"
                    element={
                      <MainPage
                        mainData={mainData}
                        setMainData={setMainData}
                        languageState={languageState}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/metaDataSearch"
                    element={
                      <MainPage
                        mainData={mainData}
                        setMainData={setMainData}
                        languageState={languageState}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/search"
                    element={
                      <MainPage
                        mainData={mainData}
                        setMainData={setMainData}
                        languageState={languageState}
                        toggleCurrentLang={toggleCurrentLang}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/ExportPdf"
                    element={
                      <ExportPdf
                        languageState={languageState}
                        mainData={mainData}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/PrintPdfAttrTbl"
                    element={
                      <AttrTblStatisticsPrint
                        languageState={languageState}
                        mainData={mainData}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/MarsedStatisticsPrint"
                    element={<MarsedStatisticsPrint />}
                  />{" "}
                  {/* GIS Dashboard Page */}
                  <Route
                    exact
                    path="/gisDashboard"
                    id="dashboard"
                    element={<GISNewDashboardPage mainData={mainData} />}
                  />
                  <Route
                    exact
                    path="/incidentdashboard"
                    id="dashboard"
                    element={
                      <DashboardPage isIncident={true} mainData={mainData} />
                    }
                  />
                  <Route
                    exact
                    path="/akarReport"
                    element={
                      <ParcelOwnershipReport
                        mainData={mainData}
                        setMainData={setMainData}
                        languageState={languageState}
                        toggleCurrentLang={toggleCurrentLang}
                        navigateToServerErrPage={navigateToServerErrPage}
                      />
                    }
                  />
                  <Route
                    exact
                    path="/gisAkarReport"
                    element={
                      <GisParcelOwnership
                        mainData={mainData}
                        setMainData={setMainData}
                        languageState={languageState}
                        toggleCurrentLang={toggleCurrentLang}
                        navigateToServerErrPage={navigateToServerErrPage}
                      />
                    }
                  />
                  <Route exact path="/serverErr" element={<ServerErrPage />} />
                  <Route exact path="/unauthPage" element={<UnauthPage />} />
                  <Route
                    exact
                    path="/404notfound"
                    element={<NotFoundPage404 />}
                  />
                </Routes>
              )
            }
          </Media>
        </div>
      </LoaderProvider>
    );
}
