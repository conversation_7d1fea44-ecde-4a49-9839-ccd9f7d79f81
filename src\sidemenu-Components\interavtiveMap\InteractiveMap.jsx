import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@mui/material";
import { <PERSON><PERSON>, message } from "antd";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import tools from "../../assets/images/sidemenu/tools.svg";
import maps from "../../assets/images/sidemenu/maps.svg";
import study_areas from "../../assets/images/sidemenu/study-areas.svg";
import areas from "../../assets/images/sidemenu/areas.svg";
import InteractiveMapTools from "./InteractiveMapTools";
import InteractiveMapMaps from "./InteractiveMapMaps";
import InteractiveMapStudyAreas from "./InteractiveMapStudyAreas";
import InteractiveMapAreas from "./InteractiveMapAreas";
import axios from "axios";
import eventBus from "../../helper/EventBus";

import design_services from "../../assets/icons/design_services.svg";
import interactiveMap from "../../assets/icons/interactiveMap.svg";
import ineractiveVector from "../../assets/icons/ineractiveVector.svg";
import interactiveGroup from "../../assets/icons/interactiveGroup.svg";
import clarityAnimationSolid from "../../assets/icons/clarity_animation-solid.svg";
import InteractiveMapTopBar from "./InteractiveMapTopBar";
export default function InteractiveMap(props) {
  const { t } = useTranslation("sidemenu");

  /***
   * selected tab
   */
    const [openDrawer, setOpenDrawer] = useState(true);
  const [selectedTab, setSelectedTab] = useState("tools"); // tools - my_maps - study_areas - areas
  const [regionsData, setRegionsData] = useState([]);
  const [editDrawingData, setEditDrawingData] = useState(undefined);

  useEffect(() => {
    if (!editDrawingData && selectedTab == "tools") {
      getAllRegions();
    } else if (editDrawingData && selectedTab != "tools") {
      setEditDrawingData(undefined);
    }
  }, [selectedTab]);

  useEffect(() => {
    window.__speedFactor = 5;
    return () => {
      // resetMap();
      eventBus.dispatch("setShowInteractivEditBar", { message: false });
    };
  }, []);

  const resetMap = () => {
    props.map.view.goTo(window.__fullExtent);
    props.map.findLayerById("InteractiveMapGraphicLayer").graphics.removeAll();
  };

  const defaultMapView = () => {
    props.map.view.extent = window.__fullExtent;
    props.map.findLayerById("InteractiveMapGraphicLayer").graphics.removeAll();
  };

  const changeSelectedTab = (name) => {
    eventBus.dispatch("setShowInteractivEditBar", {
      message: name == "tools" ? true : false,
    });
    setSelectedTab(name);
  };

  const getAllRegions = async () => {
    await axios
      .get(`${window.ApiUrl}/StudyRegion/GetAll?pageSize=100`)
      .then((response) => {
        let regions = response.data.results.map((region) => {
          return {
            id: region.id,
            name: region.name,
            zones: region.zones,
          };
        });
        setRegionsData([...regions]);
      })
      .catch((error) => {
        message.warning(t("ErrorRequest"));
      });
  };

  const drawSavedGraphicsRef = useRef();

  const calldrawSavedGraphicsRef = (drawing) => {
    if (drawSavedGraphicsRef.current) {
      drawSavedGraphicsRef.current(drawing);
    }
  };

  const [sliderValue, setSliderValue] = useState(1);

  const handleChangeSliderValue = (event, newValue) => {
    window.__speedFactor = newValue * 5;
    setSliderValue(newValue);
  };

  return (
    <div
      style={{
        paddingRight: "10px",
        paddingLeft: "10px",
        // height: "calc(100vh - 60px)",
        // overflow: "auto",
        marginTop: "14px",
      }}
      className="interactiveMap"
    >
      {/* <div
        style={{ height: "1.5px", background: "#fff", marginBlock: "10px" }}
      /> */}

      {/* start tabs */}
      <div
        className="tabs"
        style={{
          display: "flex",
          gap: "8px",
          // marginBlock: "20px",
          alignItems: "center",
        }}
      >
        <Tooltip placement="top" title={t("tools")} className="MuiTooltipStyle">
          <Button
            style={{
              background: selectedTab === "tools" ? "#338C9A" : "#284587",
              borderRadius: selectedTab === "tools" ? "20px" : "20px",
              color: "#fff",
              // padding: "8px",
              border: "none",
              boxShadow: "none",
              display: "flex",
              alignItems: "center",
            }}
            onClick={() => changeSelectedTab("tools")}
          >
            <img
              src={design_services}
              alt="tools"
              style={{ margin: "0 0 0 3px", width: "16px" }}
            />
            {selectedTab === "tools" && (
              <span className="interActive-span">{t("tools")}</span>
            )}
          </Button>
        </Tooltip>

        <Tooltip placement="top" title={t("my_maps")}>
          <Button
            style={{
              background: selectedTab === "my_maps" ? "#338C9A" : "#284587",
              borderRadius: selectedTab === "my_maps" ? "20px" : "20px",
              // padding: "8px",
              color: "#fff",
              border: "none",
              boxShadow: "none",
              display: "flex",
              alignItems: "center",
            }}
            onClick={() => changeSelectedTab("my_maps")}
          >
            <img
              src={interactiveMap}
              alt="my_maps"
              style={{ margin: "0 0 0 3px", width: "16px" }}
            />
            {selectedTab === "my_maps" && (
              <span className="interActive-span">{t("my_maps")}</span>
            )}
          </Button>
        </Tooltip>

        <Tooltip placement="top" title={t("study_areas")}>
          <Button
            style={{
              background: selectedTab === "study_areas" ? "#338C9A" : "#284587",
              borderRadius: selectedTab === "study_areas" ? "20px" : "20px",
              // padding: "8px",
              color: "#fff",
              border: "none",
              boxShadow: "none",
              display: "flex",
              alignItems: "center",
            }}
            onClick={() => changeSelectedTab("study_areas")}
          >
            <img
              src={ineractiveVector}
              alt="study_areas"
              style={{ margin: "0 0 0 3px", width: "16px" }}
            />
            {selectedTab === "study_areas" && (
              <span className="interActive-span">{t("study_areas")}</span>
            )}
          </Button>
        </Tooltip>

        <Tooltip placement="top" title={t("areas")}>
          <Button
            style={{
              background: selectedTab === "areas" ? "#338C9A" : "#284587",
              borderRadius: selectedTab === "areas" ? "20px" : "20px",
              // padding: "8px",
              color: "#fff",
              border: "none",
              boxShadow: "none",
              display: "flex",
              alignItems: "center",
            }}
            onClick={() => changeSelectedTab("areas")}
          >
            <img
              src={interactiveGroup}
              alt="areas"
              style={{ margin: "0 0 0 3px", width: "16px" }}
            />
            {selectedTab === "areas" && (
              <span className="interActive-span">{t("areas")}</span>
            )}
          </Button>
        </Tooltip>

        {/* <Slider
          value={sliderValue}
          onChange={handleChangeSliderValue}
          min={0}
          max={5}
          step={1}
          marks
          valueLabelDisplay="auto"
          sx={{
            marginInline: "10px",
            "& .MuiSlider-thumb": {
              backgroundColor: "#b55433",
              "&:hover": {
                boxShadow: "none",
              },
            },
            "& .MuiSlider-track": {
              backgroundColor: "#b55433",
              borderColor: "transparent",
            },
            "& .MuiSlider-rail ": {
              backgroundColor: "#fff",
            },
            "& .MuiSlider-valueLabel": {
              backgroundColor: "#b55433",
              color: "white",
              padding: "5px 10px",
              borderRadius: "5px",
              fontStyle: "italic",
            },
          }}
        /> */}
        {/* <Slider
          // defaultValue={50}
          aria-label="Default"
          valueLabelDisplay="auto"
          value={sliderValue}
          onChange={handleChangeSliderValue}
        /> */}
        {selectedTab === "tools" && (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-start",
              width: "-webkit-fill-available",
            }}
          >
            <img src={clarityAnimationSolid} alt="" />
            <Slider
              value={sliderValue}
              onChange={handleChangeSliderValue}
              min={0}
              max={5}
              step={1}
              valueLabelDisplay="auto"
              sx={{
                marginInline: "10px",
                height: 8,
                "& .MuiSlider-thumb": {
                  width: 20,
                  height: 20,
                  backgroundColor: "#2a8d98", // teal color like the image
                  boxShadow: "none",
                  "&:hover": {
                    boxShadow: "none",
                  },
                },
                "& .MuiSlider-track": {
                  backgroundColor: "#2a8d98", // right side of track
                  border: "none",
                },
                "& .MuiSlider-rail": {
                  backgroundColor: "#fff", // left side of track
                  opacity: 1,
                },
                "& .MuiSlider-valueLabel": {
                  backgroundColor: "#2a8d98",
                  color: "white",
                  padding: "3px 10px",
                  borderRadius: "5px",
                  fontStyle: "italic",
                },
              }}
            />
          </div>
        )}
      </div>
      {/* end tabs */}
       {/* <InteractiveMapTopBar openDrawer={openDrawer} map={props.map} /> */}
      {selectedTab === "tools" && (
        <InteractiveMapTools
          map={props.map}
          regionsData={regionsData}
          editDrawingData={editDrawingData}
          setEditDrawingData={setEditDrawingData}
          callDrawSavedGraphics={calldrawSavedGraphicsRef}
          defaultMapView={defaultMapView}
        />
      )}
      {selectedTab === "my_maps" && (
        <InteractiveMapMaps
          map={props.map}
          defaultMapView={defaultMapView}
          setSelectedTab={setSelectedTab}
          setEditDrawingData={setEditDrawingData}
          setDrawGraphicsRef={(func) => (drawSavedGraphicsRef.current = func)}
        />
      )}
      {selectedTab === "study_areas" && <InteractiveMapStudyAreas />}
      {selectedTab === "areas" && <InteractiveMapAreas map={props.map} />}
    </div>
  );
}
