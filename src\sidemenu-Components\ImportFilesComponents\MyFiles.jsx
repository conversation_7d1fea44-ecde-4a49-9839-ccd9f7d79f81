import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { MdKeyboardArrowDown } from "react-icons/md";
import share from "../../assets/icons/share.svg";
import special_maps_logo from "../../assets/icons/archiveInfo.svg";
// import edit_icon from "../../assets/images/sidemenu/edit.svg";
import delete_icon from "../../assets/images/sidemenu/delete.svg";
import TrashBin2 from "../../assets/icons/Trash Bin 2.svg";
import arrow_drop_down from "../../assets/icons/arrow_drop_down.svg";
import arrow_drop_up from "../../assets/icons/arrow_drop_up.svg";
import axios from "axios";
import { Button, message, Modal, Select } from "antd";
import {
  executeGPTool,
  showGeneralDataTable,
  showLoading,
} from "../../helper/common_func";
import { Checkbox, FormControlLabel, FormGroup } from "@mui/material";
import { IoMdClose } from "react-icons/io";
import usePersistentState from "../../helper/usePersistentState";
import { getUniqeID } from "../../helper/utilsFunc";

import { RiArrowDropDownFill } from "react-icons/ri";
import { useRef } from "react";
import { Tooltip } from "@mui/material";
import { FaCheck } from "react-icons/fa";

const componentName = "MyFiles";
export default function MyFiles(props) {
  const { t } = useTranslation("common");

  const [showSpecialFiles, setShowSpecialFiles] = usePersistentState(
    "showSpecialFiles",
    true,
    componentName
  );
  const [showSharedFiles, setShowSharedFiles] = usePersistentState(
    "showSharedFiles",
    true,
    componentName
  );
  const [privateFiles, setPrivateFiles] = usePersistentState(
    "privateFiles",
    [],
    componentName
  );
  const [sharedFiles, setSharedFiles] = usePersistentState(
    "sharedFiles",
    [],
    componentName
  );

  useEffect(() => {
    getPrivateFiles();
    return () => {};
  }, []);

  const getPrivateFiles = async () => {
    await axios
      .get(`${window.ApiUrl}/ImportedFile/GetAll?pageSize=100`)
      .then((privateResponse) => {
        let privateFiles = privateResponse?.data.results?.map((file) => file);
        setPrivateFiles([...privateFiles]);
        getSharedFiles();
      })
      .catch((error) => {
        message.warning(t("ErrorRequest"));
      });
  };

  const getSharedFiles = async () => {
    await axios
      .get(`${window.ApiUrl}/ImportedFile/shared?pageSize=100`)
      .then((sharedResponse) => {
        let sharedFiles = sharedResponse.data.results.map((file) => file);
        setSharedFiles([...sharedFiles]);
      })
      .catch((error) => {
        message.warning(t("ErrorRequest"));
      });
  };

  const handleFileClick = (file) => {
    let fileName = file.path;
    let fileExt = file.path.split(".");
    fileExt = fileExt[fileExt.length - 1];
    let params;
    let processingToolUrl;
    let fileType = "cad";
    let outputName = "output_value";

    if (fileExt == "kmz" || fileExt == "kml") {
      params = {
        KML_File_Name: fileName,
      };
      processingToolUrl = window.kmlToJSONGPUrl;
      outputName = "output_value";
      fileType = "kmz";
    } else if (fileExt == "dwg") {
      params = {
        CAD_File_Name: fileName,
      };
      processingToolUrl = window.cadToJsonGPUrl;
      outputName = "output_value";
    }

    showLoading(true);
    let userObj = localStorage.getItem("user");
    if (userObj) userObj = JSON.parse(userObj);
    executeGPTool(
      `${processingToolUrl}?token=${userObj ? userObj.esriToken : ""}`,
      params,
      (result) => {
        // showLoading(false);
        let fileID = getUniqeID();
        showGeneralDataTable({
          type: "importGisFile",
          data: result,
          map: props.map,
          uploadFileType: fileType,
          show: true,
          fileID,
        });
      },
      (error) => {
        showLoading(false);
        message.error(t("sidemenu:addFileToMapError"));
      },
      outputName,
      "submitJob",
      userObj?.esriToken
    );
  };

  ////// delete file /////

  const [activeDeleteName, setActiveDeleteName] = usePersistentState(
    "activeDeleteName",
    undefined,
    componentName
  );
  const [activeDeleteId, setActiveDeleteId] = usePersistentState(
    "activeDeleteId",
    undefined,
    componentName
  );
  const [deleteModalVisible, setdeleteModalVisible] = usePersistentState(
    "deleteModalVisible",
    false,
    componentName
  );

  const deleteFile = async (fileId) => {
    try {
      const delteUrl = `${window.ApiUrl}/ImportedFile/${fileId}`;
      await axios.delete(delteUrl);
      return { success: true };
    } catch (error) {
      return { success: false };
    }
  };

  const showDelete = (file) => {
    setActiveDeleteId(file.id);
    setActiveDeleteName(file.name);
    setdeleteModalVisible(true);
  };

  const submitDeleteFile = async () => {
    const fileToDelete = privateFiles.find((file) => file.id == activeDeleteId);
    const result = await deleteFile(fileToDelete.id);
    if (!result.success) {
      message.warning(t("ErrorRequest"));
    } else {
      const otherFiles = privateFiles.filter(
        (file) => file.id != fileToDelete.id
      );
      setPrivateFiles(otherFiles);
      fileDeletedSuccessfully();
    }
  };

  const fileDeletedSuccessfully = () => {
    setActiveDeleteName();
    setActiveDeleteId();
    setdeleteModalVisible(false);
    message.warning(t("mapDeletedSuccessfully"));
    defaultView();
  };

  const defaultView = () => {
    props.map.view.extent = window.__fullExtent;
    showGeneralDataTable({
      type: "importGisFile",
      show: false,
    });
  };

  ///// share modal /////

  const [isShareModalOpen, setIsShareModalOpen] = usePersistentState(
    "isShareModalOpen",
    false,
    componentName
  );
  const [selectedUsers, setSelectedUsers] = usePersistentState(
    "selectedUsers",
    [],
    componentName
  );
  const [selectedDepartments, setSelectedDepartments] = usePersistentState(
    "selectedDepartments",
    [],
    componentName
  );
  const [checked, setChecked] = usePersistentState(
    "checked",
    {
      public: false,
      persons: false,
      departments: false,
    },
    componentName
  );
  const [fileToShare, setFileToShare] = usePersistentState(
    "fileToShare",
    undefined,
    componentName
  );
  const [usersList, setUsersList] = usePersistentState(
    "usersList",
    [],
    componentName
  );
  const [departmentsList, setDepartmentsList] = usePersistentState(
    "departmentsList",
    [],
    componentName
  );

  useEffect(() => {
    const searchInputs = document.querySelectorAll(
      ".ant-select-selection-search-input"
    );
    if (searchInputs.length > 0) {
      searchInputs.forEach((searchInput) =>
        searchInput.setAttribute("maxLength", 30)
      );
    }
  }, [checked]);

  const updateFile = async (fileObject) => {
    try {
      const putUrl = `${window.ApiUrl}/ImportedFile/${fileObject.id}`;
      await axios.put(putUrl, fileObject).then((response) => {
        let updatedFiles = privateFiles.map((file) =>
          file.id != fileObject.id ? file : { ...fileObject }
        );
        setPrivateFiles([...updatedFiles]);
      });
      return { success: true };
    } catch (error) {
      return { success: false };
    }
  };

  const showShareModal = (file) => {
    setIsShareModalOpen(true);
    setFileToShare({ ...file });
    if (file.is_public) {
      handleChangeShareOption({
        target: { name: "public", checked: true },
      });
    }
    if (file.shared_users && file.shared_departments) {
      handleChangeShareOption({ persons: true, departmens: true });
      setSelectedUsers([
        ...file.shared_users.map((user) => {
          return {
            id: user.user_id,
            name: user.user_name,
          };
        }),
      ]);
      setSelectedDepartments([
        ...file.shared_departments.map((dept) => {
          return {
            id: dept.department_id,
            name: dept.department_name,
          };
        }),
      ]);
    } else if (file.shared_users) {
      handleChangeShareOption({
        target: { name: "persons", checked: true },
      });
      setSelectedUsers([
        ...file.shared_users.map((user) => {
          return {
            id: user.user_id,
            name: user.user_name,
          };
        }),
      ]);
    } else if (file.shared_departments) {
      handleChangeShareOption({
        target: { name: "departments", checked: true },
      });
      setSelectedDepartments([
        ...file.shared_departments.map((dept) => {
          return {
            id: dept.department_id,
            name: dept.department_name,
          };
        }),
      ]);
    }
  };

  const handleShareModalOk = () => {
    setIsShareModalOpen(false);
  };

  const handleShareModalCancel = () => {
    setIsShareModalOpen(false);

    setSelectedUsers([]);
    setSelectedDepartments([]);

    setChecked({
      public: false,
      persons: false,
      departments: false,
    });
  };

  const handleChangeShareOption = (event) => {
    if (event.persons && event.departmens) {
      setChecked({
        public: false,
        persons: true,
        departments: true,
      });
    } else if (event.target.name == "public" && event.target.checked) {
      setChecked({
        public: true,
        persons: false,
        departments: false,
      });
    } else {
      if (event.target.checked) {
        if (event.target.name == "persons") {
          searchEntity("", "select-user");
        } else {
          searchEntity("", "select-department");
        }
      }
      setChecked({
        ...checked,
        [event.target.name]: event.target.checked,
      });
    }
  };

  const searchEntity = async (name, entityType) => {
    debugger;
    // if (name.trim().length && name) {
    switch (entityType) {
      case "select-user":
        await axios
          .get(
            `${window.ApiUrl}/user?pageSize=100&filter_key=name&q=${name}&contain=1`
          )
          .then((response) => {
            setUsersList([...response.data.results]);
          })
          .catch((error) => message.warning(t("ErrorRequest")));
        break;
      case "select-department":
        await axios
          .get(
            `${window.ApiUrl}/department/GetAll?pageSize=100&filter_key=name&q=${name}&contain=1`
          )
          .then((response) => {
            setDepartmentsList([...response.data.results]);
          })
          .catch((error) => message.warning(t("ErrorRequest")));
        break;

      default:
        break;
    }
    // } else {
    //   setUsersList([]);
    //   setDepartmentsList([]);
    //   entityType == "select-user"
    //     ? setOpenUserDropDown(false)
    //     : setOpenDeptDropDown(false);
    // }
  };

  const handleSelectedDepartment = (id) => {
    if (id) {
      let selectedDepartment = departmentsList.find((dept) => dept.id == id);

      let isExist = selectedDepartments.find(
        (dept) => dept.id == selectedDepartment.id
      );
      if (isExist) {
        message.warning(t("alreadySelected"));
      } else {
        setSelectedDepartments((prevState) => {
          return [
            ...prevState,
            {
              id: selectedDepartment.id,
              name: selectedDepartment.name,
            },
          ];
        });
      }
    }
  };

  const handleDepartmentDeletion = (department) => {
    let depts = selectedDepartments.filter((dept) => dept.id != department.id);
    setSelectedDepartments([...depts]);
  };

  const handleSelectedUser = (id) => {
    if (id) {
      let selectedUser = usersList.find((user) => user.id == id);

      let isExist = selectedUsers.find((user) => user.id == selectedUser.id);
      if (isExist) {
        message.warning(t("alreadySelected"));
      } else {
        setSelectedUsers((prevState) => {
          return [
            ...prevState,
            { id: selectedUser.id, name: selectedUser.name },
          ];
        });
      }
    }
  };

  const handleUserDeletion = (user) => {
    let users = selectedUsers.filter((usr) => usr.id != user.id);
    setSelectedUsers([...users]);
  };

  const handleShareFile = () => {
    //setChecked({});
    let file = { ...fileToShare };
    if (!checked.public && !checked.persons && !checked.departments) {
      message.warning(t("NoShareChoice"));
    } else if (checked.public && file) {
      file.is_public = true;
      file.shared_users = null;
      file.shared_departments = null;
    } else {
      file.is_public = false;
      if (selectedUsers.length > 0) {
        let usersToShare = selectedUsers.map((user) => {
          return {
            user_id: user.id,
            user_name: user.name,
            file_id: file.id,
          };
        });
        file.shared_users = [...usersToShare];
      } else {
        file.shared_users = null;
      }
      if (selectedDepartments.length > 0) {
        let deptsToShare = selectedDepartments.map((dept) => {
          return {
            department_id: dept.id,
            department_name: dept.name,
            file_id: file.id,
          };
        });

        file.shared_departments = [...deptsToShare];
      } else {
        file.shared_departments = null;
      }
    }

    let success = updateFile(file);
    if (!success) {
      message.warning(t("ErrorRequest"));
    } else {
      message.warning(t("sharedSuccessfully"));
      handleShareModalCancel();
    }
  };

  const timeoutRef1 = useRef(null);
  const timeoutRef2 = useRef(null);

  return (
    <>
      <>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "10px",
          }}
        >
          {/* start special maps */}
          <div className="box">
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                color: "#fff",
              }}
            >
              <div
                style={{ display: "flex", gap: "5px", alignItems: "center" }}
              >
                <img src={special_maps_logo} alt="maps logo" />
                <span className="interactive_map_span">
                  {t("special", { ns: "sidemenu" })}
                </span>
              </div>

              <MdKeyboardArrowDown
                size={20}
                style={{
                  cursor: "pointer",
                  transform: `rotate(${!showSpecialFiles ? "180deg" : 0})`,
                }}
                onClick={() => setShowSpecialFiles(!showSpecialFiles)}
                className="MdKeyboardArrowDown_icon"
              />
            </div>

            {showSpecialFiles &&
              privateFiles.map((file, indx) => {
                return (
                  <div
                    key={indx}
                    className="generalSearchCard"
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      padding: "18px 5px",
                      borderRadius: "16px",
                      background: "#FFFFFF99",
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleFileClick(file);
                    }}
                  >
                    <div>
                      <div className="interactive_drawing_label">
                        {file.name}
                      </div>
                      <div></div>
                    </div>
                    <div style={{ display: "flex", gap: "10px" }}>
                      <Tooltip placement="top" title={t("share")}>
                        <img
                          src={share}
                          alt="share icon"
                          onClick={(e) => {
                            e.stopPropagation();
                            showShareModal(file);
                          }}
                        />
                      </Tooltip>
                      <Tooltip placement="top" title={t("delete")}>
                        <img
                          src={TrashBin2}
                          alt="delete icon"
                          onClick={(e) => {
                            e.stopPropagation();
                            showDelete(file);
                          }}
                        />
                      </Tooltip>
                    </div>
                  </div>
                );
              })}
          </div>
          {/* end special maps */}

          {/* start shared maps */}
          <div className="box">
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                color: "#fff",
              }}
            >
              <div
                style={{ display: "flex", gap: "5px", alignItems: "center" }}
              >
                <img src={share} alt="maps logo" />
                <span className="interactive_map_span">
                  {t("shared", { ns: "sidemenu" })}
                </span>
              </div>

              <MdKeyboardArrowDown
                size={20}
                style={{
                  cursor: "pointer",
                  transform: `rotate(${!showSharedFiles ? "180deg" : 0})`,
                }}
                onClick={() => setShowSharedFiles(!showSharedFiles)}
                className="MdKeyboardArrowDown_icon"
              />
            </div>

            {showSharedFiles &&
              sharedFiles.map((file, indx) => {
                return (
                  <div
                    className="generalSearchCard"
                    style={{
                      textAlign: "start",
                      padding: "20px",
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleFileClick(file);
                    }}
                  >
                    <div>{file.name}</div>
                  </div>
                );
              })}
          </div>
          {/* end shared maps */}
        </div>
      </>
      <>
        <Modal
          // title={t("deleteDrawingConfirmation") + activeDeleteName}
          title={<div className="custom-modal-title">حذف</div>}
          centered
          closable={false}
          visible={deleteModalVisible}
          //onOk={() => afterEditModal()}
          onCancel={() => setdeleteModalVisible(false)}
          // okText={t("yes")}
          // cancelText={t("no")}
          className="shareModal sharmodalBookmark"
          footer={
            <div className="footer_modal">
              <Button
                key="submit"
                type="primary"
                onClick={() => submitDeleteFile()}
                className="footer_buttonEdit_modal"
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "10px",
                }}
              >
                {/* <img src={PenImg} alt="" /> */}
                <FaCheck />
                <span className="me-1">{t("yes")}</span>
              </Button>
              <Button
                key="cancel"
                onClick={() => {
                  setdeleteModalVisible(false);
                }}
                className="footer_buttonCancel_modal"
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "10px",
                }}
              >
                <IoMdClose color="red" />
                <span className="me-1">{t("no")}</span>
              </Button>
            </div>
          }
        >
          <div
            style={{
              textAlign: "center",
              fontSize: "16px",
              fontWeight: "bold",
              marginTop: "24px",
            }}
          >
            {t("deleteDrawingConfirmation") + activeDeleteName}
          </div>
          {/* <div
            style={{
              display: "flex",
              gap: "10px",
              marginTop: "10px",
              justifyContent: "end",
            }}
          >
            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => {
                submitDeleteFile();
              }}
            >
              {t("yes")}
            </Button>

            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => setdeleteModalVisible(false)}
            >
              {t("no")}
            </Button>
          </div> */}
        </Modal>
      </>

      {/* start share modal */}
      <>
        <Modal
          className="shareModal"
          title={<div className="custom-modal-title">{t("share")}</div>}
          visible={isShareModalOpen}
          onOk={handleShareModalOk}
          onCancel={handleShareModalCancel}
          footer={null} // <-- Hide buttons
        >
          <FormGroup style={{ textAlign: "start" }}>
            <div className="shardContent">
              {/* <FormControlLabel
                control={
                  <Checkbox
                    checked={checked.public}
                    onChange={handleChangeShareOption}
                    name="public"
                    sx={{
                      color: "#fff",
                      "&.Mui-checked": {
                        color: "#fff",
                      },
                    }}
                  />
                }
                label={t("public")}
              /> */}
              <div style={{ display: "flex", alignItems: "center" }}>
                <input
                  type="checkbox"
                  checked={checked.public}
                  onChange={handleChangeShareOption}
                  name="public"
                  className="toc-gallery-content_checkbox"
                />
                <label className="checkBox_print">{t("all_employees")}</label>
              </div>
              <div></div>
            </div>
            <div className="shardContent">
              {/* <FormControlLabel
                control={
                  <Checkbox
                    checked={checked.persons}
                    disabled={checked.public ? true : false}
                    onChange={handleChangeShareOption}
                    name="persons"
                    sx={{
                      color: "#fff",
                      "&.Mui-checked": {
                        color: "#fff",
                      },
                    }}
                  />
                }
                label={t("persons")}
              /> */}
              <div style={{ display: "flex", alignItems: "center" }}>
                <input
                  type="checkbox"
                  disabled={checked.public ? true : false}
                  onChange={handleChangeShareOption}
                  name="employee"
                  className="toc-gallery-content_checkbox"
                />
                <label className="checkBox_print">{t("employee")}</label>
              </div>
              {checked.persons && (
                <div style={{ marginTop: "10px" }}>
                  {/* <div style={{ display: "grid" }}>
                    <label
                      className="selectLabelStyle"
                      style={{ marginBlock: "0" }}
                    >
                      {t("search_select")}
                    </label>
                    <Select
                      onDropdownVisibleChange={(flag) => setOpen1(flag)}
                      id="select-user"
                      virtual={false}
                      suffixIcon={null}
                      // suffixIcon={
                      //   open1 ? (
                      //     <img src={arrow_drop_up} alt="" />
                      //   ) : (
                      //     <img src={arrow_drop_down} alt="" />
                      //   )
                      // }
                      open={openUserDropDown}
                      filterOption={false}
                      showSearch
                      allowClear
                      className="dont-show"
                      placeholder={t("name")}
                      onSearch={(value) => {
                        searchEntity(value, "select-user");
                      }}
                      value={null}
                      onSelect={() => setOpen1(true)}
                      onChange={(value) => {
                        handleSelectedUser(value);
                        setUsersList([]);
                        setOpenUserDropDown(false);
                        setOpen1(false);
                      }}
                    >
                      {usersList.length > 0 &&
                        usersList.map((user) => {
                          return (
                            <Select.Option key={user.id} value={user.id}>
                              {user.name}
                            </Select.Option>
                          );
                        })}
                    </Select>
                  </div> */}
                  <div className="select-cust select-cust_mapArea">
                    <label className="selectLabelStyle">
                      {" "}
                      {t("search_select")}
                    </label>
                    <Select
                      allowClear
                      id="select-user"
                      virtual={false}
                      suffixIcon={<RiArrowDropDownFill size={30} />}
                      className="searchInput englishFont compareLayers-select"
                      showSearch
                      placeholder={t("name")}
                      onSearch={(value) => {
                        if (timeoutRef1.current) {
                          clearTimeout(timeoutRef1.current);
                        }
                        timeoutRef1.current = setTimeout(() => {
                          searchEntity(value, "select-user");
                        }, 1500);
                      }}
                      value={null}
                      onChange={(value) => {
                        handleSelectedUser(value);
                        setUsersList([]);
                      }}
                      dropdownMatchSelectWidth={true}
                      filterOption={false}
                      placement="bottomLeft"
                      getPopupContainer={() => document.body}
                      dropdownStyle={{
                        maxHeight: "150px",
                        overflow: "auto",
                        zIndex: 99999,
                      }}
                      notFoundContent={
                        usersList.length === 0 ? t("search_select") : null
                      }
                    >
                      {/* {regionsData.map((region) => {
                        return (
                          <Select.Option
                            value={region.name}
                            id={region.id}
                            key={region.id}
                          >
                            {region.name}
                          </Select.Option>
                        );
                      })} */}
                      {usersList.length > 0 &&
                        usersList.map((user) => {
                          return (
                            <Select.Option
                              key={user.id}
                              value={user.id}
                              // id={user.id}
                            >
                              {user.name}
                            </Select.Option>
                          );
                        })}
                    </Select>
                  </div>
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      gap: "10px",
                      marginTop: "10px",
                    }}
                  >
                    {selectedUsers.length > 0 &&
                      selectedUsers.map((user) => (
                        <div
                          style={{
                            backgroundColor: "#fff",
                            padding: "5px 10px",
                            margin: "0 10px",
                            color: "#b45333",
                            borderRadius: "20px",
                            display: "flex",
                            alignItems: "center",
                            gap: "10px",
                          }}
                        >
                          {user.name}
                          <IoMdClose
                            color="#000"
                            style={{ cursor: "pointer" }}
                            onClick={() => handleUserDeletion(user)}
                          />
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
            <div className="shardContent">
              {/* <FormControlLabel
                control={
                  <Checkbox
                    checked={checked.departments}
                    disabled={checked.public ? true : false}
                    onChange={handleChangeShareOption}
                    name="departments"
                    sx={{
                      color: "#fff",
                      "&.Mui-checked": {
                        color: "#fff",
                      },
                    }}
                  />
                }
                label={t("departments")}
              /> */}
              <div style={{ display: "flex", alignItems: "center" }}>
                <input
                  type="checkbox"
                  checked={checked.departments}
                  disabled={checked.public ? true : false}
                  onChange={handleChangeShareOption}
                  name="departments"
                  className="toc-gallery-content_checkbox"
                />
                <label className="checkBox_print">{t("departments")}</label>
              </div>
              {checked.departments && (
                <div style={{ marginTop: "10px" }}>
                  {/* <div style={{ display: "grid" }}>
                    <label
                      className="selectLabelStyle"
                      style={{ marginBlock: "0" }}
                    >
                      {t("search_select")}
                    </label>
                    <Select
                      id="select-department"
                      virtual={false}
                      suffixIcon={null}
                      onDropdownVisibleChange={(flag) => setOpen2(flag)}
                      // suffixIcon={
                      //   open2 ? (
                      //     <img src={arrow_drop_up} alt="" />
                      //   ) : (
                      //     <img src={arrow_drop_down} alt="" />
                      //   )
                      // }
                      open={opendeptDropDown}
                      showSearch
                      filterOption={false}
                      allowClear
                      className="dont-show"
                      placeholder={t("department")}
                      onSearch={(value) => {
                        searchEntity(value, "select-department");
                      }}
                      value={null}
                      onSelect={() => setOpen2(true)}
                      onChange={(value) => {
                        handleSelectedDepartment(value);
                        setDepartmentsList([]);
                        setOpenDeptDropDown(false);
                        setOpen2(false);
                      }}
                    >
                      {departmentsList.length > 0 &&
                        departmentsList.map((department) => {
                          return (
                            <Select.Option
                              key={department.id}
                              value={department.id}
                            >
                              {department.name}
                            </Select.Option>
                          );
                        })}
                    </Select>
                  </div> */}
                  <div className="select-cust select-cust_mapArea">
                    <label className="selectLabelStyle">
                      {" "}
                      {t("search_select")}
                    </label>
                    <Select
                      id="select-department"
                      allowClear
                      virtual={false}
                      suffixIcon={<RiArrowDropDownFill size={30} />}
                      className="searchInput englishFont compareLayers-select"
                      showSearch
                      placeholder={t("department")}
                      onSearch={(value) => {
                        if (timeoutRef2.current) {
                          clearTimeout(timeoutRef2.current);
                        }
                        timeoutRef2.current = setTimeout(() => {
                          searchEntity(value, "select-department");
                        }, 1500);
                      }}
                      value={null}
                      onChange={(value) => {
                        handleSelectedDepartment(value);
                        setDepartmentsList([]);
                      }}
                      dropdownMatchSelectWidth={true}
                      filterOption={false}
                      placement="bottomLeft"
                      getPopupContainer={() => document.body}
                      dropdownStyle={{
                        maxHeight: "150px",
                        overflow: "auto",
                        zIndex: 99999,
                      }}
                      notFoundContent={
                        departmentsList.length === 0 ? t("search_select") : null
                      }
                    >
                      {/* {regionsData.map((region) => {
                        return (
                          <Select.Option
                            value={region.name}
                            id={region.id}
                            key={region.id}
                          >
                            {region.name}
                          </Select.Option>
                        );
                      })} */}
                      {/* {usersList.length > 0 &&
                        usersList.map((user) => {
                          return (
                            <Select.Option
                              key={user.id}
                              value={user.id}
                             // id={user.id}
                            >
                              {user.name}
                            </Select.Option>
                          );
                        })} */}
                      {departmentsList.length > 0 &&
                        departmentsList.map((department) => {
                          return (
                            <Select.Option
                              key={department.id}
                              value={department.id}
                            >
                              {department.name}
                            </Select.Option>
                          );
                        })}
                    </Select>
                  </div>
                  {selectedDepartments.length > 0 &&
                    selectedDepartments.map((department) => (
                      <div
                        style={{
                          display: "flex",
                          flexDirection: "row",
                          gap: "10px",
                          marginTop: "10px",
                        }}
                      >
                        <div
                          style={{
                            backgroundColor: "#fff",
                            padding: "5px 10px",
                            margin: "0 10px",
                            color: "#b45333",
                            borderRadius: "20px",
                            display: "flex",
                            alignItems: "center",
                            gap: "10px",
                          }}
                        >
                          {department.name}
                          <IoMdClose
                            color="#000"
                            style={{ cursor: "pointer" }}
                            onClick={() => handleDepartmentDeletion(department)}
                          />
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </div>
          </FormGroup>

          <button
            className="SearchBtn mt-3"
            size="large"
            block
            onClick={handleShareFile}
          >
            {t("share")}
          </button>
        </Modal>
      </>
      {/* end share modal */}
    </>
  );
}
