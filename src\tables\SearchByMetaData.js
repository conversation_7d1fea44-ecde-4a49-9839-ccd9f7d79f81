import React, { useState, useEffect, useRef } from "react";
import { Container } from "react-bootstrap";
import { Button, Tooltip, Row, Col, Input, Table, Space, Select } from "antd";
import axios from "axios";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import arrow_drop_down from "../assets/icons/arrow_drop_down.svg";
import arrow_drop_up from "../assets/icons/arrow_drop_up.svg";
import zoom_in_icon from "../assets/icons/zoom.svg";
import {
  faChevronCircleRight,
  faChevronCircleLeft,
  faExpandArrowsAlt,
  faFilter,
  faSearchPlus,
  faChartPie,
  faAddressBook
} from "@fortawesome/free-solid-svg-icons";
import { toArabic } from "arabic-digits";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { DownCircleFilled } from "@ant-design/icons";

import { SearchOutlined, DoubleRightOutlined, DoubleLeftOutlined } from "@ant-design/icons";
import moment from "moment-hijri";
import { useNavigate } from "react-router-dom";
import FilteRModal from "./FiltersComps/FilterByAttr/FilterModal";
import HijriDatePicker from "../components/hijriDatePicker/components/HijriDatePicker";
import MetaDataStatistics from "./StatisticsOfAttrTbl/MetaDataStatistics";
//import layers data
// import { layersSetting } from "../helper/layers";
//common_func
import {
  queryTask,
  getLayerId,
  getFeatureDomainName,
  showLoading,
  zoomToFeatureDefault,
  clearGraphics,
  highlightFeature,
  drawLine,
  clearCanvasLine,
  convertHirjiDateToTimeSpan,
  convertToArabic,
} from "../helper/common_func";

import replay_icon from "../assets/icons/replay.svg"
import pan_zoom_icon from "../assets/icons/pan_zoom.svg"
import pie_chart_icon from "../assets/icons/pie_chart.svg"
import filter_alt_icon from "../assets/icons/filter_alt.svg"

import PaginationComp from "./TablePagination/paginationComp";
import ExportFilesComp from "./ExportsFeatures/ExportFilesComp";
import {
  convertEngNumbersToArabic,
  convertNumbersToEnglish,
  isNumber,
  notificationMessage,
  showDataSplittedBySlash,
  getDateFromConcatNumbers
} from "../helper/utilsFunc";
import {
  getStatisticsForChart,
  getStatisticsForFeatsLayer,
  getSubtypes,
} from "./tableFunctions";
import { PARCEL_LANDS_LAYER_NAME, externalBtnsForTblData } from "../helper/constants";
import { useTranslation } from "react-i18next";
import kroky from "../assets/images/kroky.svg";
import splitIcon from "../assets/images/splitIcon.svg";
import updateContract from "../assets/images/updateContracts.svg";
import PlanDataModal from "./Modals/PlanLandsData/PlanDataModal";
import { randomPlanNumbers } from "../helper/layers";
import DistrictDataModal from "./Modals/DistrictLandsData/DistrictDataModal";

// const { Column } = Table;
export default function SearchByMetaData(props) {
  const [t] = useTranslation("common", "map", "layers");
  const navigate = useNavigate();
  const [planDataModal, setPlanDataModal] = React.useState();
  const [districtDataModal, setDistrictDataModal] = React.useState();
  const [open, setOpen] = useState(false);
  //for pagination
  const [currentPageNum, setCurrentPageNum] = useState({
    current: 1,
    dep: 1,
  });
  const [layersNames, setLayersNames] = useState([]);
  //these are filtered layer names on the right list by searching (write a word)
  const [displayedLinks, setDisplayedLinks] = useState([]);
  //main layer data
  const [tableData, setTableData] = useState([]);
  const [tblColumns, setTblColumns] = useState([]);

  //dependencties data
  const [depTableData, setDepTableData] = useState({
    data: [],
    layerData: "",
    layerMetadata: {},
  });
  const [depTblColumns, setDepTblColumns] = useState();

  const [activeLayer, setActiveLayer] = useState({
    value: "", //layerName
    layerData: "",
  });

  const [showFilterModal, seFilteRModal] = useState(false);
  const [sideDisplay, setSide] = useState(true);
  //filter layer list
  const [searchText, setSearchLayerNameText] = useState("");
  //for filters
  const filterInput = useRef(null);
  // filter in table column
  const [colFilterWhere, setColFilterWhere] = useState({
    current: [],
    dep: [],
  });
  //filter by attribute
  const filterWhereClauseRef = useRef({
    current: "1=1",
    dep: {
      default: "",
      filtered: "",
    },
  });
  //Count and Area
  let countNumberRef = useRef({});
  let totalAreaRef = useRef({});
  const [countNumber, setCountNumber] = useState({
    current: 0,
    dep: 0,
  });
  const [totalArea, setTotalArea] = useState({
    current: null,
    dep: null,
  });
  // flag to display statistics screen for features on table for land parcel layer only
  const [showMetaStat, setMetaStat] = useState(false);
  //useEffects

  useEffect(() => {
    if (!props.mainData.logged)
      window.open(window.hostURL + "/home/<USER>", "_self");
    //intialize layersNames
    let mapAllLayers = props.map.__mapInfo.info.$layers.layers;
    // let mapAllTbls = props.map.__mapInfo.info.$layers.tables;
    let layersSetting = props.mainData.layers;
    let layersNames = Object.entries(layersSetting)
      ?.filter((l) => {
        if (layersSetting[l[0]].hideFromSearchs) return undefined;
        let mapLayerNames = mapAllLayers.map((lay) => lay.name);
        if (mapLayerNames.includes(l[1].englishName)) return l;
        else return undefined;
      })
      ?.map((l) => {
        return {
          layerName: l[0],
          layerMetadata: l[1],
        };
      });
    window.moment = moment;
    setLayersNames(layersNames);
    return () => {
      console.log("unmount");
      //clear highlightGraphicLayer + ZoomGraphicLayer
      clearGraphics(["ZoomGraphicLayer", "highlightGraphicLayer"], props.map);
      //reset filtered layer list
      setDisplayedLinks([]);
      //reset tbl columns
      setTblColumns([]);
      //reset tbl data
      setTableData([]);
      //reset active layer
      setActiveLayer({
        layerData: null,
        value: null,
      });
      //reset filter modal opening //I think it is unnecessary
      seFilteRModal(false);
      //reset filter by attribute where clause
      filterWhereClauseRef.current = {
        current: "1=1",
        dep: {
          default: "",
          filtered: "",
        },
      };
      setSide(true);
      //reset layer name shown on the list
      setLayersNames([]);
      //reset search text used for filter layer list
      setSearchLayerNameText("");
      //reset count of results
      setCountNumber({
        current: 0,
        dep: 0,
      });
      countNumberRef.current = {};
      //reset total area result shown on table
      setTotalArea({
        current: null,
        dep: null,
      });
      totalAreaRef.current = null;

      //for pagination
      setCurrentPageNum({
        current: 1,
        dep: 1,
      });
      //for filters per column in table
      setColFilterWhere({
        current: [],
        dep: [],
      });
      // reset statistics data
      // flag to display statistics screen for features on table

      setMetaStat(null);
      navigate("/");
    };
  }, []);
  // getFeatures based on currentPageNum (pagination)
  useEffect(() => {
    if (currentPageNum.current > 1 || currentPageNum.dep > 1) {
      showLoading(true);
      let queriedLayerData = depTblColumns
        ? depTableData.layerData
        : activeLayer.layerData;
      let startIndex = depTblColumns
        ? (currentPageNum.dep - 1) * window.paginationCount
        : (currentPageNum.current - 1) * window.paginationCount;

      let whereClause = getWhereForStatistics(depTblColumns);
      getLayerFeatures(queriedLayerData, false, {
        num: window.paginationCount,
        start: startIndex,
        where: whereClause
      }).then((data) => {


        settingTableData(
          depTblColumns ? depTblColumns : tblColumns,
          data.tableData,
          {
            isNewTbl: false,
            colsIsNeeded: false,
            isDependData: depTblColumns ? true : false,
          }
        );
        showLoading(false);
      });
    }
  }, [currentPageNum]);

  //useEffect for language state showing table column header in arabic if 'ar' and english if 'en'
  useEffect(() => {
    if (!props.languageState) return;
    else {
      //Arabic column header
      if (
        props.languageState === "ar" &&
        (depTblColumns || tblColumns).length
      ) {
        //in case of dependent tbl
        if (depTblColumns && depTblColumns?.length) {
          let cloneDepTblCol = [...depTblColumns];
          for (let i = 0; i < cloneDepTblCol.length; i++) {
            let item = cloneDepTblCol[i];
            item.title = item.arAlias;
          }
          setDepTblColumns(cloneDepTblCol);
        }
        if (tblColumns?.length) {
          let cloneTblCol = [...tblColumns];
          console.log("Dd", tblColumns, depTblColumns)
          for (let i = 0; i < cloneTblCol.length; i++) {
            let item = cloneTblCol[i];
            item.title = item.arAlias;
          }
          setTblColumns(cloneTblCol);
        }
      }
      //English column header
      else if (
        props.languageState === "en" &&
        (depTblColumns || tblColumns).length
      ) {
        if (depTblColumns && depTblColumns?.length) {
          let cloneDepTblCol = [...depTblColumns];
          for (let i = 0; i < cloneDepTblCol.length; i++) {
            let item = cloneDepTblCol[i];
            item.title = item.enAlias;
          }
          setDepTblColumns(cloneDepTblCol);
        }
        if (tblColumns?.length) {
          let cloneTblCol = [...tblColumns];
          for (let i = 0; i < cloneTblCol.length; i++) {
            let item = cloneTblCol[i];
            item.title = item.enAlias;
          }
          setTblColumns(cloneTblCol);
        }
      }
    }
    console.log(depTblColumns)
  }, [props.languageState]);

  ///////////////////////////////////////////////////////////////////////////
  //handlers land parcels statistics
  //handler to open statistics UI screen  ---> it is for just parcels layer
  const openMetaStat = async () => {
    if (showMetaStat) setMetaStat(false);
    else {
      let layerName = "Landbase_Parcel";
      let layerID = getLayerId(props.map.__mapInfo, layerName);
      let layerData = layersNames.find((f) => f.layerName === layerName);
      let isDepNotCurrent = depTblColumns ? true : false;
      let whereClause = getWhereForStatistics(isDepNotCurrent);
      showLoading(true);
      try {
        let { data } = await getStatisticsForChart(
          layerID,
          layerData,
          whereClause
        );
        if (data.length) {
          let landUseValues = props.map.__mapInfo.info.$layers.layers
            .find((l) => l.name === layerName)
            .fields.find((f) => f.name === "PARCEL_MAIN_LUSE");
          setMetaStat({
            totalCount: isDepNotCurrent ? countNumber.dep : countNumber.current,
            totalArea: isDepNotCurrent ? totalArea.dep : totalArea.current,
            landUseValues,
            data: data?.map((f) => f.attributes),
            where: whereClause,
          });
        } else {
          notificationMessage(t("common:NoDataAvailForStatistics"));
        }
        showLoading(false);
      } catch (err) {
        showLoading(false);
        if (err?.response?.status === 401) {
          //logut
          notificationMessage(t("common:sessionFinished"), 5);
          localStorage.removeItem("user");
          localStorage.removeItem("token");
          window.open(window.hostURL + "/home/<USER>", "_self");
        } else notificationMessage(t("common:retrievError"), 4);
      }
    }
  };
  //handle export PDF for statistics UI
  const exportPDFStatistics = () => {
    let isDepNotCurrent = depTblColumns ? true : false;
    let whereClause = showMetaStat.where;
    let count = isDepNotCurrent ? countNumber.dep : countNumber.current;
    let area = isDepNotCurrent ? totalArea.dep : totalArea.current;
    localStorage.setItem(
      "attrTblChart",
      whereClause + ";" + area + ";" + count
    );
    window.open(process.env.PUBLIC_URL + "/PrintPdfAttrTbl", "_blank");
  };
  ////////////////////////////////////////////////////////////////////////

  //////////////// open side list + activate a selected layer /////////////////////////
  const openSideLinkData = (e, queriedLayerData) => {
    //1- check if it is the same active link
    let layerName = e.target.id;
    if (layerName !== activeLayer.value) {
      //reset filter columns
      filterInput.current = null;
      let defaultWhereClause = "1=1";
      //2- highlight the item in list
      setActiveLayer({
        layerData: queriedLayerData,
        value: layerName,
      });
      //3- get the layer data
      let layerID = getLayerId(props.map.__mapInfo, layerName);
      let params = {
        layerID,
        pagCount: window.paginationCount,
        layerData: queriedLayerData,
        isNewTbl: true,
        colsIsNeeded: true,
      };
      resetPagination();
      setColFilterWhere({
        current: [],
        dep: [],
      });
      setDepTableData({
        data: [],
        layerData: "",
      });
      setDepTblColumns();
      setCountNumber({ ...countNumber, dep: 0 });   //Q1: why current is not setted by 0 ???
      countNumberRef.current = {
        ...countNumberRef.current,
        dep: 0,
      };
      setTotalArea({ ...totalArea, dep: null });      //Q2: why current is not setted by null ???
      totalAreaRef.current = {
        ...totalAreaRef.current,
        dep: null,
      };
      setCurrentPageNum({ ...currentPageNum, dep: 1 });     //Q3: why current is not setted by 1 ???
      // the answers of Q1, Q2, Q3 are here in the next line "getLayerDataForTable" in this func: they will be set again by actual data 
      getLayerDataForTable(params);
      //reset any filters 
      //reset filter by attribute
      filterWhereClauseRef.current = {
        current: defaultWhereClause,
        dep: {
          default: "",
          filtered: "",
        },
      };
      //reset filter by column 
      setColFilterWhere({
        dep: [], current: []
      })
    }
  };
  const openSide = (e) => {
    setSide(true);
  };
  const closeSide = (e) => {
    setSide(false);
  };

  //filter layer list by search
  const handleChangeInput = (e) => {
    let searchQuery = e.target.value.toLowerCase();
    setSearchLayerNameText(e.target.value.toLowerCase());
    let displayed = layersNames
      // .filter((la) => la.layerMetadata.isSearchable)
      .filter(function (el) {
        let searchValue = el.layerMetadata.name.toLowerCase();
        return searchValue.indexOf(searchQuery) !== -1;
      });
    setDisplayedLinks(displayed);
  };
  //////////////////////////////////////////////////////////////////////////

  ///////////////////////////////////////////////////////////////////////////////////////
  //open search/filter modal by attribute
  const openFilterModal = () => {
    seFilteRModal(!showFilterModal);
  };
  /////////////////////////////////////////////////////////////////////////////////////////

  ////////////////////////////////////////////////////////////////////////////////////////

  //helpers functions
  //get where clause for using in statistics UI ---> it is for just parcels layer
  const getWhereForStatistics = (isDepNotCurrent) => {
    let where = "";
    //in case of dependent layer
    if (isDepNotCurrent) {
      let isFilteredByCol = colFilterWhere.dep.length ? colFilterWhere.dep : [];
      let isFilteredByAttr =
        filterWhereClauseRef.current.dep.filtered ||
        filterWhereClauseRef.current.dep.default;
      if (isFilteredByAttr) where = isFilteredByAttr;
      if (isFilteredByCol.length)
        where = isFilteredByAttr
          ? where + " AND " + isFilteredByCol.join(" AND ")
          : isFilteredByCol.join(" AND ");
    } else {
      //current
      let isFilteredByCol = colFilterWhere.current.length
        ? colFilterWhere.current
        : [];
      let isFilteredByAttr = filterWhereClauseRef.current.current;
      if (isFilteredByAttr) where = isFilteredByAttr;
      if (isFilteredByCol.length)
        where = isFilteredByAttr
          ? where + " AND " + isFilteredByCol.join(" AND ")
          : isFilteredByCol.join(" AND ");
    }
    return where || "1=1";
  };
  //get table data to display
  const getLayerDataForTable = (params) => {
    const {
      layerID,
      pagCount,
      layerData,
      isNewTbl,
      callBackFunc,
      where,
      orderByFields,
      colsIsNeeded,
      isDependData,
    } = params;
    showLoading(true);

    Promise.all([
      getStatisticsForFeatsLayer(layerID, layerData, where),
      getLayerFeatures(layerData, colsIsNeeded, {
        where: where || "1=1",
        num: pagCount || window.paginationCount,
        start: 0,
        orderByFields,
      }),
    ])
      .then((data) => {
        setCountNumber(
          !isDependData
            ? {
              ...countNumberRef.current,
              current: data[0].countPlusArea.COUNT,
            }
            : {
              ...countNumberRef.current,
              dep: data[0].countPlusArea.COUNT,
            }
        );
        setTotalArea(
          !isDependData
            ? {
              ...totalAreaRef.current,
              current: data[0].countPlusArea.AREA
                ? parseFloat(data[0].countPlusArea.AREA / 1000000).toFixed(2)
                : null,
            }
            : {
              ...totalAreaRef.current,
              dep: data[0].countPlusArea.AREA
                ? parseFloat(data[0].countPlusArea.AREA / 1000000).toFixed(2)
                : null,
            }
        );
        countNumberRef.current = !isDependData
          ? { ...countNumberRef.current, current: data[0].countPlusArea.COUNT }
          : { ...countNumberRef.current, dep: data[0].countPlusArea.COUNT };
        totalAreaRef.current = !isDependData
          ? {
            ...totalAreaRef.current,
            current: data[0].countPlusArea.AREA
              ? parseFloat(data[0].countPlusArea.AREA / 10000).toFixed(2)
              : null,
          }
          : {
            ...totalAreaRef.current,
            dep: data[0].countPlusArea.AREA
              ? parseFloat(data[0].countPlusArea.AREA / 10000).toFixed(2)
              : null,
          };
        let tblCols = data[1].tblColumns.length
          ? data[1].tblColumns
          : tblColumns;
        settingTableData(tblCols, data[1].tableData, {
          isNewTbl,
          colsIsNeeded,
          isDependData,
        });
        showLoading(false);
        callBackFunc && callBackFunc();
      })
      .catch((err) => {
        console.log(err);
        showLoading(false);
        if (err?.response?.status === 401) {
          //logut
          notificationMessage(t("common:sessionFinished"), 5);
          localStorage.removeItem("user");
          localStorage.removeItem("token");
          window.open(window.hostURL + "/home/<USER>", "_self");
        } else notificationMessage(t("common:retrievError"), 5);
      });
  };
  const getLayerFeatures = async (queriedLayerData, colsIsNeeded, params) => {
    let { num, start, ...rest } = params;
    let layerID = getLayerId(props.map.__mapInfo, queriedLayerData.layerName);
    let promise = new Promise((resolve, reject) => {
      let queryParams = {
        url: window.mapUrl + "/" + layerID,
        notShowLoading: true,
        returnGeometry: true,
        outFields: queriedLayerData.layerMetadata.outFields,
        ...rest,
      };

      if (queriedLayerData.layerMetadata.returnDistinctValues) {
        queryParams.returnDistinctValues = queriedLayerData.layerMetadata.returnDistinctValues;
      } else {
        queryParams.num = num || 20;
        queryParams.start = start || 0;
      }

      queryTask({
        ...queryParams,
        callbackResult: ({ features }) => {
          let reqFieldsWithoutObjectID = [],
            tblData = [],
            tblCols = [];
          if (colsIsNeeded) {
            let layData = [
              ...props.map.__mapInfo.info.$layers.layers,
              ...props.map.__mapInfo.info.$layers.tables,
            ].find((l) => l.name === queriedLayerData.layerName);

            reqFieldsWithoutObjectID = layData.fields.filter((f) => {
              return (
                queriedLayerData.layerMetadata.outFields.includes(f.name) &&
                !["OBJECTID"].includes(f.name) &&
                !f.name?.toString().includes("_SPATIAL_ID")
              );
            });
            tblCols = reqFieldsWithoutObjectID.map((f, index) => {
              let hasSubtype = getSubtypes(
                f.name,
                props.map,
                queriedLayerData.layerName
              );
              //for filter in column
              if (hasSubtype)
                f.domain = hasSubtype.subTypeData.reduce((total, item) => {
                  if (!total.length) {
                    total = item.domains;
                  } else {
                    total = [...total, ...item.domains];
                  }
                  return total;
                }, []);
              let layerIsNotInConfig =
                queriedLayerData.layerMetadata?.notInConfig;
              let fieldFromLayerData =
                queriedLayerData?.layerMetadata?.fields?.find(
                  (ff) => ff.fieldName === f.name
                );

              let arAlias = layerIsNotInConfig
                ? fieldFromLayerData?.alias
                : fieldFromLayerData?.alias && (fieldFromLayerData?.alias).match('[\u0600-\u06ff]|[\u0750-\u077f]|[\ufb50-\ufbc1]|[\ufbd3-\ufd3f]|[\ufd50-\ufd8f]|[\ufd92-\ufdc7]|[\ufe70-\ufefc]|[\uFDF0-\uFDFD]')
                  ? fieldFromLayerData?.alias :
                  fieldFromLayerData?.alias ?
                    t(`layers:${fieldFromLayerData?.alias}`)
                    : f.alias || f.name;
              let rowProp = {
                filtered: false,
                title: props.languageState === "ar" ? arAlias : f.name,
                enAlias: f.name,
                arAlias: arAlias,
                dataType: f.type,
                withDomain: f.domain,
                dataIndex:
                  f.name.includes("_DATE") || f.name.includes("_date")
                    ? [f.name, "hijri"]
                    : f.name,
                key: f.name,
                sorter: true,
                sortOrder: false,
                filteredValue: null,
                defaultFilteredValue: undefined,
                filterResetToDefaultFilteredValue: true,
                ...setFiltersIntoTable(
                  f,
                  queriedLayerData,
                  props.languageState === "ar" ? arAlias : f.name
                ),

                sortDirections: ["ascend", "descend", "ascend"],
                showSorterTooltip: true,
              };
              if (!index) rowProp.width = "max-content";
              return {
                ...rowProp,
              };
            });
            let isLayer = props.map.__mapInfo.info.$layers.layers.find(
              (lay) => lay.name === queriedLayerData.layerName
            );
            let haveDependencies = queriedLayerData.layerMetadata?.dependecies;
            if ((isLayer || haveDependencies) && features.length)
              tblCols.push({
                title: "",
                key: "zoom",
                render: (text, record) => {
                  // console.log(record);
                  return (
                    <>
                      {isLayer ? (
                        <Button
                          className="tableHeaderBtn "
                          onClick={() => zoomToFeature(record)}>
                          <Tooltip placement="top" title={t("map:zoomMap")}>
                            {/* <FontAwesomeIcon icon={faSearchPlus} /> */}
                            <img src={zoom_in_icon} />
                          </Tooltip>
                        </Button>
                      ) : null}

                      {haveDependencies?.filter(dep => {
                        //filter icons based on owner type [sales, royal, private] lands
                        if (queriedLayerData.layerName === PARCEL_LANDS_LAYER_NAME && dep.showingField === "OWNER_TYPE") {
                          return (dep.codeValue === record?.OWNER_TYPE_Code)
                        }
                        return dep
                      })?.map((dep, index) => {
                        let planNoEng = record ? convertNumbersToEnglish(record?.PLAN_NO) : undefined;
                        if (dep.depName === 'LandStatistics' && randomPlanNumbers.includes(planNoEng)) return undefined;

                        // console.log({dep});
                        else if (
                          dep
                        )
                          return (
                            <Button
                              key={index}
                              className="tableHeaderBtn"
                              onClick={() => getDepData(dep, record)}>
                              <Tooltip
                                placement="top"
                                title={t(`layers:${dep.tooltip}`)}>
                                {dep.icon ? (
                                  <FontAwesomeIcon icon={dep.icon} />
                                ) : (
                                  <img
                                    alt='icons'
                                    src={dep.imgIconSrc}
                                    style={{
                                      cursor: "pointer",
                                    }}
                                  />
                                )}
                              </Tooltip>
                            </Button>
                          );
                        else return undefined;
                      })}
                    </>
                  );
                },
              });
          }
          if (features.length)
            getFeatureDomainName(features, layerID).then((feats) => {
              tblData = feats.map((f) => {
                return { ...f.attributes, geometry: f.geometry };
              });
              resolve({
                tableData: tblData,
                tblColumns: tblCols,
              });
            });
          else
            resolve({
              tableData: tblData,
              tblColumns: tblCols,
            });
        },
        callbackError: (err) => {
          notificationMessage(t("common:retrievError"), 4);

          reject(err);
        },
      });
    });
    return promise;
  };

  const getLayerExtext = () => {
    clearGraphics("ZoomGraphicLayer", props.map);
    if (tableData.length && tableData.find((d) => d.geometry)) {
      let features = tableData.filter((d) => {
        if (d.geometry?.rings || d.geometry?.paths || d.geometry?.x) return d;
        else return undefined;
      });
      highlightFeature(features, props.map, {
        layerName: "highlightGraphicLayer",
        isZoom: true,
        zoomDuration: 1000,
      });
    }
  };
  ////////////////////////table handlers and custom component ////////////////////////////////
  // helper func that munipulate data before display it via table

  const settingTableData = (tblColumns, newTableData, params) => {
    let { isNewTbl, colsIsNeeded, isDependData } = params;
    let totalDataToShow = [];
    //fill all empty values with لا يوجد
    let colData = [...tblColumns];
    let tblDataWithNotFoundValues = newTableData?.map((row) => {
      colData.forEach((col) => {
        if (col.key.includes("_DATE")) {
          let hijriDate =
            row[col.key] && isNumber(row[col.key])
              ? {
                timeStamp: row[col.key],
                hijri:
                  row[col.key]?.toString()?.length > 6
                    ? convertToArabic(moment(row[col.key]).format("iYYYY/iM/iD"))
                    : convertToArabic(moment(row[col.key]).format("iYYYY/iM")),
              }
              : row[col.key] && !isNumber(row[col.key])
                ? {
                  hijri: convertToArabic(row[col.key]),
                  timeStamp: null,
                }
                : {
                  hijri: t("common:notFoundValue"),
                  timeStamp: null,
                };
          //use convertEngNumbersToArabic method in utilsFunc to show it in arabic numbers
          row[col.key] = hijriDate;
        } else if (col.key == "PLAN_NO" || col.key == "PARCEL_PLAN_NO") {
          row[col.key] = convertToArabic(row[col.key]) ? convertToArabic(row[col.key]) : t('common:notAvailable');
        }
        else if (col.key.includes("DATE")) {
          row[col.key] = (convertToArabic(row[col.key]));
        }
        else {
          row[col.key] =
            typeof row[col.key] === "string"
              ? // if string
              row[col.key].trim()
                ? convertToArabic(row[col.key])
                : t("common:notAvailable")
              : //if not string
              row[col.key]
                ? convertToArabic(row[col.key])
                : t("common:notAvailable");
        }
      });
      return row;
    });
    let isLayer;
    if (isDependData) {
      isLayer = props.map.__mapInfo.info.$layers.layers.find(
        (lay) => lay.name === isDependData.layerName
      );

      if (isNewTbl) {
        totalDataToShow = [...tblDataWithNotFoundValues];
        const { isHidden, isSearchable, name } = props.mainData.layers[isDependData.layerName];
        setDepTableData(
          typeof isDependData === "object"
            ? {
              data: totalDataToShow,
              layerData: isDependData,
              layerMetadata: { isHidden, isSearchable, name },
            }
            : {
              ...depTableData,
              data: totalDataToShow,
              layerMetadata: { isHidden, isSearchable, name },
            }
        );
        if (colsIsNeeded) setDepTblColumns(colData);
      } else {
        //used in case of pagination to add old data with new data
        totalDataToShow = [...depTableData.data, ...tblDataWithNotFoundValues];
        setDepTableData(
          typeof isDependData === "object"
            ? {
              data: totalDataToShow,
              layerData: isDependData,
              layerMetadata: props.mainData.layers[isDependData.layerName],
            }
            : {
              ...depTableData,
              data: totalDataToShow,
              layerMetadata: props.mainData.layers[isDependData.layerName],
            }
        );
      }
    } else {
      isLayer = true;
      if (isNewTbl) {
        totalDataToShow = [...tblDataWithNotFoundValues];
        setTableData(totalDataToShow);
        if (colsIsNeeded) setTblColumns(colData);
        // clearGraphics(["highlightGraphicLayer"], props.map);
      } else {
        //used in case of pagination to add old data with new data
        totalDataToShow = [...tableData, ...tblDataWithNotFoundValues];
        setTableData(totalDataToShow);
      }
    }
    clearGraphics(["ZoomGraphicLayer", "highlightGraphicLayer"], props.map);
    if (isLayer) {
      if (totalDataToShow.length) {
        let features = totalDataToShow.filter((d) => {
          if (d.geometry?.rings || d.geometry?.paths || d.geometry?.x) return d;
          else return;
        });
        highlightFeature(features, props.map, {
          layerName: "highlightGraphicLayer",
          isZoom: true,
          zoomDuration: 1000,
        });
      }
    }
  };
  //reset filters function (for columns and attribute as well)
  const resetFilters = () => {
    console.log("reset");
    let defaultWhere = "1=1";
    //in case of dependent tbl not main layer
    if (
      colFilterWhere.dep.length ||
      (depTblColumns && filterWhereClauseRef.current.dep.filtered)
    ) {
      //dependency data
      let layerID = getLayerId(
        props.map.__mapInfo,
        depTableData.layerData.layerName
      );
      getLayerDataForTable({
        layerID,
        layerData: depTableData.layerData,
        isNewTbl: true,
        where: filterWhereClauseRef.current.dep.default,
        colsIsNeeded: true,
      });
      //reset filter by attribute for dependent tbl
      filterWhereClauseRef.current = {
        ...filterWhereClauseRef.current,
        dep: {
          ...filterWhereClauseRef.current.dep,
          filtered: "",
        },
      };
    }
    //in case of current main laye not dependent tbl
    else if (
      colFilterWhere.current.length ||
      (!depTblColumns && filterWhereClauseRef.current.current !== "1=1")
    ) {
      //currnet layer
      let layerID = getLayerId(props.map.__mapInfo, activeLayer.value);
      getLayerDataForTable({
        layerID,
        layerData: activeLayer.layerData,
        isNewTbl: true,
        where: defaultWhere,
        colsIsNeeded: true,
      });
      //reset only the current main layer
      filterWhereClauseRef.current = {
        ...filterWhereClauseRef.current,
        current: defaultWhere,
      };
    }
    //reset columns filter for both (dep, current layer)
    setColFilterWhere({
      current: [],
      dep: [],
    });
  };
  //handler for reset column filter
  const resetOnlyRestColFilter = () => {
    //in case of dependent tbl
    if (depTblColumns) {
      //dependency data
      let layerID = getLayerId(
        props.map.__mapInfo,
        depTableData.layerData.layerName
      );
      //to reset dep col filter ---> I have to go back to the filter by attr if found --> if not get all data "1=1"-->filterWhereClauseRef.current.dep.default
      //get the filter by attribute and get data based on it
      let depWhereClause = filterWhereClauseRef.current.dep.filtered
        ? filterWhereClauseRef.current.dep.filtered
        : filterWhereClauseRef.current.dep.default;
      getLayerDataForTable({
        layerID,
        layerData: depTableData.layerData,
        isNewTbl: true,
        where: depWhereClause,
        colsIsNeeded: true,
      });
      //?? I think filterWhereClauseRef is used for filter/search by attribute only not for tbl column
      //I comment the below lines of 'filterWhereClauseRef.current'
      // filterWhereClauseRef.current = {
      //   ...filterWhereClauseRef.current,
      //   dep: {
      //     ...filterWhereClauseRef.current.dep,
      //     filtered: "",
      //   },
      // };
    }

    //in case of currnet layer
    else {
      let layerID = getLayerId(props.map.__mapInfo, activeLayer.value);
      //to reset current col filter ---> I have to go back to the filter by attr if found -->
      //
      //get the filter by attribute and get data based on it
      let currWhereClause = filterWhereClauseRef.current.current || "1=1";
      getLayerDataForTable({
        layerID,
        layerData: activeLayer.layerData,
        isNewTbl: true,
        where: currWhereClause,
        colsIsNeeded: true,
      });
    }
    //reset tbl col filters for both (dep, current)
    setColFilterWhere({
      current: [],
      dep: [],
    });
  };
  //for filter in server side
  const setFiltersIntoTable = (rowData, layerData, placeholder) => {
    // let hasDropDownList = rowData.domain || getSubtypes(rowData.name,props.map,layerData.layerName );
    return {
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }) => {
        return (
          <div
            style={{
              padding: 8,
            }}>
            {rowData.domain?.codedValues || rowData.domain ? (
              <Select
                virtual={false}
                //suffixIcon={<DownCircleFilled />}
                onDropdownVisibleChange={(flag) => setOpen(flag)}
                suffixIcon={
                  open ? (
                    <img src={arrow_drop_up} alt="" />
                  ) : (
                    <img src={arrow_drop_down} alt="" />
                  )
                }
                showSearch
                // allowClear
                onSelect={(value, input) => {
                  setOpen(true);
                  setSelectedKeys([
                    {
                      name: input.name,
                      value,
                    },
                  ]);
                }}
                onChange={() => setOpen(false)}
                ref={filterInput}
                placeholder={`${t(`common:searchWith`)} ${placeholder}`}
                value={selectedKeys[0]?.name}
                // onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                style={{
                  marginBottom: 8,
                  display: "block",
                }}
                // placeholder="القيمة"
                getPopupContainer={(trigger) => trigger.parentNode}
                optionFilterProp="name"
                filterOption={(input, option) => {
                  return option.name && option.name.indexOf(input) >= 0;
                }}>
                {(rowData.domain?.codedValues || rowData.domain)
                  .filter((c) => c.name && c.code)
                  .map((domain, index) => {
                    return (
                      <Select.Option
                        key={index}
                        name={domain.name}
                        value={domain.code}>
                        {domain.name}
                      </Select.Option>
                    );
                  })}
              </Select>
            ) : rowData.type === "esriFieldTypeDate" ? (
              <HijriDatePicker
                disableOnClickOutside
                placeholder={t("common:selectDate")}
                input={{
                  ref: { filterInput },
                  // id:"value" + row.id,
                  value: selectedKeys,
                  onChange: (e) => setSelectedKeys(e),
                }}
              />
            ) : (
              <Input
                ref={filterInput}
                placeholder={`${t(`common:searchWith`)} ${placeholder}`}
                value={selectedKeys[0]}
                onChange={(e) =>
                  setSelectedKeys(e.target.value ? [e.target.value] : [])
                }
                onPressEnter={() =>
                  handleTblFilter(selectedKeys, confirm, rowData.name)
                }
                style={{
                  marginBottom: 8,
                  display: "block",
                }}
              />
            )}

            <Space>
              <button
                type="primary"
                className="SearchBtn mt-3 w-25"
                size="large"
                htmlType="submit"
                onClick={() => handleTblFilter(selectedKeys, confirm, rowData)}
                icon={<SearchOutlined />}
                style={{
                  width: 90,
                }}>
                بحث
              </button>
              <button
                className="SearchBtn mt-3 w-25"
                size="large"
                htmlType="submit"
                onClick={() =>
                  clearFilters && handleResetTblFilter(clearFilters, confirm)
                }
                style={{
                  width: 90,
                }}>
                {t("common:cancelSearch")}{" "}
              </button>
            </Space>
          </div>
        );
      },
      filterIcon: (filtered) => (
        <SearchOutlined
          // onClick={(e)=>handleClickingOnfilterIcon(e, rowData, layerData)}
          style={{
            color: filtered ? "#1890ff" : undefined,
          }}
        />
      ),
      onFilterDropdownVisibleChange: (visible) => {
        if (visible) {
          setTimeout(() => filterInput.current?.select?.(), 100);
        }
      },
      render: (text) => text,
    };
  };

  const handleTblFilter = (selectedKeys, confirm, rowData) => {
    resetPagination()
    confirm(); //confirm will fire onTableChange
  };

  const handleResetTblFilter = (clearFilters, confirm) => {
    clearFilters();
    confirm(); //confirm will fire onTableChange
  };

  const onTableChange = (newPagination, filters, sorter, actionObj, e) => {
    //layer/table data from map object
    let layData = [
      ...props.map.__mapInfo.info.$layers.layers,
      ...props.map.__mapInfo.info.$layers.tables,
    ].find(
      (l) =>
        l.name ===
        (depTblColumns ? depTableData.layerData.layerName : activeLayer.value)
    );

    let layerFields = layData.fields;
    let reqLayerData = depTblColumns
      ? depTableData.layerData
      : activeLayer.layerData;
    let layerID = getLayerId(props.map.__mapInfo, reqLayerData.layerName);
    //in case of sort
    if (actionObj.action === "sort" && actionObj?.currentDataSource?.length) {
      let tblColsEdited = [...tblColumns];
      //1- set sortOrder to highlight sort icon on table
      tblColsEdited.forEach((c) => {
        if (c.key === sorter.columnKey) c.sortOrder = sorter.order;
        else c.sortOrder = false;
      });
      let filterColWhere = [];
      //copy filters fields from filters object to filter dictionary
      let filterDic = {};
      for (let f in filters) {
        if (filters[f]) {
          filterDic[f] =
            typeof filters[f] === "object" ? filters[f][0] : filters[f];
        }
      }
      //set table columns to reflect all changes on it
      setTblColumns(tblColsEdited);
      //whereClause of previous filters
      let whereClause = depTblColumns
        ? filterWhereClauseRef.current.dep.filtered
          ? filterWhereClauseRef.current.dep.filtered
          : filterWhereClauseRef.current.dep.default
        : filterWhereClauseRef.current.current;
      //if there is a new implemented filter rather than prevois one ---> update the whereClause to contain prev + new filters
      if (Object.values(filterDic).length) {
        Object.entries(filterDic).forEach((f) => {
          let fLayer = layerFields.find((fl) => fl.name === f[0]);

          if (fLayer.type === "esriFieldTypeString") {
            filterColWhere.push(` ${fLayer.name} LIKE '%${f[1]}%'`);
          } else if (
            [
              "esriFieldTypeInteger",
              "esriFieldTypeDouble",
              "esriFieldTypeSmallInteger",
            ].includes(fLayer.type)
          ) {
            let value = typeof f[1] === "object" ? f[1].value : f[1];
            filterColWhere.push(` ${fLayer.name} = ${value}`);
          } else {
            //date

            filterColWhere.push(
              `(${fLayer.name} >= ${convertHirjiDateToTimeSpan(f[1])})`
            );
            filterColWhere.push(
              `(${fLayer.name} <= ${convertHirjiDateToTimeSpan(f[1], true)})`
            );
          }
        });
        if (filterColWhere.length) {
          whereClause =
            whereClause !== "1=1"
              ? "(" +
              whereClause +
              ") AND (" +
              filterColWhere.join(" AND ") +
              ")"
              : "(" + filterColWhere.join(" AND ") + ")";
        }
      }
      showLoading(true);
      getLayerDataForTable({
        layerID,
        layerData: reqLayerData,
        isNewTbl: true,
        callBackFunc: () => notificationMessage(t("orderSucc")),
        where: whereClause,
        orderByFields: [
          `${sorter.columnKey} ${sorter.order === "ascend" ? "ASC" : "DESC"}`,
        ],
        colsIsNeeded: false,
      });
    } else if (
      actionObj.action === "sort" &&
      !actionObj?.currentDataSource?.length
    ) {
      notificationMessage(t("common:noDataForSort"));
    } else if (actionObj.action === "filter") {
      let filterColWhere = [];
      let filterDic = {};
      for (let f in filters) {
        if (filters[f]) {
          filterDic[f] =
            typeof filters[f] === "object" ?
              typeof filters[f][0] !== 'object' ? filters[f][0] :
                filters[f][0]?.value
              : filters[f];
        }
      }
      //1- set filtered to highlight filter icon on table
      let tblColsEdited = depTblColumns ? [...depTblColumns] : [...tblColumns];
      tblColsEdited.forEach((col) => {
        if (col.key === "zoom") return;
        col.filtered = Object.keys(filterDic).includes(col.key);
        if (!col.filtered) col.filteredValue = null;
        else col.filteredValue = [filterDic[col.key]];
      });
      depTblColumns
        ? setDepTblColumns(tblColsEdited)
        : setTblColumns(tblColsEdited);

      let whereClauseToGetData;
      //check if there is a column filter --> set all col filters into filter dictionary and push them to filterColWhere array
      if (Object.values(filterDic).length) {
        Object.entries(filterDic).forEach((f) => {
          let fLayer = layerFields.find((fl) => fl.name === f[0]);

          if (fLayer.type === "esriFieldTypeString") {
            filterColWhere.push(` ${fLayer.name} LIKE '%${f[1]}%'`);
          } else if (
            [
              "esriFieldTypeInteger",
              "esriFieldTypeDouble",
              "esriFieldTypeSmallInteger",
            ].includes(fLayer.type)
          ) {
            let value = typeof f[1] === "object" ? f[1].value : f[1];
            filterColWhere.push(` ${fLayer.name} = ${value}`);
          } else {
            //date

            filterColWhere.push(
              `(${fLayer.name} >= ${convertHirjiDateToTimeSpan(f[1])})`
            );
            filterColWhere.push(
              `(${fLayer.name} <= ${convertHirjiDateToTimeSpan(f[1], true)})`
            );
          }
        });
        if (filterColWhere.length) {
          let attrTblWhereClause = depTblColumns
            ? filterWhereClauseRef.current.dep.filtered
              ? filterWhereClauseRef.current.dep.filtered
              : filterWhereClauseRef.current.dep.default
            : filterWhereClauseRef.current.current;
          whereClauseToGetData =
            attrTblWhereClause !== "1=1"
              ? "(" +
              attrTblWhereClause +
              ") AND (" +
              filterColWhere.join(" AND ") +
              ")"
              : "(" + filterColWhere.join(" AND ") + ")";

          if (whereClauseToGetData !== attrTblWhereClause) {
            showLoading(true);
            getLayerDataForTable({
              layerID,
              layerData: reqLayerData,
              isNewTbl: true,
              callBackFunc: () => notificationMessage(t("common:filteredSucc")),
              where: whereClauseToGetData,
              colsIsNeeded: false,
              isDependData: depTblColumns ? depTableData.layerData : false,
            });
            let settedFilterWhere = depTblColumns
              ? {
                ...colFilterWhere,
                dep: filterColWhere,
              }
              : {
                ...colFilterWhere,
                current: filterColWhere,
              };
            setColFilterWhere(settedFilterWhere);
          } else showLoading(false);
        }
      } else {
        //in case of reset filter if just one column was filtered

        // if (colFilterWhere.length) {
        //   let attrTblWhereClause = depTblColumns
        //     ? filterWhereClauseRef.current.dep.filtered
        //       ? filterWhereClauseRef.current.dep.filtered
        //       : filterWhereClauseRef.current.dep.default
        //     : filterWhereClauseRef.current.current;
        //   showLoading(true);
        //   getLayerDataForTable({
        //     layerID,
        //     layerData: reqLayerData,
        //     isNewTbl: true,
        //     callBackFunc: () => notificationMessage("تم الفلترة بنجاح"),
        //     where: attrTblWhereClause,
        //     colsIsNeeded: false,
        //     isDependData: depTblColumns ? depTableData.layerData : false,
        //   });
        //   setColFilterWhere({
        //     current: [],
        //     dep: [],
        //   });
        // } else {
        resetOnlyRestColFilter();
        // }
      }
    }
  };

  const getDepData = (dependencyData, record) => {
    clearFeatures();
    let attributes = record;
    let filteredValue = "";
    let isDepChanged =
      depTableData.layerData?.layerName &&
      dependencyData.name &&
      depTableData.layerData?.layerName === dependencyData.name;

    debugger;
    if (
      dependencyData.name == "Landbase_Parcel" &&
      dependencyData.tooltip == "gisAkarReportBtn"
    ) {
      localStorage.setItem(
        "akarReportLandId",
        `PARCEL_SPATIAL_ID=${record?.["PARCEL_SPATIAL_ID"]}`
      );
      window.open(
        `${window.location.origin}/eexplorer` + dependencyData?.redirectURL,
        "_blank",
        "rel=noopener noreferrer"
      );
      return;
    }

    if (isDepChanged) {
      setCurrentPageNum({ ...currentPageNum, dep: 1 });
      setColFilterWhere({ ...colFilterWhere, dep: [] });
    }
    debugger;
    if (
      Object.keys(attributes).includes(`${dependencyData.filter}` + "_Code")
    ) {
      filteredValue = convertNumbersToEnglish(
        attributes[`${dependencyData.filter}` + "_Code"]
      );
    } else
      filteredValue = convertNumbersToEnglish(
        attributes[dependencyData.filter]
      );

    //put condition if(LGR_ROYAL or PARCEL_PRIVACY,KROKY_SUBMISSIONS,FARZ_SUBMISSIONS,CONTRACT_UPDATE_SUBMISSIONS)
    if (
      [
        "LGR_ROYAL",
        "PARCEL_PRIVACY",
        "SALES_LANDS",
        "KROKY_SUBMISSIONS",
        "FARZ_SUBMISSIONS",
        "CONTRACT_UPDATE_SUBMISSIONS",
        "ZAWAYED_SUBMISSIONS",
        "SERVICE_PROJECTS_SUBMISSIONS",
      ].includes(dependencyData.name)
    ) {
      showLoading(true);
      let token = props.mainData.user?.token;
      let requestURL = "";
      if (["LGR_ROYAL", "PARCEL_PRIVACY", "SALES_LANDS"].includes(dependencyData.name)) {
        requestURL = dependencyData.url + filteredValue;
      } else {
        let parcel_no = record["PARCEL_PLAN_NO"];
        let mun_no = record["MUNICIPALITY_NAME_Code"];
        let plan_no =
          record["PLAN_NO"] && record["PLAN_NO"] !== t("common:notFoundValue")
            ? record["PLAN_NO"]
            : "";
        let block_no =
          record["PARCEL_BLOCK_NO"] &&
            record["PARCEL_BLOCK_NO"] !== t("common:notFoundValue")
            ? record["PARCEL_BLOCK_NO"]
            : "" || "";
        let subdivision_type = record["SUBDIVISION_TYPE_Code"] || "";
        let subdivision_Desc =
          record["SUBDIVISION_DESCRIPTION"] &&
            record["SUBDIVISION_DESCRIPTION"] !== t("common:notFoundValue")
            ? record["SUBDIVISION_DESCRIPTION"]
            : "" || "";

        requestURL =
          dependencyData.url +
          `?Parcel_no=${parcel_no}&Mun_code=${mun_no}&plan_no=${plan_no}&block_no=${block_no}&subdivision_Desc=${subdivision_Desc}&subdivision_type=${subdivision_type}`;
      }
      axios
        .get(requestURL, {
          headers: {

            Authorization: `Bearer ${token}`
          }
        })
        .then((res) => {
          let data = res.data || [];
          if (['PARCEL_PRIVACY', 'SALES_LANDS'].includes(dependencyData.name)) data = data?.results || [];

          setCountNumber({
            ...countNumberRef.current,
            dep: data.length,
          });
          setTotalArea({
            ...totalAreaRef.current,
            dep: null,
          });
          countNumberRef.current = {
            ...countNumberRef.current,
            dep: data.length,
          };
          totalAreaRef.current = {
            ...totalAreaRef.current,
            dep: null,
          };
          let isDepPrivateRoyalSales = ["LGR_ROYAL", "PARCEL_PRIVACY", "SALES_LANDS"].includes(
            dependencyData.name
          );
          let outFields = isDepPrivateRoyalSales && props.mainData.layers[dependencyData.name].fields
            ? props.mainData.layers[dependencyData.name].fields
            : props.mainData.layers[dependencyData.name].outFields;
          let tblCols = outFields
            .filter(
              (item) =>
                item !== "OBJECTID" &&
                !item?.toString()?.includes("_SPATIAL_ID")
            )
            .map((f, index) => {
              let rowProp = {
                filtered: false,
                title: t(`layers:${f.alias}`),
                dataType: "esriFieldTypeString",
                // withDomain: f.domain,
                dataIndex: f.name,
                key: f.name,
                sorter: true,
                sortOrder: false,
                filteredValue: undefined,
                defaultFilteredValue: undefined,
                filterResetToDefaultFilteredValue: true,
                // ...setFiltersIntoTable(f, queriedLayerData),
                sortDirections: ["ascend", "descend", "ascend"],
                showSorterTooltip: true,
              };
              if (f.isAnchor) {
                rowProp.render = (txt, { id }) => {
                  return (
                    <a
                      href={dependencyData.workflowUrl + id + `?tk=${token}`}
                      target="_blank"
                      rel="noreferrer">
                      {txt}
                    </a>
                  );
                };
              } else if (f.isHijriDateFormat) {
                rowProp.render = (txt) => getDateFromConcatNumbers(txt)
              }
              if (!index) rowProp.width = "max-content";
              return {
                ...rowProp,
              };
            });

          let tblDataWithNotFoundValues = data?.map((row) => {
            tblCols.forEach((col) => {
              if (col.key.includes("_dateh")) {
                let hijriDate =
                  row[col.key] && isNumber(row[col.key]) && (row[col.key].includes('-') || row[col.key].includes('/'))
                    ? row[col.key]?.toString()?.length > 6
                      ? toArabic(moment(row[col.key]).format("iYYYY/iM/iD"))
                      : toArabic(moment(row[col.key]).format("iYYYY/iM/iD"))
                    : row[col.key] && !(row[col.key].includes('-') || row[col.key].includes('/')) ?
                      getDateFromConcatNumbers(row[col.key])
                      : t("common:notFoundValue");
                //use convertEngNumbersToArabic method in utilsFunc to show it in arabic numbers
                row[col.key] = hijriDate;
              } else
                row[col.key] =
                  typeof row[col.key] === "string"
                    ? // if string
                    row[col.key].trim()
                      ? showDataSplittedBySlash(row[col.key])
                      : t("common:notFoundValue")
                    : //if not string
                    row[col.key]
                      ? showDataSplittedBySlash(row[col.key])
                      : t("common:notFoundValue");
            });
            return row;
          }) || [];

          setDepTableData({
            data: tblDataWithNotFoundValues,
            layerData: dependencyData.name,
          });
          setDepTblColumns(tblCols);
          showLoading(false);
        })
        .catch((err) => {
          console.log(err);
          showLoading(false);
          if (err?.response?.status === 401) {
            //logut
            notificationMessage(t("common:sessionFinished"), 5);
            localStorage.removeItem("user");
            localStorage.removeItem("token");
            window.open(window.hostURL + "/home/<USER>", "_self");
          } else notificationMessage(t("common:retrievError"), 5);
        });
    }
    else if (dependencyData?.depName === 'LandStatistics') {
      setPlanDataModal({
        PLAN_SPATIAL_ID: record?.PLAN_SPATIAL_ID,
        PLAN_NO: record?.PLAN_NO ? convertNumbersToEnglish(record?.PLAN_NO) : record?.PLAN_NO, MUNICIPALITY_NAME: record?.MUNICIPALITY_NAME_Code
      })
    } else if (dependencyData?.depName === 'districtLandStatistics') {
      setDistrictDataModal({
        value: record?.DISTRICT_NAME,
        DISTRICT_NAME: record?.DISTRICT_NAME_Code,
        MUNICIPALITY_NAME: record?.MUNICIPALITY_NAME_Code
      })
    }
    else {
      debugger;
      let whereClause =
        dependencyData.filterDataType === "esriFieldTypeString" && dependencyData.filter
          ? `${dependencyData.filter}='${filteredValue}'`
          : `${dependencyData.filter}=${filteredValue}`;
      let layerID = getLayerId(props.map.__mapInfo, dependencyData.name);
      let layerData = Object.entries(props.mainData.layers)
        ?.filter((l) => l[0] === dependencyData.name)
        ?.map((l) => {
          return {
            layerName: l[0],
            layerMetadata: l[1],
          };
        });
      if (layerData.length) layerData = layerData[0];
      // let layerData = layersNames.find(
      //   (l) => l.layerName === dependencyData.name
      // );
      filterWhereClauseRef.current = {
        ...filterWhereClauseRef.current,
        dep: {
          default: whereClause,
          filtered: "",
        },
      };
      getLayerDataForTable({
        layerID,
        layerData,
        isNewTbl: true,
        // callBackFunc,
        where: whereClause,
        // orderByFields:layerData,
        colsIsNeeded: true,
        isDependData: layerData,
      });
    }
  };
  const handleBackClick = () => {
    clearGraphics(["ZoomGraphicLayer", "highlightGraphicLayer"], props.map);
    if (tableData.length) {
      let features = tableData.filter((d) => {
        if (d.geometry?.rings || d.geometry?.paths || d.geometry?.x) return d;
        else return undefined;
      });
      highlightFeature(features, props.map, {
        layerName: "highlightGraphicLayer",
        isZoom: true,
        zoomDuration: 1000,
      });
    }
    setDepTableData({
      data: [],
      layerData: "",
    });
    setDepTblColumns();
    setCountNumber({ ...countNumber, dep: 0 });
    countNumberRef.current = {
      ...countNumberRef.current,
      dep: 0,
    };
    setTotalArea({ ...totalArea, dep: null });
    totalAreaRef.current = {
      ...totalAreaRef.current,
      dep: null,
    };
    setCurrentPageNum({ ...currentPageNum, dep: 1 });
    filterWhereClauseRef.current = {
      ...filterWhereClauseRef.current,
      dep: {
        default: "",
        filtered: "",
      },
    };
  };
  const CustomtableRow = ({
    dataIndex,
    title,
    record,
    index,
    children,
    ...restProps
  }) => {
    let tblDataNumber = depTblColumns
      ? depTableData.data.length
      : tableData.length;
    return (
      <>
        <tr {...restProps}>{children}</tr>

        {tblDataNumber != 0 && index + 1 == tblDataNumber ? (
          countNumber.current !== tblDataNumber &&
            tblColumns.length &&
            !depTblColumns ? (
            props.searchTableDisplay !== "searchTableHidden" ? (
              <tr>
                <PaginationComp
                  currentPageNum={currentPageNum}
                  isCurrent={true}
                  setCurrentPageNum={setCurrentPageNum}
                />
              </tr>
            ) : null
          ) : depTblColumns ? (
            countNumber.dep !== tblDataNumber ? (
              <tr>
                <PaginationComp
                  currentPageNum={currentPageNum}
                  isCurrent={false}
                  searchTableDisplay={props.searchTableDisplay}
                  setCurrentPageNum={setCurrentPageNum}
                />
              </tr>
            ) : null
          ) : null
        ) : null}
      </>
    );
  };

  ///////////////////////// zoom and draw lines from tbl row to feature on map
  function drawLineWithZoom(feature, event) {
    if (
      feature.geometry &&
      (feature.geometry?.rings?.length ||
        feature.geometry?.x ||
        feature.geometry?.paths)
    ) {
      highlightFeature(feature, props.map, {
        layerName: "SelectGraphicLayer",
        isHiglightSymbol: true,
      });

      drawLine({
        feature: feature,
        map: props.map,
        event: event,
        //condition here depend on css style
        hideFromHeight:
          props.searchTableDisplay == "searchTableShown"
            ? window.innerHeight * 0.6
            : 200,
      });
    }
  }

  const clearFeatures = () => {
    props.map.findLayerById("SelectGraphicLayer").removeAll();
    clearCanvasLine();
  };
  const zoomToFeature = (feature) => {
    if (
      feature.geometry?.rings?.length ||
      feature.geometry?.x ||
      feature.geometry?.paths
    )
      zoomToFeatureDefault(feature, props.map);
  };

  ///////////////////////////////////////////////////////////////

  //reset pagination 
  const resetPagination = () => {
    setCurrentPageNum({
      current: 1,
      dep: 1
    })
  }
  return (
    <div
      className={props.searchTableDisplay}
      id="searchMeta"
      style={{
        right: props.openDrawer ? "275px" : "80px",
        width: props.openDrawer ? "83%" : "93%",
      }}
    >
      <Container fluid className="openMetaHelp">
        {planDataModal && (
          <PlanDataModal
            map={props.map}
            open={planDataModal}
            planData={planDataModal}
            closeModal={() => setPlanDataModal()}
            landbaseParcelData={props.mainData.layers["Landbase_Parcel"]}
            userGroups={props?.mainData?.user?.groups || []}
          />
        )}
        {districtDataModal && (
          <DistrictDataModal
            map={props.map}
            open={districtDataModal}
            districtData={districtDataModal}
            closeModal={() => setDistrictDataModal()}
            landbaseParcelData={props.mainData.layers["Landbase_Parcel"]}
            userGroups={props?.mainData?.user?.groups || []}
          />
        )}
        <Row className="metaRow">
          <Col span={sideDisplay ? 5 : 0}>
            <div className="metaSide">
              <Input
                placeholder={t("common:layerFilter")}
                allowClear
                value={searchText}
                onChange={handleChangeInput}
              />
              <div className="metaSideScroll">
                {searchText !== ""
                  ? displayedLinks.map((layData) => (
                    // <Tooltip title={text.name} placement="left">
                    <div
                      id={layData.layerName}
                      onClick={(e) => openSideLinkData(e, layData)}
                      key={layData.layerName}
                      className={
                        activeLayer.value === layData.layerName
                          ? "activeMetaSide metaSideDiv"
                          : " metaSideDiv"
                      }
                    >
                      {props?.languageState === "ar"
                        ? layData.layerMetadata.arabicName
                        : layData.layerMetadata.englishName}
                    </div>
                    // </Tooltip>
                  ))
                  : layersNames
                    // ?.filter((la) => la.layerMetadata.isSearchable)
                    .map((layer) => (
                      // <Tooltip title={text.name} placement="left">
                      <div
                        id={layer.layerName}
                        key={layer.layerName}
                        onClick={(e) => openSideLinkData(e, layer)}
                        className={
                          activeLayer.value === layer.layerName
                            ? "activeMetaSide metaSideDiv"
                            : " metaSideDiv"
                        }
                      >
                        {props?.languageState === "ar"
                          ? layer.layerMetadata.arabicName
                          : layer.layerMetadata.englishName}
                      </div>
                      // </Tooltip>
                    ))}
              </div>
            </div>
          </Col>{" "}
          {props.searchTableDisplay === "searchTableHidden" ? (
            <DoubleRightOutlined
              onClick={props.openSearchTable}
              className="fas fa-arrow-up tableArrow searchTableArrow"
            ></DoubleRightOutlined>
          ) : (
            <DoubleRightOutlined
              onClick={props.closeSearchTable}
              className="fas fa-arrow-down tableArrow searchTableArrow"
            ></DoubleRightOutlined>
          )}
          {(depTblColumns || tblColumns).length ? (
            <Col span={sideDisplay ? 19 : 24}>
              <div className="tableHeaderIconsDiv">
                {showMetaStat ? (
                  <MetaDataStatistics
                    openMetaStat={openMetaStat}
                    showMetaStat={showMetaStat}
                    exportPDFStatistics={exportPDFStatistics}
                  />
                ) : null}
                <p className="resultsNumber">
                  {sideDisplay ? (
                    <Button onClick={closeSide} className="metaheaderBtn">
                      <DoubleRightOutlined />
                    </Button>
                  ) : (
                    <Button onClick={openSide} className="metaheaderBtn">
                      <DoubleLeftOutlined />
                    </Button>
                  )}
                  <span style={{ fontWeight: "bold" }} className="px-2">
                    {t("map:mapTools.resultNum")}
                  </span>
                  {depTblColumns ? (
                    <span>{convertToArabic(countNumber.dep)}</span>
                  ) : (
                    <span>{convertToArabic(countNumber.current)}</span>
                  )}
                  {(depTblColumns ? totalArea.dep : totalArea.current) ? (
                    <>
                      <span
                        style={{ fontWeight: "bold" }}
                        className="metaHeaderSpacePadding px-2"
                      >
                        {t("map:mapTools.area")}{" "}
                      </span>
                      <span>
                        {convertToArabic((depTblColumns
                          ? totalArea.dep
                          : totalArea.current
                        ).toLocaleString())}{" "}
                        {convertToArabic(t("map:km2"))}
                      </span>
                    </>
                  ) : null}
                </p>

                <div className="tableFiltersButtons">
                  {/* <Button className="tableHeaderBtn "> <Tooltip title={t("map:kroky")} placement="top">
                
                <img className="splitKrokyMetaSVG"
                    src={kroky}
                    style={{
                      cursor: "pointer",
                    }}
                  />
              </Tooltip></Button>
              <Button className="tableHeaderBtn ">
              <Tooltip title={t("map:split")} placement="top">
               
                  <img className="splitKrokyMetaSVG"
                    src={splitIcon}
                    style={{
                      cursor: "pointer",
                    }}
                  />
              </Tooltip></Button>
              <Button className="tableHeaderBtn ">
              <Tooltip title={t("map:updateContract")} placement="top">
              <img className="splitKrokyMetaSVG"
                    src={updateContract}
                    style={{paddingTop:"7px",
                      cursor: "pointer",
                    }}
                  />
              </Tooltip></Button> */}
                  {(depTblColumns &&
                    countNumber.dep &&
                    depTableData.layerData?.layerName === "Landbase_Parcel" &&
                    depTableData?.layerMetadata?.dependencies?.find(
                      (d) => d.name === externalBtnsForTblData.statisticsAttrTbl
                    )) ||
                    (!depTblColumns &&
                      countNumber.current &&
                      activeLayer.value === "Landbase_Parcel" &&
                      activeLayer?.layerData?.layerMetadata?.dependencies?.find(
                        (d) =>
                          d.name === externalBtnsForTblData.statisticsAttrTbl
                      ) && (
                        <Button
                          className="tableHeaderBtn "
                          onClick={openMetaStat}
                        >
                          <Tooltip placement="top" title={t("common:stat")}>
                            <FontAwesomeIcon icon={faChartPie} />
                            {/* <img src={pie_chart_icon} alt="" /> */}
                          </Tooltip>
                        </Button>
                      ))}

                  {depTblColumns && depTableData
                    ? depTableData?.layerMetadata?.dependencies?.find(
                      (d) =>
                        d.name === externalBtnsForTblData.filterAttrTblBtn
                    )
                    : activeLayer?.layerData?.layerMetadata?.dependencies?.find(
                      (d) =>
                        d.name === externalBtnsForTblData.filterAttrTblBtn
                    ) && (
                      <>
                        <Button
                          disabled={[
                            "LGR_ROYAL",
                            "PARCEL_PRIVACY",
                            "SALES_LANDS",
                          ].includes(depTableData?.layerData)}
                          className="tableHeaderBtn "
                          onClick={openFilterModal}
                        >
                          <Tooltip placement="top" title={t("common:filter")}>
                            <FontAwesomeIcon icon={faFilter} />
                            {/* <img src={filter_alt_icon} alt="" /> */}
                          </Tooltip>
                        </Button>
                        <Button
                          disabled={[
                            "LGR_ROYAL",
                            "PARCEL_PRIVACY",
                            "SALES_LANDS",
                          ].includes(depTableData?.layerData)}
                          className="tableHeaderBtn"
                          onClick={resetFilters}
                        >
                          <Tooltip
                            placement="top"
                            title={t("map:cancelFilter")}
                          >
                            {/* <RestartAltIcon /> */}
                            <img src={replay_icon} alt="" />
                          </Tooltip>
                        </Button>

                        <FilteRModal
                          resetPagination={resetPagination}
                          showLoading={showLoading}
                          fields={depTblColumns || tblColumns}
                          isDepend={
                            depTblColumns ? depTableData?.layerData : false
                          }
                          setTblColumns={
                            depTblColumns ? setDepTblColumns : setTblColumns
                          }
                          showFilterModal={showFilterModal}
                          openFilterModal={openFilterModal}
                          reqLayer={
                            depTblColumns
                              ? depTableData.layerData
                              : activeLayer.layerData
                          }
                          map={props.map}
                          getLayerDataForTable={getLayerDataForTable}
                          setColFilterWhere={setColFilterWhere}
                          filterWhereClauseRef={filterWhereClauseRef}
                        />
                      </>
                    )}
                  {/**Export files */}
                  {(depTblColumns &&
                    depTableData?.layerMetadata?.dependencies?.find(
                      (d) => d.name === externalBtnsForTblData.exportAttrTbl
                    )) ||
                    (tblColumns.length &&
                      activeLayer?.layerData?.layerMetadata?.dependencies?.find(
                        (d) => d.name === externalBtnsForTblData.exportAttrTbl
                      )) ? (
                    <ExportFilesComp
                      map={props.map}
                      isDepend={depTblColumns ? true : false}
                      columns={
                        depTblColumns
                          ? depTblColumns
                            ?.filter((c) => c.title)
                            ?.map((c) => c.dataIndex)
                          : tblColumns
                            ?.filter((c) => c.title)
                            ?.map((c) => c.dataIndex)
                      }
                      filterWhereClause={filterWhereClauseRef.current}
                      colFilterWhere={colFilterWhere}
                      dataSet={depTblColumns ? depTableData.data : tableData}
                      labels={
                        depTblColumns
                          ? depTblColumns
                            ?.filter(
                              (c) =>
                                c.title &&
                                !["OBJECTID"].includes(c.dataIndex) &&
                                !c.dataIndex
                                  ?.toString()
                                  .includes("_SPATIAL_ID")
                            )
                            ?.map((c) => c.title)
                          : tblColumns
                            ?.filter(
                              (c) =>
                                c.title &&
                                !["OBJECTID"].includes(c.dataIndex) &&
                                !c.dataIndex
                                  ?.toString()
                                  .includes("_SPATIAL_ID")
                            )
                            ?.map((c) => c.title)
                      }
                      layerData={
                        depTblColumns
                          ? depTableData.layerData
                          : layersNames.length
                            ? activeLayer.layerData
                            : ""
                      }
                    />
                  ) : null}
                  {/****** */}
                  {(depTblColumns
                    ? depTableData
                    : activeLayer.layerData
                  )?.layerMetadata?.dependencies?.find(
                    (d) => d.name === externalBtnsForTblData.zoomBtn
                  ) && (
                      <Button
                        className="tableHeaderBtn"
                        onClick={() =>
                          getLayerExtext(
                            depTblColumns
                              ? depTableData.layerData
                              : activeLayer.layerData
                          )
                        }
                      >
                        <Tooltip
                          placement="top"
                          title={t("map:mapTools.zoomToAll")}
                        >
                          {/* <FontAwesomeIcon icon={faExpandArrowsAlt} /> */}
                          <img src={pan_zoom_icon} alt="" />
                        </Tooltip>
                      </Button>
                    )}
                  {!depTblColumns ? null : (
                    <Button
                      className="tableHeaderBtn"
                      onClick={handleBackClick}
                    >
                      <Tooltip placement="top" title={t("map:mapTools.prev")}>
                        <ArrowBackIcon />
                      </Tooltip>
                    </Button>
                  )}
                </div>
              </div>
              <Table
                components={{
                  body: {
                    row: CustomtableRow,
                  },
                }}
                className="metaTableIcons"
                // class="table table-hover result-table"
                // style={{
                //   borderCollapse: "separate",
                //   borderSpacing: "0px 10px",
                // }}
                // bordered
                locale={{
                  filterTitle: "Filter menu",
                  filterConfirm: "OK",
                  filterReset: "Reset",
                  filterEmptyText: "No filters",
                  filterCheckall: "Select all items",
                  filterSearchPlaceholder: "Search in filters",
                  emptyText: (
                    <h2 style={{ textAlign: "center" }}>
                      {t("common:noResults")}{" "}
                    </h2>
                  ),
                  selectAll: "Select current page",
                  selectInvert: "Invert current page",
                  selectNone: "Clear all data",
                  selectionAll: "Select all data",
                  sortTitle: "Sort",
                  expand: "Expand row",
                  collapse: "Collapse row",
                  triggerDesc: t("map:clickDescSort"),
                  triggerAsc: t("map:clickAscSort"),
                  cancelSort: t("map:cancelSort"),
                }}
                // columns={tblColumns}
                dataSource={depTblColumns ? depTableData.data : tableData}
                pagination={false}
                onChange={onTableChange}
                scroll={{ x: 200, y: 300 }}
                onRow={(record, index) => {
                  return {
                    onMouseMove: (e) => drawLineWithZoom(record, e), // mouse enter row
                    onMouseLeave: clearFeatures, // mouse leave row
                    index,
                  };
                }}
                columns={(depTblColumns || tblColumns).filter(
                  (c) =>
                    !["OBJECTID", "id"].includes(c.dataIndex) &&
                    !c.dataIndex?.toString().includes("_SPATIAL_ID")
                )}
              >
                {/* {setColumnsComps(tblColumns)} */}
              </Table>
            </Col>
          ) : (
            <div className="no-chosen-layer">
              <strong>{t("common:noLayer")}</strong>
              <br />
              <h4>{t("common:chooseLayerToresult")}</h4>
            </div>
          )}
        </Row>
      </Container>
    </div>
  );
}
