import { useTranslation } from "react-i18next";
import ImportFiles from "./ImportFilesComponents/ImportFiles";
import MyFiles from "./ImportFilesComponents/MyFiles";
import tools from "../assets/images/arrodown.svg";
import maps from "../assets/images/docsImg.svg";
import { Tooltip } from "@mui/material";
import { Button } from "antd";
import usePersistentState from "../helper/usePersistentState";
import { useEffect } from "react";

const componentName = "ImportFile";
export default function ImportFile(props) {
  const { t } = useTranslation("common");

  useEffect(() => {
    window.DisableActiveTool();
  }, []);

  /***
   * selected tab
   */
  const [selectedTab, setSelectedTab] = usePersistentState(
    "selectedTab",
    "import_files",
    componentName
  ); // import_files - my_files

  return (
    <div
      className="import_file"
      style={{
        overflow: "auto",
        height: "calc(-85px + 100vh)",
        // paddingLeft: "10px",
        // paddingRight: "10px",
      }}
    >
      <div className="container">
        {/* start tabs */}
        <div
          className="tabs"
          style={{
            display: "flex",
            gap: "10px",
            marginBlock: "20px",
            alignItems: "center",
          }}
        >
          <Tooltip placement="top" title={t("import_files")} className="importFile">
            <Button
              style={{
                background:
                  selectedTab === "import_files"
                    ? "rgba(51, 140, 154, 1)"
                    : "rgba(40, 69, 135, 1)",
                // borderRadius: selectedTab === "import_files" ? "20px" : "50%",
                borderRadius: "20px",
                color: "#fff",
                // padding: "8px",
                border: "none",
                boxShadow: "none",
                padding: "0 10px",
                display: "flex",
                alignItems: "center",
              }}
              onClick={() => setSelectedTab("import_files")}
            >
              <img src={tools} alt="import_files" />
              {selectedTab === "import_files" && (
                <span>{t("import_files")}</span>
              )}
            </Button>
          </Tooltip>

          <Tooltip placement="top" title={t("my_files")}>
            <Button
              style={{
                background:
                  selectedTab === "my_files"
                    ? "rgba(51, 140, 154, 1)"
                    : "rgba(40, 69, 135, 1)",
                // borderRadius: selectedTab === "my_files" ? "20px" : "50%",
                borderRadius: "20px",
                // padding: "8px",
                color: "#fff",
                border: "none",
                boxShadow: "none",
                padding: "0 10px",
                display: "flex",
                alignItems: "center",
              }}
              onClick={() => setSelectedTab("my_files")}
            >
              <img src={maps} alt="my_files" className="importFile_img"/>
              {selectedTab === "my_files" && <span>{t("my_files")}</span>}
            </Button>
          </Tooltip>
        </div>
        {/* end tabs */}

        {selectedTab === "import_files" ? (
          <ImportFiles map={props.map} setFile={props.setFile} />
        ) : (
          <MyFiles map={props.map} />
        )}
      </div>
    </div>
  );
}
