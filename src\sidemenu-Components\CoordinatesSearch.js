import React, { useEffect, useState } from "react";
import { Row, Col, Input, Form, Select, message, Checkbox, Steps } from "antd";
import { Tabs, Tab, Container } from "react-bootstrap";
import { useTranslation } from "react-i18next";

// import Loader from "../containers/Loader";
// import { SearchOutlined } from "@ant-design/icons";
import { DownCircleFilled } from "@ant-design/icons";
import Point from "@arcgis/core/geometry/Point";
import SpatialReference from "@arcgis/core/geometry/SpatialReference";
import {
  SearchOutlined,
  MenuFoldOutlined,
  UnorderedListOutlined,
} from "@ant-design/icons";
import googleLocation from "../assets/icons/google.svg";
import info from "../assets/icons/info.svg";
import locationIcon from "../assets/images/location.gif";
import {
  addPictureSymbol,
  convertToEnglish,
  getLayerId,
  highlightFeature,
  project,
  queryTask,
} from "../helper/common_func";
import arrow_drop_down from "../assets/icons/arrow_drop_down.svg";
import arrow_drop_up from "../assets/icons/arrow_drop_up.svg";
import { isNumber } from "../helper/utilsFunc";
const { Step } = Steps;
export default function CoordinatesSearch(props) {
  const { t } = useTranslation("common");
  useEffect(() => {
    window.DisableActiveTool();
  }, []);
  const [loggedIn] = useState(localStorage.user);
  const [open1, setOpen1] = useState(false);
  const [open2, setOpen2] = useState(false);
  //const [loggedIn] = useState(localStorage.user);
  const [formValues, setFormValues] = useState({
    coordinateWidth: 0,
    coordinateTall: 0,
    lngInMeters: 0,
    latInMeters: 0,
  });
  const [errorMessage, setErrorMessage] = useState(null);
  const [WGS_Geographic, setWGS_Geographic] = useState(null);
  const [WGS_Projected, setWGS_Projected] = useState(null);
  const [Ain_El_Abd_Projected, setAin_El_Abd_Projected] = useState(null);
  const [Ain_El_Abd_Geographic, setAin_El_Abd_Geographic] = useState(null);
  // const [activeStep, setActiveStep] = useState(1);
  const getDecimalDigitsCount = (num) => {
    const numStr = num.toString();
    if (numStr.includes(".")) {
      return numStr.split(".")[1].length;
    }
    return 0;
  };

  const toFixedWithoutRounding = (num, decimals) => {
    const factor = Math.pow(10, decimals);
    return (Math.trunc(num * factor) / factor).toFixed(decimals);
  };

  const handleChange = (digitsNoAfterComma, e) => {
    let isValidNumber = false;
    let isStr = false;
    if (isNumber(e.target.value)) {
      if (getDecimalDigitsCount(e.target.value) <= digitsNoAfterComma) {
        isValidNumber = true;
      }
    } else {
      isStr = true;
    }
    setFormValues({
      ...formValues,
      [e.target.name]: e.target.value
        ? !isStr
          ? (isValidNumber && e.target.value) ||
            toFixedWithoutRounding(+e.target.value, digitsNoAfterComma)
          : e.target.value.match(/(\d+\/?)+/g)[0]
        : "",
    });
  };
  const CoordinateSearch = (e) => {};
  const degSearch = (e) => {};

  const [dropSystem, setdropSystem] = useState(2);
  const [metersType, setMetersType] = useState(1);
  const [dropType, setdropType] = useState(undefined);
  // console.log(props.activeStep)
  const onFinish = async (values) => {
    //props.setActiveStep(3);
    values = formValues;
    // setActiveStep(3)
    setWGS_Projected(null);
    setWGS_Geographic(null);

    console.log(dropSystem);

    let point;
    if (values.x && values.y) {
      point = new Point({
        x: values.x,
        y: values.y,
        spatialReference: new SpatialReference({ wkid: 32639 }),
      });

      if (dropSystem == 3) {
        //ain al abd
        point = new Point({
          x: values.x,
          y: values.y,
          spatialReference: new SpatialReference({ wkid: 20439 }),
        });

        let withinAmanaBoundary = await checkPointInAmana(point);
        debugger;
        if (!withinAmanaBoundary) {
          setAin_El_Abd_Geographic(null);
          setWGS_Geographic(null);
          setWGS_Projected(null);
          setAin_El_Abd_Projected(null);
          return;
        }
        //wgs 84 utm
        project([point], 32639, (res) => {
          setWGS_Projected({ x: res[0].x, y: res[0].y });
        });
      }

      let withinAmanaBoundary = await checkPointInAmana(point);
      debugger;
      if (!withinAmanaBoundary) {
        setAin_El_Abd_Geographic(null);
        setWGS_Geographic(null);
        setWGS_Projected(null);
        setAin_El_Abd_Projected(null);
        return;
      }
      project([point], 102100, (res) => {
        setWGS_Geographic({
          latitude: fromLatLngToDegree(res[0].latitude),
          longitude: fromLatLngToDegree(res[0].longitude),
        });

        addPictureSymbol(
          res[0],
          locationIcon,
          "locationGraphicLayer",
          props.map
        );

        highlightFeature(res[0], props.map, {
          layerName: "ZoomGraphicLayer",
          isZoom: true,
          isZoomOnly: true,
          zoomDuration: 1000,
        });
      });
    } else if (values.latitudeDeg || values.latitudeDec) {
      if (values.latitudeDec) {
        point = zoomToLatLng(values.latitudeDec, values.longitudeDec);
      } else {
        point = zoomToDegreePoint(values);
      }
      let withinAmanaBoundary = await checkPointInAmana(point);
      debugger;
      if (!withinAmanaBoundary) {
        setAin_El_Abd_Geographic(null);
        setWGS_Geographic(null);
        setWGS_Projected(null);
        setAin_El_Abd_Projected(null);
        return;
      }
      //wgs 84 utm
      project([point], 32639, (res) => {
        setWGS_Projected({ x: res[0].x, y: res[0].y });
      });

      //Ain el abd utm
      project([point], 20439, (res) => {
        setAin_El_Abd_Projected({ x: res[0].x, y: res[0].y });
      });
    } else {
      setErrorMessage("من فضلك قم بإدخال الحقول");
      return;
    }

    setErrorMessage(null);

    //wgs84
    if (dropSystem == 2) {
      setWGS_Geographic(null);
      //Ain el abd geographic
      project([point], 4204, (res) => {
        setAin_El_Abd_Geographic({
          latitude: fromLatLngToDegree(res[0].latitude),
          longitude: fromLatLngToDegree(res[0].longitude),
        });
      });
    }
    //Ain el abd
    else if (dropSystem == 3) {
      setAin_El_Abd_Geographic(null);
      //wgs 84 geographic
      project([point], 4326, (res) => {
        setWGS_Geographic({
          latitude: fromLatLngToDegree(res[0].latitude),
          longitude: fromLatLngToDegree(res[0].longitude),
        });
      });
    }
  };

  const checkPointInAmana = (point) => {
    return new Promise((resolve, reject) => {
      project([point], 102100, (res) => {
        let layerdId = getLayerId(props.map.__mapInfo, "Province_Boundary");
        queryTask({
          url: window.mapUrl + "/" + layerdId,
          geometry: res[0],
          outFields: ["OBJECTID"],
          returnGeometry: false,
          callbackResult: ({ features }) => {
            if (!features.length) {
              message.warning("هذة النقطة خارج حدود الأمانة");
              resolve(false);
            } else {
              resolve(true);
            }
          },
        });
      });
    });
  };

  const fromDegreeToLatLng = (values) => {
    let latitudeResult =
      +convertToEnglish(values.latitudeDeg) +
      +convertToEnglish(values.latitudeMinutes) / 60 +
      +convertToEnglish(values.latitudeSeconds) / 3600;
    let longitudeResult =
      +convertToEnglish(values.longitudeDeg) +
      +convertToEnglish(values.longitudeMinutes) / 60 +
      +convertToEnglish(values.longitudeSeconds) / 3600;

    return { latitude: latitudeResult, longitude: longitudeResult };
  };

  const zoomToDegreePoint = (values) => {
    let latitudeResult =
      +convertToEnglish(values.latitudeDeg) +
      +convertToEnglish(values.latitudeMinutes) / 60 +
      +convertToEnglish(values.latitudeSeconds) / 3600;
    let longitudeResult =
      +convertToEnglish(values.longitudeDeg) +
      +convertToEnglish(values.longitudeMinutes) / 60 +
      +convertToEnglish(values.longitudeSeconds) / 3600;

    return zoomToLatLng(latitudeResult, longitudeResult);
  };

  const zoomToLatLng = (lat, lng) => {
    let point = new Point({
      latitude: lat,
      longitude: lng,
    });

    addPictureSymbol(point, locationIcon, "locationGraphicLayer", props.map);

    highlightFeature(point, props.map, {
      layerName: "ZoomGraphicLayer",
      isZoom: true,
      isZoomOnly: true,
      zoomDuration: 1000,
    });

    return point;
  };

  const onPublicUserDecimalSubmit = async (values) => {
    values = formValues;
    if (values.latitudeDec && values.longitudeDec) {
      let point = new Point({
        latitude: convertToEnglish(values.latitudeDec),
        longitude: convertToEnglish(values.longitudeDec),
      });

      let withinAmanaBoundary = await checkPointInAmana(point);
      debugger;
      if (!withinAmanaBoundary) {
        return;
      }
      addPictureSymbol(point, locationIcon, "locationGraphicLayer", props.map);

      highlightFeature(point, props.map, {
        layerName: "ZoomGraphicLayer",
        isZoom: true,
        isZoomOnly: true,
        zoomDuration: 1000,
      });

      //checkPointInAmana(point);
    }
  };

  const fromLatLngToDegree = (angleInDegrees, returnObject) => {
    while (angleInDegrees < -180.0) angleInDegrees += 360.0;

    while (angleInDegrees > 180.0) angleInDegrees -= 360.0;

    var result = {};

    angleInDegrees = Math.abs(angleInDegrees);

    //gets the degree
    result.deg = Math.floor(angleInDegrees);
    var delta = angleInDegrees - result.deg;

    //gets minutes and seconds
    var seconds = 3600.0 * delta;
    result.sec = seconds % 60;
    result.min = Math.floor(seconds / 60.0);

    if (returnObject) {
      return result;
    }

    return (
      "''" +
      (+result.sec).toFixed(3) +
      " '" +
      result.min +
      " " +
      result.deg +
      "° "
    );
    //return result;
  };

  const onPublicUserDegreesSubmit = async (values) => {
    values = formValues;
    // convert (deg, min, sec) to value of (lat,lng)
    let point = zoomToDegreePoint(values);
    //checkPointInAmana(point);
    let withinAmanaBoundary = await checkPointInAmana(point);
    debugger;
    if (!withinAmanaBoundary) {
      return;
    }
  };

  const onChangeCheckBox = (e, type) => {
    setMetersType(type);
    // formValues.latitudeDec = "";
    // formValues.longitudeDec = "";
    // formValues.latitudeDeg = "";
    // formValues.latitudeMinutes = "";
    // formValues.latitudeSeconds = "";
    // formValues.longitudeDeg = "";
    // formValues.longitudeMinutes = "";
    // formValues.longitudeSeconds = "";
  };

  const handleSelect = (name) => (value, e) =>
    e !== undefined
      ? name === "dropSystem"
        ? (setOpen1(false), setdropSystem(e.id), setFormValues({}))
        : name === "dropType"
        ? (setOpen2(false), setdropType(e.id), setFormValues({}))
        : null
      : null;

  return (
    <div className="coordinates">
      {/* {loading ? <Loader /> : null} */}
      {/* {!localStorage.getItem("user") ? (
        <Tabs
          defaultActiveKey="coord"
          id="uncontrolled-tab-example"
          className=""
        >
          <Tab eventKey="coord" title={t("decimalCoor")}>
            <Form
              onFinish={onPublicUserDecimalSubmit}
              className="coordinateForm"
              layout="vertical"
              name="validate_other"
            >
              <Container style={{ display: "grid", gridGap: "10px" }}>
                <div style={{ display: "grid", gridGap: "10px" }} >
                  <Col span={24} className="select-cust">
                    <label className=" "> {t("Latitude")} </label>
                    <Form.Item
                      name="latitude"
                      rules={[
                        {
                          // message: t("LatitudeSelect"),
                          // required: true,
                        },
                      ]}
                    >
                      <Input
                        // type="number"
                        name="latitude"
                        onChange={handleChange}
                        value={formValues.latitude}
                        placeholder={t("Latitude")}
                      />
                    </Form.Item>
                  </Col>
                  <div className="select-cust">
                    <label className> {t("longitude")}</label>
                    <Form.Item
                      rules={[
                        {
                          // message: t("longitudeSelect"),
                          // required: true,
                        },
                      ]}
                      name="longitude"
                    >
                      <Input
                        // type="number"
                        name="longitude"
                        onChange={handleChange}
                        value={formValues.longitude}
                        placeholder={t("longitude")}
                      />
                    </Form.Item>
                  </div>
                </div>
                <div style={{ textAlign: "center" }}>
                  <button
                    icon={<SearchOutlined />}
                    onClick={CoordinateSearch}
                    className="SearchBtn mt-3 w-25"
                    size="large"
                    htmlType="submit"
                  >
                    {t("search")}
                  </button>
                </div>
                {errorMessage && (
                  <div style={{ textAlign: "center" }}>
                    <label style={{ fontSize: "12px", color: "red" }}>
                      {errorMessage}
                    </label>
                  </div>
                )}
              </Container>
            </Form>
          </Tab>

          <Tab eventKey="deg-min" title={t("degMinSec")}>
            <Form
              onFinish={onPublicUserDegreesSubmit}
              className="coordinateForm"
              layout="vertical"
              name="validate_other"
            >
              <Container fluid style={{
                display: "grid",

                gridGap: "10px"
              }}>
                <label className="mt-2 mr-1">{t("Latitude")}</label>
                <Row style={{ gridGap: "10px" }}>
                  <Col span={8} className="select-cust">
                    <Form.Item
                      name="latitudeSeconds"
                      rules={[
                        {
                          message: t("chooseSec"),
                          required: true,
                        },
                      ]}
                    // help="Should be combination of numbers & alphabets"
                    >
                      <Input
                        // type="number"
                        name="latitudeSeconds"
                        onChange={handleChange}
                        value={formValues.latitudeSeconds}
                        placeholder={t("seconds")}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={7} className="select-cust">
                    <Form.Item
                      rules={[
                        {
                          message: t("chooseMin"),
                          required: true,
                        },
                      ]}
                      name="latitudeMinutes"

                    // help="Should be combination of numbers & alphabets"
                    >
                      <Input
                        // type="number"
                        name="latitudeMinutes"
                        onChange={handleChange}
                        value={formValues.latitudeMinutes}
                        placeholder={t("minutes")}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={7} className="select-cust">
                    <Form.Item
                      rules={[
                        {
                          message: t("chooseDeg"),
                          required: true,
                        },
                      ]}
                      name="latitudeDeg"

                    // help="Should be combination of numbers & alphabets"
                    >
                      <Input
                        // type="number"
                        name="latitudeDeg"
                        onChange={handleChange}
                        value={formValues.latitudeDeg}
                        placeholder={t("degrees")}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <label className="mt-2 mr-1">{t("longitude")}</label>
                <Row style={{ gridGap: "10px" }}>
                  <Col span={8} className="select-cust">
                    <Form.Item
                      name="longitudeSeconds"
                      rules={[
                        {
                          message: t("chooseSec"),
                          required: true,
                        },
                      ]}

                    // help="Should be combination of numbers & alphabets"
                    >
                      <Input
                        // type="number"
                        name="longitudeSeconds"
                        onChange={handleChange}
                        value={formValues.longitudeSeconds}
                        placeholder={t("seconds")}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={7} className="select-cust">
                    <Form.Item
                      name="longitudeMinutes"
                      rules={[
                        {
                          message: t("chooseMin"),
                          required: true,
                        },
                      ]}
                    // help="Should be combination of numbers & alphabets"
                    >
                      <Input
                        // type="number"
                        name="longitudeMinutes"
                        onChange={handleChange}
                        value={formValues.longitudeMinutes}
                        placeholder={t("minutes")}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={7} className="select-cust">
                    <Form.Item
                      name="longitudeDeg"
                      rules={[
                        {
                          message: t("chooseDeg"),
                          required: true,
                        },
                      ]}
                    >
                      <Input
                        // type="number"
                        name="longitudeDeg"
                        onChange={handleChange}
                        value={formValues.longitudeDeg}
                        placeholder={t("degrees")}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <div style={{ textAlign: "center" }}>
                  <button
                    icon={<SearchOutlined />}
                    onClick={degSearch}
                    className="SearchBtn mt-3 w-25"
                    size="large"
                    htmlType="submit"
                  >
                    {t("search")}
                  </button>
                </div>
              </Container>
            </Form>
          </Tab>
        </Tabs>
      ) : ( */}
      <Container fluid>
        <Form
          onFinish={
            localStorage.getItem("user")
              ? onFinish
              : metersType == 1
              ? onPublicUserDecimalSubmit
              : onPublicUserDegreesSubmit
          }
          className="coordinateForm"
          layout="vertical"
          name="validate_other"
          style={{ display: "grid", gridGap: "10px 10px" }}
        >
          {loggedIn && (
            <>
              <Form.Item
                label={t("dropSys")}
                name="dropSystem"
                className="select-cust"
              >
                <Select
                  virtual={false}
                  onDropdownVisibleChange={(flag) => setOpen1(flag)}
                  // suffixIcon={<DownCircleFilled />}
                  suffixIcon={
                    open1 ? (
                      <img src={arrow_drop_up} alt="" />
                    ) : (
                      <img src={arrow_drop_down} alt="" />
                    )
                  }
                  showSearch
                  allowClear
                  // defaultValue="WGS-84"
                  className="dont-show"
                  onSelect={() => setOpen1(true)}
                  onChange={handleSelect("dropSystem")}
                  value={dropSystem}
                  placeholder={t("dropSysSelect")}
                  getPopupContainer={(trigger) => trigger.parentNode}
                  onClear={() => setdropSystem(undefined)}
                  optionFilterProp="value"
                  filterOption={(input, option) =>
                    option.value.indexOf(input) >= 0
                  }
                >
                  <Select.Option value="WGS-84" id={2}>
                    WGS-84
                  </Select.Option>
                  <Select.Option value="Ain-El-Abd" id={3}>
                    Ain El-Abd
                  </Select.Option>
                </Select>
              </Form.Item>
              <Form.Item
                className="select-cust"
                label={t("dropType")}
                name="dropType"
                rules={[
                  {
                    message: t("dropTypeSelect"),
                    required: true,
                  },
                ]}
              >
                <Select
                  onDropdownVisibleChange={(flag) => setOpen2(flag)}
                  virtual={false}
                  // suffixIcon={<DownCircleFilled />}
                  suffixIcon={
                    open2 ? (
                      <img src={arrow_drop_up} alt="" />
                    ) : (
                      <img src={arrow_drop_down} alt="" />
                    )
                  }
                  showSearch
                  allowClear
                  className="dont-show"
                  onSelect={() => setOpen2(true)}
                  onChange={handleSelect("dropType")}
                  value={dropType}
                  placeholder={t("dropTypeSelect")}
                  getPopupContainer={(trigger) => trigger.parentNode}
                  optionFilterProp="value"
                  filterOption={(input, option) =>
                    option.value.indexOf(input) >= 0
                  }
                >
                  <Select.Option value={t("geographic")} id={1}>
                    {t("geographic")}
                  </Select.Option>
                  <Select.Option value={t("metric")} id={2}>
                    {t("metric")}
                  </Select.Option>
                </Select>
              </Form.Item>
            </>
          )}

          <div className="checkbox_container">
            <Checkbox
              style={{ fontWeight: "bold" }}
              onChange={(e) => onChangeCheckBox(e, 1)}
              checked={metersType == 1}
            >
              {t("decimalDeg")}{" "}
            </Checkbox>
            <Checkbox
              style={{ fontWeight: "bold" }}
              onChange={(e) => onChangeCheckBox(e, 2)}
              checked={metersType == 2}
            >
              {t("degMinSec")}
            </Checkbox>
          </div>

          {dropType === 1 || !loggedIn ? (
            <>
              <div
                className={metersType == 2 ? "label_size" : "select-cust"}
                style={{ border: metersType == 2 ? "none" : "" }}
              >
                <label className="ant-form-item-label"> {t("Latitude")}</label>
                {metersType == 2 ? (
                  <Row>
                    <Col span={8} className="select-cust_2">
                      <Input
                        // type="number"
                        name="latitudeDeg"
                        onChange={handleChange.bind(this, 0)}
                        value={formValues.latitudeDeg}
                        placeholder={t("degrees")}
                      />
                    </Col>
                    <Col span={7} className="mx-1 select-cust_2">
                      <Input
                        // type="number"
                        name="latitudeMinutes"
                        onChange={handleChange.bind(this, 0)}
                        value={formValues.latitudeMinutes}
                        placeholder={t("minutes")}
                      />
                    </Col>
                    <Col span={7} className="select-cust_2">
                      <Input
                        // type="number"
                        name="latitudeSeconds"
                        onChange={handleChange.bind(this, 1)}
                        value={formValues.latitudeSeconds}
                        placeholder={t("seconds")}
                      />
                    </Col>
                  </Row>
                ) : (
                  <Row>
                    <Col span={19}>
                      <Input
                        // type="number"
                        name="latitudeDec"
                        onChange={handleChange.bind(this, 6)}
                        value={formValues.latitudeDec}
                        placeholder=""
                      />
                    </Col>
                    <Col span={5}>
                      <label
                        className="ant-form-item-label"
                        style={{ fontWeight: "bold", marginRight: "10px" }}
                      >
                        {" "}
                        {t("north")} °
                      </label>
                    </Col>
                  </Row>
                )}
              </div>
              <div
                className={metersType == 2 ? "label_size" : "select-cust"}
                style={{ border: metersType == 2 ? "none" : "" }}
              >
                <label className="ant-form-item-label"> {t("longitude")}</label>

                {metersType == 2 ? (
                  <Row>
                    <Col span={8} className="select-cust_2">
                      <Input
                        // type="number"
                        name="longitudeDeg"
                        onChange={handleChange.bind(this, 0)}
                        value={formValues.longitudeDeg}
                        placeholder={t("degrees")}
                      />
                    </Col>
                    <Col span={7} className="mx-1 select-cust_2">
                      <Input
                        // type="number"
                        name="longitudeMinutes"
                        onChange={handleChange.bind(this, 0)}
                        value={formValues.longitudeMinutes}
                        placeholder={t("minutes")}
                      />
                    </Col>
                    <Col span={7} className="select-cust_2">
                      <Input
                        // type="number"
                        name="longitudeSeconds"
                        onChange={handleChange.bind(this, 1)}
                        value={formValues.longitudeSeconds}
                        placeholder={t("seconds")}
                      />
                    </Col>
                  </Row>
                ) : (
                  <Row>
                    <Col span={19}>
                      <Input
                        // type="number"
                        name="longitudeDec"
                        onChange={handleChange.bind(this, 6)}
                        value={formValues.longitudeDec}
                        placeholder=""
                      />
                    </Col>
                    <Col span={5}>
                      <label
                        className="ant-form-item-label"
                        style={{ fontWeight: "bold", marginRight: "10px" }}
                      >
                        {" "}
                        {t("east")} °
                      </label>
                    </Col>
                  </Row>
                )}
              </div>
            </>
          ) : dropType === 2 || !loggedIn ? (
            <>
              <div style={{ display: "grid", gridGap: "10px" }}>
                <Col span={24} className="select-cust">
                  <div style={{ textAlign: "justify" }}>
                    <label style={{ fontWeight: "bold" }}> {t("xCoor")}</label>
                  </div>
                  <Input
                    // type="number"
                    name="x"
                    onChange={handleChange}
                    value={formValues.x}
                    placeholder={t("xCoor")}
                  />
                </Col>
                <Col span={24} className="select-cust">
                  <div style={{ textAlign: "justify", marginTop: "10px" }}>
                    <label style={{ fontWeight: "bold" }}> {t("yCoor")}</label>
                  </div>
                  <Input
                    // type="number"
                    name="y"
                    onChange={handleChange}
                    value={formValues.y}
                    placeholder={t("yCoor")}
                  />
                </Col>
              </div>
            </>
          ) : null}
          {(dropSystem !== undefined && dropType !== undefined) || !loggedIn ? (
            <div style={{ textAlign: "center" }}>
              <button
                icon={<SearchOutlined />}
                className="SearchBtn mt-3 w-25"
                size="large"
                htmlType="submit"
              >
                {t("search")}
              </button>
            </div>
          ) : null}

          {WGS_Geographic && (
            <div className="coordinate_search">
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <p className="coordinateData">WGS ( Geographic )</p>
                {/* <div>
                    <img src={info} />
                    <img src={googleLocation} />
                  </div> */}
              </div>
              <table className="table table-bordered">
                <tr>
                  <td>
                    <span> {t("Latitude")}</span>
                  </td>
                  <td>
                    <span>{WGS_Geographic.latitude}</span>
                  </td>
                </tr>
                <tr>
                  <td>
                    <span> {t("longitude")}</span>
                  </td>
                  <td>
                    <span>{WGS_Geographic.longitude}</span>
                  </td>
                </tr>
              </table>
            </div>
          )}
          {WGS_Projected && (
            <div className="coordResult_table">
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <p className="coordinateData">WGS Projected ( UTM )</p>
                {/* <div>
                    <img src={info} />
                    <img src={googleLocation} />
                  </div> */}
              </div>
              <table className="table table-bordered">
                <tr>
                  <td>
                    <span> {t("xCoor")}</span>
                  </td>
                  <td>
                    <span>{WGS_Projected.x}</span>
                  </td>
                </tr>
                <tr>
                  <td>
                    <span>{t("yCoor")}</span>
                  </td>
                  <td>
                    <span>{WGS_Projected.y}</span>
                  </td>
                </tr>
              </table>
            </div>
          )}

          {Ain_El_Abd_Projected && (
            <div className="coordResult_table">
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <p className="coordinateData">Ain El-Abd Projected ( UTM )</p>
                {/* <div>
                    <img src={info} />
                    <img src={googleLocation} />
                  </div> */}
              </div>
              <table className="table table-bordered">
                <tr>
                  <td>
                    <span> {t("xCoor")}</span>
                  </td>
                  <td>
                    <span>{Ain_El_Abd_Projected.x}</span>
                  </td>
                </tr>
                <tr>
                  <td>
                    <span> {t("yCoor")} </span>
                  </td>
                  <td>
                    <span>{Ain_El_Abd_Projected.y}</span>
                  </td>
                </tr>
              </table>
            </div>
          )}

          {Ain_El_Abd_Geographic && (
            <div className="coordResult_table">
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <p className="coordinateData">Ain El-Abd ( Geographic )</p>
                {/* <div>
                    <img src={info} />
                    <img src={googleLocation} />
                  </div> */}
              </div>
              <table className="table table-bordered">
                <tr>
                  <td>
                    <span> {t("Latitude")} </span>
                  </td>
                  <td>
                    <span>{Ain_El_Abd_Geographic.latitude}</span>
                  </td>
                </tr>
                <tr>
                  <td>
                    <span> {t("longitude")}</span>
                  </td>
                  <td>
                    <span>{Ain_El_Abd_Geographic.longitude}</span>
                  </td>
                </tr>
              </table>
            </div>
          )}
        </Form>
        {errorMessage && (
          <div style={{ textAlign: "center" }}>
            <label style={{ fontSize: "12px", color: "red" }}>
              {errorMessage}
            </label>
          </div>
        )}
      </Container>
      {/* )} */}
    </div>
  );
}
