import React, {
  useState,
  useEffect,
  useContext,
  useRef,
  useCallback,
} from "react";
import { Input, Form, Tooltip } from "antd";
import nearbyIcon from "../assets/images/outerSearchIcon.svg";
import searchIcon from "../assets/images/searchIcon.svg";
import searchIcon2 from "../assets/images/searchIcon2.svg";
import activeNearByIcon from "../assets/images/outerSearchActive.svg";
import {
  addPictureSymbol,
  getFeatureDomainName,
  getLayerId,
  makeIdentify,
  queryTask,
  showLoading,
  convertToEnglish,
} from "../helper/common_func";
// import { layersSetting } from "../helper/layers";

import { CiLocationArrow1 } from "react-icons/ci";

import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Fade from "react-reveal";
let displayed = [];
export default function OuterSearchForm(props) {
  const { t, i18n } = useTranslation("common");
  const timeoutObj = {};
  const location = useLocation();
  const [displayedOuterSearch, setDisplayedOuterSearch] = useState([]);
  const [outerAutoComplete, setOuterComplete] = useState(true);
  const [outerSearchText, setOuterSearchText] = useState("");
  const [isActiveBufferSearch, setActiveBufferSearch] = useState(false);

  const componentRef = useRef({});
  const formRef = useRef();
  const { current: timeout } = componentRef;
  const selectedTabsIndex = ["Landbase_Parcel", "Plan_Data", "Street_Naming"];
  const searchfields = ["PARCEL_PLAN_NO", "PLAN_NO", "STREET_FULLNAME"];

  let navigate = useNavigate();
  const [isExpanded, setIsExpanded] = useState(false);
  const [isActive, setIsActive] = useState(false);
  const [showContent, setShowContent] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [districtsData, setDistrictsData] = useState([]);
  const [searchResult, setSearchResult] = useState([]);
  const [isSearchInMapExtend, setIsSearchInMapExtend] = useState(false);
  const [isSearchPending, setIsSearchPending] = useState(false);
  const [shouldClearCache, setShouldClearCache] = useState(false);
  const [loadedLayersNames, setLoadedLayersNames] = useState([]);
  const [loadedLayerIds, setLoadedLayerIds] = useState([]);
  const [searchResultEmpty, setSearchResultEmpty] = useState(false);
  const inputRef = useRef(null);
  const timeoutRef = useRef(null);
  const lastSearchValueRef = useRef("");

  // Maximum number of search results
  const MAX_SEARCH_RESULTS = 5000;
  class SearchCache {
    constructor(maxSize = 100, expirationMs = 30 * 60 * 1000) {
      this.cache = new Map();
      this.maxSize = maxSize;
      this.expirationMs = expirationMs;
    }

    // Modified set method to include geometry context
    set(key, value, geometry = null) {
      if (this.cache.size >= this.maxSize) {
        const oldestKey = this.cache.keys().next().value;
        this.cache.delete(oldestKey);
      }
      this.cache.set(key, {
        value,
        timestamp: Date.now(),
        geometry, // Store the geometry context
      });
    }

    // Modified get method to check geometry context
    get(key, currentGeometry = null) {
      const entry = this.cache.get(key);
      if (!entry) return null;

      if (Date.now() - entry.timestamp > this.expirationMs) {
        this.cache.delete(key);
        return null;
      }

      // If the search is map-extent constrained, only return cached results
      // if they were fetched with the same geometry constraint
      if (
        currentGeometry &&
        (!entry.geometry ||
          JSON.stringify(entry.geometry) !== JSON.stringify(currentGeometry))
      ) {
        return null;
      }

      return entry.value;
    }

    clear() {
      this.cache.clear();
    }
  }
  const searchCacheRef = useRef({
    lands: new Map(),
    plans: new Map(),
    streets: new Map(),
  });

  const searchCache = {
    lands: new SearchCache(),
    plans: new SearchCache(),
    streets: new SearchCache(),
  };
  const clearSearchCache = useCallback(() => {
    console.log(searchResult);

    console.log("Clearing search cache...");
    if (searchCacheRef.current) {
      searchCacheRef.current.lands.clear();
      searchCacheRef.current.plans.clear();
      console.log("Cache cleared successfully");
      console.log(searchCacheRef.current);
    } else {
      console.log("Cache not found");
    }
  }, []);

  // Function to pass to TopSearchContent
  const clearSearchBeforeToggle = useCallback(() => {
    lastSearchValueRef.current = "";
    setIsExpanded(false);
  }, []);

  // Pre-compile regex patterns for better performance
  const REGEX = {
    LAND_PATTERN: /^(\d+|(?:ارض|أرض)\s*(?:رقم\s*)?(\d+))$/,
    SLASH_PATTERN: /^[^A-Za-z\u0600-\u06FF]*\/[^A-Za-z\u0600-\u06FF]*$/,
    PLAN_PATTERN: /^(?:مخطط\s*(?:رقم\s*)?(\d+))/,
    HAS_LETTERS:
      /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\u0041-\u005A\u0061-\u007A]/,
    HAS_NUMBERS: /\d/,
    NUMBERS_ONLY: /^\d+$/,
  };

  // Updated getSearchType to handle all cases
  const getSearchType = (value) => {
    if (!value) return null;

    // Check for slash pattern first
    if (REGEX.SLASH_PATTERN.test(value)) {
      return "lands_and_plans";
    }

    // Check for numbers-only pattern
    if (REGEX.NUMBERS_ONLY.test(value)) {
      return "lands_and_streets_and_plans";
    }

    // Check for text content
    if (REGEX.HAS_LETTERS.test(value)) {
      return "plans_and_streets";
    }

    // Default case
    return "plans";
  };

  // Quick check function for search type
  const calculateDistance = (center, geometry) => {
    const dx = center.x - (geometry.x || geometry.centroid?.x);
    const dy = center.y - (geometry.y || geometry.centroid?.y);
    return Math.sqrt(dx * dx + dy * dy) / 1000;
  };
  // const getSearchType = (value) => {
  //   if (!value) return null;
  //   if (REGEX.LAND_PATTERN.test(value)) return "land";
  //   else if (REGEX.SLASH_PATTERN.test(value)) return "both";
  //   else if (REGEX.PLAN_PATTERN.test(value)) return "plan";
  //   else return "plan";
  // };

  const fetchLands = async (searchText, useMapExtent, querySettings = {}) => {
    if (!useMapExtent) {
      const cacheKey = `lands_${searchText}_${
        querySettings.maxRecordCount || 20
      }`;
      const cached = searchCache.lands.get(cacheKey);
      if (cached) {
        return cached.slice(0, querySettings.maxRecordCount || cached.length);
      }
    }

    const currentExtent = useMapExtent ? props.map.view.extent : null;
    const layerIndex = getLayerId(props.map.__mapInfo, "Landbase_Parcel");
    console.log("layerIndex", layerIndex);

    const settings = {
      url: window.mapUrl + `/${layerIndex}`,
      outFields: ["*"],
      // [
      //   "OBJECTID",
      //   "MUNICIPALITY_NAME",
      //   "SUB_MUNICIPALITY_NAME",
      //   "PLAN_NO",
      //   "PARCEL_PLAN_NO",
      //   "DISTRICT_NAME",
      //   "PARCEL_MAIN_LUSE",
      //   "PARCEL_SUB_LUSE",
      //   "PARCEL_SPATIAL_ID",
      // ]
      where: `PARCEL_PLAN_NO = '${searchText}'`,
      returnGeometry: true,
      geometry: currentExtent,
      ...querySettings,
    };

    return new Promise((resolve) => {
      queryTask({
        ...settings,
        callbackResult: async ({ features }) => {
          const results = await processFeatures(features, layerIndex, "land");
          if (!useMapExtent) {
            const cacheKey = `lands_${searchText}`;
            searchCacheRef.current.lands.set(cacheKey, results);
          }
          resolve(
            results.slice(0, querySettings.maxRecordCount || results.length)
          );
        },
      });
    });
  };
  // Fetch plans with query settings
  const fetchPlans = async (searchText, useMapExtent, querySettings = {}) => {
    if (!useMapExtent) {
      const cacheKey = `plans_${searchText}_${
        querySettings.maxRecordCount || 20
      }`;
      const cached = searchCacheRef.current.plans.get(cacheKey);
      if (cached) {
        return cached.slice(0, querySettings.maxRecordCount || cached.length);
      }
    }

    const currentGeometry = useMapExtent ? props.map.view.extent : null;
    const layerIndex = getLayerId(props.map.__mapInfo, "Plan_Data");
    const settings = {
      url: `${window.mapUrl}/${layerIndex}`,
      outFields: ["*"],
      where: `PLAN_NO = '${searchText}' OR PLAN_NO LIKE '%${searchText}%'`,
      returnGeometry: true,
      geometry: currentGeometry,
      ...querySettings,
    };

    return new Promise((resolve) => {
      queryTask({
        ...settings,
        callbackResult: async ({ features }) => {
          const results = await processFeatures(features, layerIndex, "plan");
          if (!useMapExtent) {
            const cacheKey = `plans_${searchText}`;
            searchCacheRef.current.plans.set(cacheKey, results);
          }
          resolve(
            results.slice(0, querySettings.maxRecordCount || results.length)
          );
        },
      });
    });
  };

  // Fetch streets with query settings
  const fetchStreets = async (searchText, useMapExtent, querySettings = {}) => {
    if (!useMapExtent) {
      const cacheKey = `streets_${searchText}_${
        querySettings.maxRecordCount || 20
      }`;
      const cached = searchCacheRef.current.streets.get(cacheKey);
      if (cached) {
        return cached.slice(0, querySettings.maxRecordCount || cached.length);
      }
    }

    const currentExtent = useMapExtent ? props.map.view.extent : null;
    const layerIndex = getLayerId(props.map.__mapInfo, "Street_Naming");
    const searchConditions = [
      `STREET_FULLNAME = '${searchText}'`, // Exact match
      `STREET_FULLNAME LIKE '${searchText}%'`, // Starts with
      `STREET_FULLNAME LIKE '%${searchText}%'`, // Contains
    ];

    const settings = {
      url: window.mapUrl + `/${layerIndex}`,
      outFields: [
        "STREET_FULLNAME",
        "MUNICIPALITY_NAME",
        "SUB_MUNICIPALITY_NAME",
        "DISTRICT_NAME",
        "OBJECTID",
      ],
      where: searchConditions.join(" OR "),
      returnGeometry: true,
      geometry: currentExtent,
      orderByFields: ["STREET_FULLNAME ASC"],
      ...querySettings,
    };

    return new Promise((resolve) => {
      queryTask({
        ...settings,
        callbackResult: async ({ features }) => {
          const results = await processFeatures(features, layerIndex, "street");
          if (!useMapExtent) {
            const cacheKey = `streets_${searchText}`;
            searchCacheRef.current.streets.set(cacheKey, results);
          }
          resolve(
            results.slice(0, querySettings.maxRecordCount || results.length)
          );
        },
      });
    });
  };

  // Helper function to process features (used by all fetch functions)
  const processFeatures = async (features, layerIndex, type) => {
    const rfeatures = await getFeatureDomainName(features, layerIndex);

    return rfeatures?.map((feature) => ({
      attributes: feature.attributes,
      geometry: feature.geometry,

      searchType: type,
    }));
  };

  // Execute search with explicit extent parameter
  // Updated executeSearch with all search combinations

  const executeSearch = useCallback(async (value, useMapExtent) => {
    const normalizedValue = value.replace(/\s+/g, " ").trim();

    if (!normalizedValue) {
      setSearchResult([]);
      return;
    }

    setIsSearchPending(true);
    try {
      const searchType = getSearchType(normalizedValue);
      let results = [];

      // Set limit to 20 records per type
      const querySettings = {
        maxRecordCount: 20,
        num: 20,
        start: 0,
      };

      switch (searchType) {
        case "lands_and_plans":
          const [landResults1, planResults1] = await Promise.all([
            fetchLands(normalizedValue, useMapExtent, querySettings),
            fetchPlans(normalizedValue, useMapExtent, querySettings),
          ]);
          results = [
            { features: landResults1, layerId: 0 },
            { features: planResults1, layerId: 1 },
          ];
          break;

        case "lands_and_streets_and_plans":
          const [planResults2, landResults2, streetResults1] =
            await Promise.all([
              fetchPlans(normalizedValue, useMapExtent, querySettings),
              fetchLands(normalizedValue, useMapExtent, querySettings),
              fetchStreets(normalizedValue, useMapExtent, querySettings),
            ]);
          results = [
            { features: landResults2, layerId: 0 },
            { features: planResults2, layerId: 1 },
            { features: streetResults1, layerId: 2 },
          ];
          break;

        case "plans_and_streets":
          const normalizedPlanValue = normalizedValue
            .replace(/(?:مخطط|رقم)\s*/g, "")
            .trim();

          if (normalizedPlanValue.length >= 3) {
            const [planResults3, streetResults2] = await Promise.all([
              fetchPlans(normalizedPlanValue, useMapExtent, querySettings),
              fetchStreets(normalizedValue, useMapExtent, querySettings),
            ]);
            results = [
              { features: planResults3, layerId: 1 },
              { features: streetResults2, layerId: 2 },
            ];
          } else {
            let streetResults1 = await fetchStreets(
              normalizedValue,
              querySettings
            );
            results = [{ features: streetResults1, layerId: 2 }];
          }
          break;

        default:
          if (normalizedValue.length >= 3) {
            let planResults1 = await fetchPlans(
              normalizedValue,
              useMapExtent,
              querySettings
            );
            results = [{ features: planResults1, layerId: 1 }];
          }
          break;
      }

      // if (results.length > 0) {
      //   let attributesList = results.map((r) => r.attributes);

      //   results = results.map((r, index) => ({
      //     ...r,
      //     attributes: attributesList[index],
      //   }));
      // }

      // setSearchResult(results);
      // setSearchResultEmpty(results.length === 0);
      //  setDisplayedOuterSearch(results);
      //  debugger;
      var allResult = [];
      let filteredResultLength = results.length;

      if (filteredResultLength) {
        const domainPromises = results.map(async (result) => {
          if (!result) return [];

          const layerId = getLayerId(
            props.map.__mapInfo,
            selectedTabsIndex[result.layerId]
          );

          const res = await getFeatureDomainName(result.features, layerId);

          return res.map((f) => ({
            layerName: selectedTabsIndex[result.layerId],
            __filterDisplayField: searchfields[result.layerId],
            id: f.attributes["OBJECTID"],
            ...f.attributes,
            geometry: f.geometry,
          }));
        });

        const allMappedResults = (await Promise.all(domainPromises)).flat();

        console.log(allMappedResults); // Now logs once, with all mapped features from all layers

        setSearchResult(allMappedResults);

        let searchQuery = normalizedValue.toLowerCase();
        const displayed = allMappedResults.filter((el) => {
          const fieldValue = el[el["__filterDisplayField"]];
          return fieldValue?.toLowerCase().includes(searchQuery);
        });

        // setDisplayedOuterSearch(displayed);
        // props.setFilteredResult(displayed);
        showLoading(false);
      } else {
        showLoading(false);
        setDisplayedOuterSearch([]);

        props.setFilteredResult([]);
      }
    } catch (error) {
      console.error("Search error:", error);
    } finally {
      setIsSearchPending(false);
    }
  }, []);

  // State for input

  // Debug state changes
  // useEffect(() => {
  //   setSearchValue(outerSearchText);
  //   console.log("outerSearchText updated:", outerSearchText);
  // }, [outerSearchText]);

  // // Handle input changes and clearing

  // // Handle activeBufferSearch to ensure it doesn't interfere
  // const activeBufferSearch = () => {
  //   console.log("activeBufferSearch called, current outerSearchText:", outerSearchText);
  //   // Your existing logic for activeBufferSearch
  //   // Ensure this function does NOT call setOuterSearchText unless intended
  // };

  // State for input

  const [formValues, setFormValues] = useState("");

  // displayAllResults (same as previous, included for completeness)
  const displayAllResults = useCallback(
    async (value, useMapExtent) => {
      const normalizedValue = value.replace(/\s+/g, " ").trim();

      if (!normalizedValue) {
        setSearchResult([]);
        setDisplayedOuterSearch([]);
        props.setFilteredResult([]);
        return;
      }

      setIsSearchPending(true);
      try {
        const searchType = getSearchType(normalizedValue);
        let results = [];

        const querySettings = {
          maxRecordCount: 100,
          num: 100,
          start: 0,
        };

        console.log("Search type:", searchType);
        console.log("Normalized value:", normalizedValue);

        switch (searchType) {
          case "lands_and_plans":
            const [landResults1, planResults1] = await Promise.all([
              fetchLands(normalizedValue, useMapExtent, querySettings),
              fetchPlans(normalizedValue, useMapExtent, querySettings),
            ]);
            results = [
              { features: landResults1, layerId: 0 },
              { features: planResults1, layerId: 1 },
            ];
            console.log("Lands and plans results:", results);
            break;

          case "lands_and_streets_and_plans":
            console.log("lands_and_streets_and_plans");
            const [planResults2, landResults2, streetResults1] =
              await Promise.all([
                fetchPlans(normalizedValue, useMapExtent, querySettings),
                fetchLands(normalizedValue, useMapExtent, querySettings),
                fetchStreets(normalizedValue, useMapExtent, querySettings),
              ]);
            results = [
              { features: landResults2, layerId: 0 },
              { features: planResults2, layerId: 1 },
              { features: streetResults1, layerId: 2 },
            ];
            console.log("Lands, streets, and plans results:", results);
            break;

          case "plans_and_streets":
            const normalizedPlanValue = normalizedValue
              .replace(/(?:مخطط|رقم)\s*/g, "")
              .trim();

            if (normalizedPlanValue.length >= 3) {
              const [planResults3, streetResults2] = await Promise.all([
                fetchPlans(normalizedPlanValue, useMapExtent, querySettings),
                fetchStreets(normalizedValue, useMapExtent, querySettings),
              ]);
              results = [
                { features: planResults3, layerId: 1 },
                { features: streetResults2, layerId: 2 },
              ];
              console.log("Plans and streets results:", results);
            } else {
              const streetResults = await fetchStreets(
                normalizedValue,
                querySettings
              );
              results = [{ features: streetResults, layerId: 2 }];
              console.log("Streets results:", results);
            }
            break;

          default:
            if (normalizedValue.length >= 3) {
              const planResults = await fetchPlans(
                normalizedValue,
                useMapExtent,
                querySettings
              );
              results = [{ features: planResults, layerId: 1 }];
              console.log("Default plans results:", results);
            }
            break;
        }

        let filteredResultLength = results.length;
        console.log("Filtered result length:", filteredResultLength);

        if (filteredResultLength) {
          const domainPromises = results.map(async (result) => {
            if (!result || !result.features) {
              console.log("Empty or invalid result detected:", result);
              return [];
            }

            const layerId = getLayerId(
              props.map.__mapInfo,
              selectedTabsIndex[result.layerId]
            );
            console.log("Layer ID for result:", layerId);

            const res = await getFeatureDomainName(result.features, layerId);
            console.log("Feature domain name response:", res);

            return res.map((f) => ({
              layerName: selectedTabsIndex[result.layerId],
              __filterDisplayField: searchfields[result.layerId],
              id: f.attributes["OBJECTID"],
              ...f.attributes,
              geometry: f.geometry,
            }));
          });

          const allMappedResults = (await Promise.all(domainPromises)).flat();
          console.log("All mapped results:", allMappedResults);

          let searchQuery = normalizedValue.toLowerCase();
          const displayed = allMappedResults.filter((el) => {
            const fieldValue = el[el["__filterDisplayField"]];
            return fieldValue?.toLowerCase().includes(searchQuery);
          });

          console.log("Displayed results:", displayed);

          setDisplayedOuterSearch(displayed);
          props.setFilteredResult(displayed);
          setSearchResult(allMappedResults);
          showLoading(false);

          // Navigate after promises resolve
          navigate("/search");
        } else {
          console.log("No results to process");
          showLoading(false);
          setDisplayedOuterSearch([]);
          props.setFilteredResult([]);
          setSearchResult([]);
        }
      } catch (error) {
        console.error("Search error:", error);
      } finally {
        setIsSearchPending(false);
      }
    },
    [
      navigate,
      props,
      setDisplayedOuterSearch,
      setIsSearchPending,
      setSearchResult,
      showLoading,
    ]
  );

  // Handle input changes and clearing
  const handleOuterSearchText = (e) => {
    // Remove e.preventDefault() to allow default input behavior
    // e.preventDefault(); // Commented out to fix clearing issue
    setOuterComplete(true);
    props.map.__selectedItem = null;
    const searchQuery = e.target.value ? e.target.value.toLowerCase() : "";

    console.log("handleOuterSearchText - New value:", searchQuery); // Debug: Log new value

    // Update outerSearchText immediately to reflect input changes
    setOuterSearchText(searchQuery);
    setSearchValue(searchQuery);

    // Close menu, clear search, and navigate to home if empty
    if (searchQuery === "") {
      props.closeResultMenu();
      setFormValues([]);
      setDisplayedOuterSearch([]);
      navigate("/");
    } else {
      setFormValues(searchQuery);
      if (timeout.current) clearTimeout(timeout.current);
      timeout.current = setTimeout(() => {
        console.log("Timeout triggered - searchQuery:", searchQuery); // Debug: Log timeout value
        setSearchValue(searchQuery);
        executeSearch(convertToEnglish(searchQuery || ""), isSearchInMapExtend);
        props.outerOpenResultMenu();
      }, 500);

      props.outerOpenResultMenu();
    }
  };
  const onSelectOuterSearchItem = (value, layerName) => {
    if (value && value.layerName) {
      props.setFilteredResult([value]);
      //set selected value to input text to show
      setSearchResult([]);
      setFormValues(value[layerName]);
      formRef.current.setFieldsValue({ searchText: value[layerName] });
    } else {
      props.setFilteredResult(displayed);
    }
    navigate("/search");
    props.handleDrawerOpen();
    props.outerOpenResultMenu();
    // props.setHelpName("cardsResultHelp");
    setOuterComplete(false);
  };
  // Handle Enter key press and button click
  const onShowAll = useCallback(
    async (e) => {
      const value = e?.target ? e.target.value.toLowerCase() : e;

      await displayAllResults(convertToEnglish(value), isSearchInMapExtend);
      props.handleDrawerOpen();
      props.outerOpenResultMenu();
      setOuterComplete(false);
      window.fastActiveMenu = 2;
    },
    [displayAllResults, isSearchInMapExtend, props]
  );
  // Handle Enter key press and trigger search
  const onShowALL = useCallback(async () => {
    await displayAllResults(convertToEnglish(searchValue), isSearchInMapExtend);
    props.handleDrawerOpen();
    props.outerOpenResultMenu();
    setOuterComplete(false);
  }, [displayAllResults, isSearchInMapExtend, props]);

  const getFilterUserSearchInput = (e) => {
    let layersId = [];
    let promiseQueries = [];
    selectedTabsIndex.forEach((layer, index) => {
      let layerdId = getLayerId(props.map.__mapInfo, layer);
      layersId.push(layerdId);
      let layersSetting = props.mainData.layers;
      promiseQueries.push(
        queryTask({
          url: window.mapUrl + "/" + layerdId,
          where: searchfields[index] + " Like '%" + e + "%'",
          outFields: layersSetting[layer].outFields,
          returnGeometry: true,
          start: 0,
          num: window.paginationCount,
          geometry: isActiveBufferSearch ? props.map.view.extent : null,
          returnExecuteObject: true,
        })
      );
    });

    showLoading(true);
    Promise.all(promiseQueries).then((resultsData) => {
      var allResult = [];

      let filteredResultLength = resultsData.filter(
        (f) => f.features.length
      ).length;

      if (filteredResultLength) {
        resultsData.forEach((result, index) => {
          if (result.features.length) {
            getFeatureDomainName(result.features, layersId[index]).then(
              (res) => {
                let mappingRes = res.map((f) => {
                  return {
                    layerName: selectedTabsIndex[index],
                    __filterDisplayField: searchfields[index],
                    id: f.attributes["OBJECTID"],
                    ...f.attributes,
                    geometry: f.geometry,
                  };
                });

                allResult = allResult.concat(mappingRes);
                --filteredResultLength;

                if (filteredResultLength == 0) {
                  showLoading(false);
                  let searchQuery = e;
                  displayed = allResult.filter(function (el) {
                    var searchValue =
                      el[el["__filterDisplayField"]].toLowerCase();
                    return searchValue.indexOf(searchQuery) !== -1;
                  });
                  setDisplayedOuterSearch(displayed);
                  //props.getOuterSearchData(searchQuery);

                  props.setFilteredResult(displayed);
                }
              }
            );
          }
        });
      } else {
        showLoading(false);
        setDisplayedOuterSearch([]);

        props.setFilteredResult([]);
      }
    });
  };

  useEffect(() => {
    setOuterComplete(false);
  }, [props.routeName]);

  const handleExtentToggle = useCallback(() => {
    setSearchResult([]);
    setIsSearchInMapExtend((prevExtent) => {
      const newExtent = !prevExtent;
      if (searchValue.trim()) {
        executeSearch(searchValue, newExtent);
      }
      return newExtent;
    });
  }, [searchValue, executeSearch]);

  //   const openDetailsOuterSearch = () => {
  //     navigate("/search");
  //     props.handleDrawerOpen();
  //     props.outerOpenResultdetails();
  //   };

  const [showSearchInput, setShowSearchInput] = useState(false);
  const [shouldRenderInput, setShouldRenderInput] = useState(false);
  const handleToggleInput = () => {
    if (showSearchInput) {
      // Trigger hide animation first
      setShowSearchInput(false);
      setOuterSearchText("");
      // After animation duration, remove from DOM
      setTimeout(() => {
        setShouldRenderInput(false);
      }, 0); // duration must match CSS transition
    } else {
      setShouldRenderInput(true); // Mount immediately
      setTimeout(() => {
        setShowSearchInput(true); // Trigger show animation
      }, 10); // Small delay to allow mounting before animating
    }
  };

  return (
    <div
      className="mapOuterSearch "
      style={{
        direction: i18n.language === "en" ? "ltr" : "rtl",
        position: "absolute",
        right:
          i18n.language === "ar"
            ? props.openDrawer
              ? location.pathname.substring(
                  location.pathname.lastIndexOf("/") + 1
                ) === "generalSearch" ||
                location.pathname.substring(
                  location.pathname.lastIndexOf("/") + 1
                ) === "search" ||
                location.pathname.substring(
                  location.pathname.lastIndexOf("/") + 1
                ) === "coordinateSearch" ||
                location.pathname.substring(
                  location.pathname.lastIndexOf("/") + 1
                ) === "marsed"
                ? "380px"
                : location.pathname.substring(
                    location.pathname.lastIndexOf("/") + 1
                  ) == "" ||
                  location.pathname.substring(
                    location.pathname.lastIndexOf("/")
                  ) == process.env.PUBLIC_URL
                ? "275px"
                : "400px"
              : "97px"
            : "unset",
        left:
          i18n.language === "en"
            ? props.openDrawer
              ? displayedOuterSearch.length > 0 &&
                outerSearchText !== "" &&
                (location.pathname.substring(
                  location.pathname.lastIndexOf("/") + 1
                ) === "generalSearch" ||
                  location.pathname.substring(
                    location.pathname.lastIndexOf("/") + 1
                  ) === "search" ||
                  location.pathname.substring(
                    location.pathname.lastIndexOf("/") + 1
                  ) === "coordinateSearch" ||
                  location.pathname.substring(
                    location.pathname.lastIndexOf("/") + 1
                  ) === "marsed")
                ? "480px"
                : displayedOuterSearch.length == 0 &&
                  outerSearchText == "" &&
                  (location.pathname.substring(
                    location.pathname.lastIndexOf("/") + 1
                  ) === "generalSearch" ||
                    location.pathname.substring(
                      location.pathname.lastIndexOf("/") + 1
                    ) === "search" ||
                    location.pathname.substring(
                      location.pathname.lastIndexOf("/") + 1
                    ) === "coordinateSearch" ||
                    location.pathname.substring(
                      location.pathname.lastIndexOf("/") + 1
                    ) === "marsed")
                ? "380px"
                : displayedOuterSearch.length > 0 &&
                  outerSearchText !== "" &&
                  (location.pathname.substring(
                    location.pathname.lastIndexOf("/") + 1
                  ) == "" ||
                    location.pathname == process.env.PUBLIC_URL)
                ? "280px"
                : displayedOuterSearch.length == 0 &&
                  outerSearchText == "" &&
                  (location.pathname.substring(
                    location.pathname.lastIndexOf("/") + 1
                  ) || location.pathname == process.env.PUBLIC_URL) == ""
                ? "280px"
                : // :displayedOuterSearch.length > 0 &&outerSearchText !== ""? "400px"
                  // :displayedOuterSearch.length == 0 &&outerSearchText == ""?"400px":"460px"
                  "26%"
              : !props.openDrawer
              ? displayedOuterSearch.length > 0 && outerSearchText !== ""
                ? "160px"
                : "160px"
              : "20%"
            : "unset",
      }}
    >
      <Fade
        {...(i18n.language === "en" ? { right: true } : { left: true })}
        delay={1000}
      >
        <div
          // className="outerSearchForm outerSearchHelp"
          className={`outerSearchForm outerSearchHelp ${
            !shouldRenderInput ? "outerFormWidthHide" : ""
          } ${
            outerSearchText ? "outerSearchFormOpen" : "outerSearchFormClose"
          }`}
          id={
            displayedOuterSearch.length > 0 && outerSearchText !== ""
              ? "outerFormWidthFit"
              : "outerFormWidth"
          }
          style={{ borderRadius: outerSearchText && "20px" }}
        >
          <Form
            ref={formRef}
            className="GeneralForm"
            layout="vertical"
            name="validate_other"
          >
            <Form.Item name="searchText" className="outerSearchInput">
              <div className="SearchInputContainer">
                <img
                  src={searchIcon2}
                  alt=""
                  onClick={handleToggleInput}
                  className="searchToggleIcon"
                />
                {shouldRenderInput && (
                  <Input
                    className={`searchFormInput searchInputWrapper ${
                      showSearchInput ? "show" : "hide"
                    }`}
                    placeholder={t("FastSearch")}
                    allowClear
                    value={outerSearchText}
                    //  onPressEnter={onShowAll}
                    onChange={handleOuterSearchText}
                    onKeyDown={(e) => {
                      console.log(
                        "Key pressed:",
                        e.key,
                        "Current value:",
                        outerSearchText
                      );
                      if (
                        e.key === "Backspace" &&
                        outerSearchText.length === 1
                      ) {
                        setOuterSearchText(""); // Force clear on backspace if last character
                      }
                    }}
                    size="large"
                    suffix={
                      i18n.language === "ar" ? (
                        <Tooltip
                          placement="bottom"
                          title={t("outerActiveSearch")}
                        >
                          <CiLocationArrow1
                            className="nearbyIconIcon"
                            onClick={handleExtentToggle}
                          />
                        </Tooltip>
                      ) : (
                        ""
                      )
                    }
                    prefix={
                      i18n.language === "en" ? (
                        <Tooltip
                          placement="bottom"
                          title={t("outerActiveSearch")}
                        >
                          <img
                            alt="nearbyIcon"
                            src={
                              isActiveBufferSearch
                                ? activeNearByIcon
                                : nearbyIcon
                            }
                            onClick={handleExtentToggle}
                            className="nearbyIcon"
                          />
                        </Tooltip>
                      ) : (
                        ""
                      )
                    }
                  />
                )}
              </div>
            </Form.Item>
            {outerSearchText !== "" &&
              searchResult.length > 0 &&
              outerAutoComplete && (
                <div className="outerSearchAutoComplete">
                  {searchResult.length > 0 ? (
                    searchResult.map((item, index) => {
                      if (item.PARCEL_PLAN_NO) {
                        // Handle the new type based on attributes like PLAN_NO, PARCEL_MAIN_LUSE, etc.
                        return (
                          <div
                            key={index}
                            onClick={() =>
                              onSelectOuterSearchItem(item, "Landbase_Parcel")
                            } // Handle click on suggestion
                            className="searchResult_outerSearch"
                          >
                            <span>
                              {item.PARCEL_PLAN_NO
                                ? `${t("generalSearch:parcelNumber")}: ${
                                    item.PARCEL_PLAN_NO
                                  }`
                                : `${t("generalSearch:parcelNumber")} ${t(
                                    "common:unselected1"
                                  )}`}
                              {/* {" | "} */}
                              {/* {item.PLAN_NO
                      ? `${t("layers:planNum")}: ${item.PLAN_NO}`
                      : `${t("layers:planNum")} ${t("common:unselected")}`} */}
                            </span>

                            <span>
                              {item.MUNICIPALITY_NAME
                                ? `${item.MUNICIPALITY_NAME} - `
                                : ""}

                              {item.PLAN_NO ? `${item.PLAN_NO} - ` : ""}
                              {item.PARCEL_MAIN_LUSE
                                ? `${item.PARCEL_MAIN_LUSE} `
                                : ""}
                            </span>
                          </div>
                        );
                      } else if (item.PLAN_NO && !item.PARCEL_PLAN_NO) {
                        return (
                          <div
                            key={index}
                            onClick={() =>
                              onSelectOuterSearchItem(item, "Plan_Data")
                            } // Handle click on suggestion
                            className="searchResult_outerSearch"
                          >
                            <span>
                              {item.PLAN_NO
                                ? `${t("layers:planNum")}: ${item.PLAN_NO}`
                                : `${t("layers:planNum")} ${t(
                                    "common:unselected"
                                  )} `}
                            </span>
                            <span>
                              {/* {item.DISTRICT_NAME
                      ? `${
                          item.DISTRICT_NAME
                        }`
                      : `${t("common:district")} ${t(
                          "common:unselected"
                        )}`}{" "}
                    {" | "} */}
                              {item.MUNICIPALITY_NAME
                                ? item.MUNICIPALITY_NAME
                                : ""}
                            </span>
                          </div>
                        );
                      } else if (item.STREET_FULLNAME) {
                        return (
                          <div
                            key={index}
                            onClick={() =>
                              onSelectOuterSearchItem(item, "Street_Naming")
                            } // Handle click on suggestion
                            className="searchResult_outerSearch"
                          >
                            <span>
                              {item.STREET_FULLNAME
                                ? `${t("layers:streetName")}: ${
                                    item.STREET_FULLNAME
                                  }`
                                : `${t("layers:streetName")} ${t(
                                    "common:unselected"
                                  )}`}
                            </span>
                            <span>
                              {/* {item.DISTRICT_NAME
                      ? `${
                          item.DISTRICT_NAME
                        }`
                      : `${t("common:district")} ${t(
                          "common:unselected"
                        )}`}{" "}
                    {" | "} */}
                              {item.MUNICIPALITY_NAME
                                ? `${item.MUNICIPALITY_NAME}  `
                                : ""}
                              {item.DISTRICT_NAME
                                ? ` - ${item.DISTRICT_NAME} `
                                : ""}
                            </span>
                          </div>
                        );
                      }
                      return null; // Return null for unknown item types
                    })
                  ) : (
                    <div>
                      {searchResultEmpty && <div>{t("common:noResults")}</div>}
                    </div> // Show when no results are found
                  )}
                </div>
              )}
            {searchResult.length > 10 && outerSearchText && (
              <div className="searchResult_Button_container">
                {" "}
                <button
                  className="searchResult_Button"
                  onClick={() => {
                    onShowAll(outerSearchText);
                    setOuterSearchText("");
                    // handleToggleInput()
                  }}
                >
                  {t("common:showAll")}
                </button>
              </div>
            )}
          </Form>
        </div>
      </Fade>
    </div>
  );
}
