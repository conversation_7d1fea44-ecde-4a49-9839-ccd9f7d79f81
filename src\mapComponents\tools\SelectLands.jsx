import React, { useEffect } from "react";
import Fade from "react-reveal/Fade";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes } from "@fortawesome/free-solid-svg-icons";
import { useTranslation } from "react-i18next";
import closeIconFigma from "../../assets/icons/closeIconFigma.svg";
import { convertToArabic } from "../../helper/common_func";
export default function SelectLands(props) {
  const { t } = useTranslation("common");

  useEffect(() => {
    console.log(props.landsAreas);
  }, [props.landsAreas]);

  const closeTool = () => {
    props.map.remove(props.map.findLayerById("trafficLayerId"));
    props.closeToolsData();
    props.Cleanup();
  };

  return (
    <Fade left collapse>
      <div className="toolsMenu trafficMenu traffic-container selectLandMenu">
        {/* <span
          style={{
            width: "100%",
            float: "left",
            textAlign: "left",
            marginLeft: "5px",
          }}
        >
          <FontAwesomeIcon
            icon={faTimes}
            style={{ marginTop: "5px", marginRight: "5px", cursor: "pointer" }}
            onClick={closeTool}
          />
        </span> */}
        <div className="Heading_tocComponent">
          <img onClick={closeTool} src={closeIconFigma} alt="" />
          <label>تحديد الأراضي</label>
        </div>
        <div className="selectLand_container">
          <span>
            {props?.landsAreas?.state?.totalArea
              ? convertToArabic(props?.landsAreas?.state?.totalArea)
              : ""}{" "}
            {props?.landsAreas?.state?.totalArea ? t("m2") : ""}{" "}
          </span>
          <p> : إجمالي مساحات الأراضي المختارة </p>
        </div>
      </div>
    </Fade>
  );
}
