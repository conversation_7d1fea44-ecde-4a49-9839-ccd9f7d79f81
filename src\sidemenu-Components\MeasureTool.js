import React, { useEffect, useRef, useState } from "react";
import { Row, Col, Button } from "antd";
import measure1 from "../assets/images/gis_measure-area-alt.svg";
import measure2 from "../assets/images/pepicons-pencil_ruler.svg";
import measure3 from "../assets/images/teenyicons_location-outline.svg";
import esriGreen from "../assets/images/esriGreen.png";
import esriGreen1 from "../assets/images/distance.svg";
import esriCursor from "../assets/images/curoser.svg";
import { Tooltip } from "@mui/material";
import { Table, Container } from "react-bootstrap";
import Measurement from "@arcgis/core/widgets/Measurement";
import { addPictureSymbol, convertToArabic } from "../helper/common_func";
import { useTranslation } from "react-i18next";
import {convertEngNumbersToArabic} from "../helper/utilsFunc"
export default function MeasureTool(props) {
  const [measureType, setMeasureType] = useState("spaceMeasure");
  const { t, i18n } = useTranslation("common");

  const [mousePoint, setMousePoint] = useState(null);
  const [mouseGreenPoint, setMouseGreenPoint] = useState(null);
const [observerMeasure, setObserverMeasure] = useState(null);
  const componentRef = useRef({});
  const { current: measurmentTool } = componentRef;

  useEffect(() => {
    window.DisableActiveTool();
    measurmentTool.current = new Measurement({
      view: props.map.view,
      activeTool: "area",
      container: document.getElementById("measurmentTool"),
    });

    let handler1 = props.map.view.on("pointer-move", (event) => {
      if (!measurmentTool.current.activeTool) {
        let point = props.map.view.toMap({ x: event.x, y: event.y });
        setMousePoint(point);
      }
    });

    window.handles.push(handler1);

    let handler2 = props.map.view.on("click", (event) => {
      if (!measurmentTool.current.activeTool) {
        setMouseGreenPoint(event.mapPoint);

        props.map.findLayerById("identifyGraphicLayer").removeAll();
        addPictureSymbol(
          event.mapPoint,
          esriGreen1,
          "identifyGraphicLayer",
          props.map,
          16,
          26
        );
      }
      if (measurmentTool.current.activeTool == "area") {
        if (
          measurmentTool.current?.domNode?.children[0]?.children[0]?.children[1]
            ?.children[0]?.children[0]
        )
          measurmentTool.current.domNode.children[0].children[0].children[1].children[0].children[0].innerHTML =
            "المساحة";
      }
      if (measurmentTool.current._widgets.get("distance")) {
        measurmentTool.current._widgets.get(
          "distance"
        ).messagesUnits.units.inches.abbr = "بوصة";
      }
    });
    window.handles.push(handler2);
    let isArabic = i18n.language === "ar";
    if (isArabic) {
      handleArabicNumForMeasure();
    }
    return  () => {
      measurmentTool.current.destroy();
      if (observerMeasure) {
        observerMeasure.disconnect();
      }
      props.map.findLayerById("identifyGraphicLayer").removeAll();
    };
  }, []);

  const distanceMeasure = (e) => {
    // window.DisableActiveTool();
    props.map.findLayerById("identifyGraphicLayer").removeAll();
    setMeasureType("distanceMeasure");
    measurmentTool.current.activeTool = "distance";
  };
  const spaceMeasure = (e) => {
    // window.DisableActiveTool();
    props.map.findLayerById("identifyGraphicLayer").removeAll();
    setMeasureType("spaceMeasure");
    measurmentTool.current.activeTool = "area";
  };
  const CoordinateMeasure = (e) => {
    // window.DisableActiveTool();
    setMeasureType("CoordinateMeasure");
    measurmentTool.current.clear();
  };

  const getFlooredFixed = (v, d) => {
    return convertToArabic(
      (Math.floor(v * Math.pow(10, d)) / Math.pow(10, d)).toFixed(d)
    );
  };

  // helper to convert measure numbers to arabic
  // FIXED: Arabic number conversion function
  const handleArabicNumForMeasure = () => {
    if (observerMeasure) {
      observerMeasure.disconnect();
    }

    const observer = new MutationObserver(function(mutations) {
      // Use a more comprehensive selector for all measurement values
      const measurementSelectors = [
        '.esri-area-measurement-2d__measurement-item-value',
        '.esri-distance-measurement-2d__measurement-item-value', 
        '.esri-measurement__measurement',
        '.esri-measurement__result',
        '.esri-area-measurement-2d__area',
        '.esri-distance-measurement-2d__distance',
        '.esri-area-measurement-2d__perimeter'
      ];

      measurementSelectors.forEach(selector => {
        const nodes = document.querySelectorAll(selector);
        nodes.forEach(function(node) {
          if (node.textContent && /\d/.test(node.textContent)) {
            const convertedText = convertEngNumbersToArabic(node.textContent);
            if (convertedText !== node.textContent) {
              node.textContent = convertedText;
            }
          }
        });
      });

      // Also convert any child text nodes that might contain numbers
      mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(function(node) {
            if (node.nodeType === Node.TEXT_NODE && /\d/.test(node.textContent)) {
              node.textContent = convertEngNumbersToArabic(node.textContent);
            } else if (node.nodeType === Node.ELEMENT_NODE) {
              // Check all text content within the added element
              const walker = document.createTreeWalker(
                node,
                NodeFilter.SHOW_TEXT,
                null,
                false
              );
              
              let textNode;
              while (textNode = walker.nextNode()) {
                if (/\d/.test(textNode.textContent)) {
                  textNode.textContent = convertEngNumbersToArabic(textNode.textContent);
                }
              }
            }
          });
        }
        
        if (mutation.type === 'characterData' && /\d/.test(mutation.target.textContent)) {
          mutation.target.textContent = convertEngNumbersToArabic(mutation.target.textContent);
        }
      });
    });

    setObserverMeasure(observer);

    // Start observing the measurement tool container specifically
    const measurementContainer = document.getElementById("measurmentTool");
    if (measurementContainer) {
      observer.observe(measurementContainer, {
        childList: true,
        subtree: true,
        characterData: true,
        characterDataOldValue: true
      });
    }

    // Convert existing content immediately
    setTimeout(() => {
      const measurementSelectors = [
        '.esri-area-measurement-2d__measurement-item-value',
        '.esri-distance-measurement-2d__measurement-item-value',
        '.esri-measurement__measurement',
        '.esri-measurement__result'
      ];

      measurementSelectors.forEach(selector => {
        const nodes = document.querySelectorAll(selector);
        nodes.forEach(function(node) {
          if (node.textContent && /\d/.test(node.textContent)) {
            node.textContent = convertEngNumbersToArabic(node.textContent);
          }
        });
      });
    }, 500);
  };
  return (
    <div className="MeasureTool">
      <Container fluid className="coordinates measurePage measurePageCopy">
        <Row>
          <Col span={12}>
            <div className="measurePageCopy_switch_btn_container">
              <Tooltip
                placement="top"
                title={t("areaCalc")}
                className="MuiTooltipStyle"
              >
                <Button
                  className="spaceMeasureBtn"
                  onClick={spaceMeasure}
                  id={measureType === "spaceMeasure" ? "activeSpaceBtn" : ""}
                  style={{ marginRight: "0" }}
                >
                  <img src={measure1} alt="spaceMeasure" />
                  {measureType === "spaceMeasure" && (
                    <span className="hashTran" style={{ marginRight: "3px" }}>
                      {t("areaCalc")}
                    </span>
                  )}
                </Button>
              </Tooltip>
              <Tooltip placement="top" title={t("measureDisLength")}>
                <Button
                  className="distanceMeasureBtn"
                  onClick={distanceMeasure}
                  id={
                    measureType === "distanceMeasure" ? "activeDistanceBtn" : ""
                  }
                >
                  <img src={measure2} alt="CoordinateMeasure" />
                  {measureType === "distanceMeasure" && (
                    <span className="hashTran" style={{ marginRight: "3px" }}>
                      {t("measureDisLength")}
                    </span>
                  )}
                </Button>
              </Tooltip>
              <Tooltip placement="top" title={t("locationCoord")}>
                <Button
                  className="CoordinateMeasureBtn"
                  onClick={CoordinateMeasure}
                  id={measureType === "CoordinateMeasure" ? "activeCooBtn" : ""}
                >
                  <img src={measure3} alt="CoordinateMeasure" />
                  {measureType === "CoordinateMeasure" && (
                    <span className="hashTran" style={{ marginRight: "3px" }}>
                      {t("locationCoord")}
                    </span>
                  )}
                </Button>
              </Tooltip>
            </div>
          </Col>
        </Row>

        <div
          id="measurmentTool"
          className="measurmentToolCopy"
          style={
            measureType != "CoordinateMeasure"
              ? { display: "block", textAlign: "right" }
              : { display: "none", textAlign: "right" }
          }
        ></div>

        {measureType === "CoordinateMeasure" && (
          <div style={{ margin: "10px", direction: "rtl" }}>
            {/* <Table className="table table-bordered">
              <tbody>
                <tr>
                  <td></td>
                  <td style={{ textAlign: "center" }}>
                    <span>{t("longitude")}</span>
                  </td>
                  <td style={{ textAlign: "center" }}>
                    <span>{t("Latitude")}</span>
                  </td>
                </tr>
                <tr>
                  <td>
                    <img src={esriCursor} />
                  </td>
                  <td style={{ textAlign: "center" }}>
                    <span>
                      {mousePoint
                        ? getFlooredFixed(mousePoint.longitude, 6)
                        : "----"}
                    </span>
                  </td>
                  <td style={{ textAlign: "center" }}>
                    <span>
                      {mousePoint
                        ? getFlooredFixed(mousePoint.latitude, 6)
                        : "----"}
                    </span>
                  </td>
                </tr>
                <tr>
                  <td>
                    <img src={esriGreen} />
                  </td>
                  <td style={{ textAlign: "center" }}>
                    <span>
                      {mouseGreenPoint
                        ? getFlooredFixed(mouseGreenPoint.longitude, 6)
                        : "----"}
                    </span>
                  </td>
                  <td style={{ textAlign: "center" }}>
                    <span>
                      {mouseGreenPoint
                        ? getFlooredFixed(mouseGreenPoint.latitude, 6)
                        : "----"}
                    </span>
                  </td>
                </tr>
              </tbody>
            </Table> */}

            <label className="HeadingCutomTable">{t("clickMapXY")}</label>

            <div className="custom-table">
              {/* Header Row */}
              <div className="row header-row">
                <div className="cell"></div>
                <div className="cell center">{t("longitude")}</div>
                <div className="cell center">{t("Latitude")}</div>
              </div>

              {/* Middle Row with border */}
              <div className="row with-border">
                <div className="cell">
                  <img src={esriCursor} alt="cursor" />
                </div>
                <div className="cell center">
                  <span>
                    {mousePoint
                      ? getFlooredFixed(mousePoint.longitude, 6)
                      : "----"}
                  </span>
                </div>
                <div className="cell center">
                  <span>
                    {mousePoint
                      ? getFlooredFixed(mousePoint.latitude, 6)
                      : "----"}
                  </span>
                </div>
              </div>

              {/* Last Row */}
              <div className="row">
                <div className="cell">
                  <img src={esriGreen1} alt="green marker" />
                </div>
                <div className="cell center">
                  <span>
                    {mouseGreenPoint
                      ? getFlooredFixed(mouseGreenPoint.longitude, 6)
                      : "----"}
                  </span>
                </div>
                <div className="cell center">
                  <span>
                    {mouseGreenPoint
                      ? getFlooredFixed(mouseGreenPoint.latitude, 6)
                      : "----"}
                  </span>
                </div>
              </div>
            </div>

            {/* <Table>
                <tbody>
                  <tr>
                    <td style={{ textAlign: "center" }}>
                      <span>{t("x")}</span>
                    </td>
                    <td style={{ textAlign: "center" }}>
                      <span>{t("y")}</span>
                    </td>
                  </tr>
                  <tr>
                    <td style={{ textAlign: "center" }}>
                      <span>
                        {mouseGreenPoint
                          ? getFlooredFixed(mouseGreenPoint.x, 6)
                          : "----"}
                      </span>
                    </td>
                    <td style={{ textAlign: "center" }}>
                      <span>
                        {mouseGreenPoint
                          ? getFlooredFixed(mouseGreenPoint.y, 6)
                          : "----"}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </Table> */}
            {mouseGreenPoint && (
              <div className="mouseGreenPointContainer">
                <div className="mouseGreenPointConntent">
                  <span>الإحداثي {t("x")}:</span>
                  <span>
                    {mouseGreenPoint
                      ? getFlooredFixed(mouseGreenPoint.x, 6)
                      : "----"}
                  </span>
                </div>
                <div className="mouseGreenPointConntent">
                  <span>الإحداثي {t("y")}:</span>
                  <span>
                    {mouseGreenPoint
                      ? getFlooredFixed(mouseGreenPoint.y, 6)
                      : "----"}
                  </span>
                </div>
              </div>
            )}
          </div>
        )}
      </Container>
    </div>
  );
}
