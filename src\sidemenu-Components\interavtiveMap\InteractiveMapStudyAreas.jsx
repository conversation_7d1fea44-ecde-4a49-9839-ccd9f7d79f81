import { Button, Form, Input, message, Modal, Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import edit_icon from "../../assets/images/sidemenu/edit.svg";
import interactive_edit from "../../assets/icons/interactive_edit.svg";
import delete_icon from "../../assets/icons/Trash Bin 2.svg";
// import delete_icon from "../../assets/images/sidemenu/delete.svg";
import { useState } from "react";
import axios from "axios";
import { FaCheck, FaRegCheckCircle } from "react-icons/fa";
import { IoMdClose } from "react-icons/io";

export default function InteractiveMapStudyAreas(props) {
  const initialFormState = { regionName: "", ediRegionName: "" };
  const [formValues, setFormValues] = useState(initialFormState);

  const { t } = useTranslation("common");
  const [regionsNames, setRegionsNames] = useState([]);

  const [editModalVisible, setEditModalVisible] = useState(false);
  const [activeEditName, setActiveEditName] = useState();
  const [activeDeleteName, setActiveDeleteName] = useState();
  const [deleteModalVisible, setdeleteModalVisible] = useState(false);

  const getAllRegions = async () => {
    await axios
      .get(`${window.ApiUrl}/StudyRegion/GetAll?pageSize=100`)
      .then((response) => {
        let regions = response.data.results.map((region) => {
          return {
            id: region.id,
            name: region.name,
            zones: region.zones,
          };
        });
        setRegionsNames([...regions]);
      })
      .catch((error) => {
        message.warning(t("ErrorRequest"));
      });
  };

  useState(() => {
    getAllRegions();
  }, []);

  const handleChangeInput = (e) => {
    setFormValues({ ...formValues, [e.target.name]: e.target.value });
  };

  ///// add new region ////

  const postRegion = async () => {
    try {
      const postUrl = `${window.ApiUrl}/StudyRegion`;
      const data = {
        name: formValues.regionName,
      };
      const response = await axios.post(postUrl, data);
      setRegionsNames((prevState) => [
        { id: response.data.id, name: response.data.name },
        ...prevState,
      ]);
      return { success: true };
    } catch (error) {
      return { success: false };
    }
  };

  const onSubmitRegion = async (e) => {
    if (formValues.regionName.trim().length === 0) {
      message.warning(t("emptyRegionName"));
    } else if (
      regionsNames.some((region) => region.name == formValues.regionName)
    ) {
      message.warning(t("newRegionNameRequired"));
    } else {
      const result = await postRegion();
      if (!result.success) {
        message.warning(t("ErrorRequest"));
      } else {
        message.warning(t("regionNamedAddSuccessfully"));
        setFormValues(initialFormState);
      }
    }
  };

  //// edit region name ///

  const editRegion = async (regionToEdit) => {
    try {
      const putUrl = `${window.ApiUrl}/StudyRegion/${regionToEdit.id}`;
      await axios.put(putUrl, { name: regionToEdit.name });
      return { success: true };
    } catch (error) {
      return { success: false };
    }
  };

  const showEdit = (regionName) => {
    setActiveEditName(regionName);
    setEditModalVisible(true);
  };

  const SubmitEditName = async (e) => {
    let otherRegionNames = regionsNames
      .filter((region) => region.name != activeEditName)
      .map((region) => region.name);
    if (
      formValues.ediRegionName == undefined ||
      formValues.ediRegionName.trim().length == 0
    ) {
      message.warning(t("emptyRegionName"));
    } else if (otherRegionNames.includes(formValues.ediRegionName)) {
      message.warning(t("newRegionNameRequired"));
    } else if (formValues.ediRegionName == activeEditName) {
      message.warning(t("SameOldNewRegionName"));
    } else {
      let regionToEdit = regionsNames.find(
        (region) => region.name == activeEditName
      );
      regionToEdit.name = formValues.ediRegionName;
      const result = await editRegion(regionToEdit);
      if (!result.success) {
        message.warning(t("ErrorRequest"));
      } else {
        const otherRegions = regionsNames.filter(
          (region) => region.name != activeEditName
        );
        setRegionsNames([...otherRegions]);
        regionEditedSuccessfully();
      }
    }
  };

  const regionEditedSuccessfully = () => {
    setEditModalVisible(false);
    setFormValues(initialFormState);
    message.warning(t("regionNamedEditSuccessfully"));
  };

  //// delete region ////

  const deleteRegion = async (regionId) => {
    try {
      const delteUrl = `${window.ApiUrl}/StudyRegion/${regionId}`;
      await axios.delete(delteUrl);
      return { success: true };
    } catch (error) {
      return { success: false };
    }
  };

  const showDelete = (regionName) => {
    const regionToDelete = regionsNames.find(
      (region) => region.name == regionName
    );
    if (!regionToDelete.zones || regionToDelete.zones.length === 0) {
      setActiveDeleteName(regionName);
      setdeleteModalVisible(true);
    } else {
      message.warning(t("CannotDeleteRegion"));
    }
  };

  const submitDeleteDrawing = async () => {
    const regionToDelete = regionsNames.find(
      (region) => region.name == activeDeleteName
    );

    const result = await deleteRegion(regionToDelete.id);
    if (!result.success) {
      message.warning(t("ErrorRequest"));
    } else {
      const otherRegions = regionsNames.filter(
        (region) => region.id != regionToDelete.id
      );
      setRegionsNames(otherRegions);
      regionDeletedSuccessfully();
    }
  };

  const regionDeletedSuccessfully = () => {
    setActiveDeleteName();
    setdeleteModalVisible(false);
    message.warning(t("regionDeletedSuccessfully"));
  };

  return (
    <>
      <>
        <div className="interActive_studyArea">
          <Form.Item
            label={t("study_area_name", { ns: "sidemenu" })}
            className="select-cust"
            style={{ width: "100%" }}
          >
            <Input
              // className="searchInput"
              placeholder={t("study_area_name", { ns: "sidemenu" })}
              name="regionName"
              value={formValues.regionName}
              maxLength={50}
              onChange={handleChangeInput}
            />
          </Form.Item>

          <button className="SearchBtn" size="large" onClick={onSubmitRegion}>
            {t("add", { ns: "sidemenu" })}
          </button>
        </div>

        <div style={{ height: "calc(-170px + 100vh)", overflow: "auto" }}>
          {regionsNames.length > 0 &&
            regionsNames.map((region, indx) => {
              return (
                <div
                  id={region.id}
                  key={indx}
                  className="generalSearchCard"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    padding: "16px",
                    borderRadius: "16px",
                    background: "#FFFFFF99",
                  }}
                >
                  <div className="interactive_drawing_label">{region.name}</div>
                  <div style={{ display: "flex", gap: "10px" }}>
                    <Tooltip title={t("edit")}>
                      <img
                        src={interactive_edit}
                        alt="edit icon"
                        onClick={() => showEdit(region.name)}
                      />
                    </Tooltip>
                    <Tooltip title={t("delete")}>
                      <img
                        src={delete_icon}
                        alt="delete icon"
                        onClick={() => showDelete(region.name)}
                      />
                    </Tooltip>
                  </div>
                </div>
              );
            })}
        </div>
      </>
      <>
        <Modal
          // title={t("editRegionName") + ": " + activeEditName}
          title={<div className="custom-modal-title">{t("edit")}</div>}
          className="shareModal sharmodalBookmark"
          centered
          visible={editModalVisible}
          onCancel={() => setEditModalVisible(false)}
          // okText={t("edit")}
          // cancelText={t("cancel")}
          closable={false}
          footer={
            <div className="footer_modal">
              <Button
                key="submit"
                type="primary"
                onClick={() => {
                  SubmitEditName();
                }}
                className="footer_buttonEdit_modal"
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "10px",
                }}
              >
                {/* <img src={PenImg} alt="" /> */}
                <FaCheck />
                <span className="me-1">{t("edit")}</span>
              </Button>
              <Button
                key="cancel"
                onClick={() => setEditModalVisible(false)}
                className="footer_buttonCancel_modal"
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "10px",
                }}
              >
                <IoMdClose color="red" />
                <span className="me-1">{t("close")}</span>
              </Button>
            </div>
          }
        >
          <div
            style={{
              textAlign: "center",
              fontSize: "16px",
              fontWeight: "bold",
              marginTop: "24px",
            }}
          >
            {t("editRegionName") + ": " + activeEditName}
          </div>

          <div
            // className="shardContent"
            style={{
              paddingTop: "10px",
            }}
          >
            <Form.Item
              label={t("study_area_name", { ns: "sidemenu" })}
              className="select-cust"
            >
              <Input
                name="ediRegionName"
                onChange={handleChangeInput}
                value={formValues.ediRegionName}
                maxLength={50}
                placeholder={t("study_area_name", { ns: "sidemenu" })}
              />
            </Form.Item>
          </div>
          {/* <div
            style={{
              display: "flex",
              gap: "10px",
              marginTop: "10px",
              justifyContent: "end",
            }}
          >

            <button
              className="SearchBtn"
              size="large"
              block
              onClick={() => {
                SubmitEditName();
              }}
            >
              {t("edit")}
            </button>
            <button
              className="SearchBtn"
              size="large"
              block
              onClick={() => setEditModalVisible(false)}
            >
              {t("close")}
            </button>
          </div> */}
        </Modal>

        <Modal
          title={
            <div className="custom-modal-title">
              {/* {t("deleteRegionConfirmation") + activeDeleteName} */}
              {t("حذف")}
            </div>
          }
          centered
          closable={false}
          visible={deleteModalVisible}
          //onOk={() => afterEditModal()}
          onCancel={() => setdeleteModalVisible(false)}
          // okText={t("yes")}
          // cancelText={t("no")}
          className="shareModal sharmodalBookmark"
          footer={
            <div className="footer_modal">
              <Button
                key="submit"
                type="primary"
                onClick={() => submitDeleteDrawing()}
                className="footer_buttonEdit_modal"
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "10px",
                }}
              >
                <FaCheck />
                {t("yes")}
              </Button>
              <Button
                key="cancel"
                onClick={() => setdeleteModalVisible(false)}
                className="footer_buttonCancel_modal"
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "10px",
                }}
              >
                <IoMdClose color="red" />
                {t("no")}
              </Button>
            </div>
          }
        >
          <div
            style={{
              textAlign: "center",
              fontSize: "16px",
              fontWeight: "bold",
              marginTop: "24px",
            }}
          >
            {t("deleteRegionConfirmation") + activeDeleteName}
          </div>
          {/* <div
            style={{
              display: "flex",
              gap: "10px",
              marginTop: "10px",
              justifyContent: "end",
            }}
          >
            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => {
                submitDeleteDrawing();
              }}
            >
              {t("yes")}
            </Button>

            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => setdeleteModalVisible(false)}
            >
              {t("no")}
            </Button>
          </div> */}
        </Modal>
      </>
    </>
  );
}
