import { Checkbox, Input, message } from "antd";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { CiFileOn } from "react-icons/ci";
import { Tab, TabList, TabPanel, Tabs } from "react-tabs";
import link_icon from "../../assets/images/attach_file.svg";
import MapImageLayer from "@arcgis/core/layers/MapImageLayer";
import { getUniqeID, notificationMessage } from "../../helper/utilsFunc";
import Upload from "../../components/Upload/Upload";
import IdentityManager from "@arcgis/core/identity/IdentityManager";
// import { showLoading } from "../helper/common_func";
// import eventBus from "../helper/EventBus";
import {
  executeGPTool,
  getFileNameFromUrl,
  showGeneralDataTable,
  showLoading,
} from "../../helper/common_func";
import eventBus from "../../helper/EventBus";

import dwg_img from "../../assets/images/file1.svg";
import kmz_img from "../../assets/images/flle2.svg";
import kml_img from "../../assets/images/file3.svg";
import usePersistentState from "../../helper/usePersistentState";
import axios from "axios";

const componentName = "ImportFiles";
export default function ImportFiles(props) {
  const { t } = useTranslation();

  const [inputValues, setinputValues] = usePersistentState(
    "inputValues",
    {
      mapName: "",
      mapUrl: "",
      mapToken: "",
    },
    componentName
  );
  const [selectedTab, setSelectedTab] = usePersistentState(
    "selectedTab",
    0,
    componentName
  );

  const [mapList, setMapList] = usePersistentState(
    "mapList",
    [],
    componentName
  );
  const [checkedItems, setCheckedItems] = usePersistentState(
    "checkedItems",
    [],
    componentName
  );
  const [addToken, setAddToken] = usePersistentState(
    "addToken",
    false,
    componentName
  );
  const [isBaseMap, setIsBaseMap] = usePersistentState(
    "isBaseMap",
    false,
    componentName
  );

  const removeAllImportLayer = () => {
    props.map?.layers?.items
      .filter((x) => x.id.indexOf("compareLayerList_") > -1)
      ?.forEach((layer) => {
        props.map.remove(props.map.findLayerById(layer.id));
      });
  };

  useEffect(() => {
    eventBus.on("unCheckImportLayers", (data) => {
      unCheckAllMaps(window.__importLayers, window.__mapList);
    });
  }, []);

  // useEffect(() => {
  //   return () => {
  //     displayDefaultBaseMap();
  //     removeAllImportLayer();
  //   };
  // }, []);

  useEffect(() => {
    window.__importLayers = [...checkedItems];
    window.__mapList = [...mapList];
  }, [checkedItems, mapList]);

  const addMapService = async (mapObj) => {
    //  debugger;
    showLoading(true);

    mapObj.mapUrl = mapObj.mapUrl.trim();
    const response = await fetch(`${mapObj.mapUrl}?f=pjson`);
    const data = await response.json();
    if (data.error && data.error.code === 499) {
      showLoading(false);
      //return;
    }

    if (mapObj.mapToken.length > 0) {
      IdentityManager.registerToken({
        server: mapObj.mapUrl,
        token: mapObj.mapToken,
      });
    }

    let guid = getUniqeID();
    guid = "compareLayerList_" + guid;

    let mapService = new MapImageLayer({
      id: guid,
      url: `${mapObj.mapUrl}${
        (mapObj.mapToken.length > 0 && `?token=${mapObj.mapToken.length}`) || ""
      }`,
      title: mapObj.mapName,
      visible: true,
    });
    let updatedObj = { ...mapObj, id: guid, isBaseMap: isBaseMap };
    let updatedCheckedItems = [...checkedItems];
    props.map.add(mapService);

    if (isBaseMap) {
      props.map.layers.reorder(mapService, 0);
      let otherBaseMapIds = mapList
        .filter((map) => map.isBaseMap)
        .map((map) => map.id);

      let otherBaseMaps = props.map.layers.items.filter((baseMap) =>
        otherBaseMapIds.includes(baseMap.id)
      );
      otherBaseMaps.forEach((baseMap) => (baseMap.visible = false));

      // for ui case
      let otherBaseMapsIds = mapList
        .filter((map) => map.isBaseMap)
        .map((item) => item.id);
      updatedCheckedItems = updatedCheckedItems.map((item) => {
        otherBaseMapsIds.forEach((id) => {
          if (item.hasOwnProperty(id)) {
            item[id] = false;
          }
        });
        return item;
      });
    }

    // if (!isBaseMap) {
    //   props.map.add(mapService);
    // } else {
    //   props.map.basemap.baseLayers.add(mapService);

    //   // for ui case
    //   let otherBaseMapsIds = mapList
    //     .filter((map) => map.isBaseMap)
    //     .map((item) => item.id);
    //   updatedCheckedItems = updatedCheckedItems.map((item) => {
    //     otherBaseMapsIds.forEach((id) => {
    //       if (item.hasOwnProperty(id)) {
    //         item[id] = false;
    //       }
    //     });
    //     return item;
    //   });

    //   // for map case
    //   props.map.basemap.baseLayers.items.forEach((baseMap) => {
    //     if (baseMap.id == guid) {
    //       props.map.basemap.baseLayers.reorder(baseMap, 0);
    //     } else {
    //       baseMap.visible = false;
    //     }
    //   });
    // }
    mapService.when(
      () => {
        showLoading(false);
        setMapList((prevList) => {
          setCheckedItems([{ [guid]: true }, ...updatedCheckedItems]);
          return [updatedObj, ...prevList];
        });
      },
      (error) => {
        showLoading(false);
        notificationMessage(t("print:ErrorOccurd"));
      }
    );
  };

  const toggleMapService = (layerId, isBaseMap) => {
    let layerToToggle = props.map.layers.items.find(
      (baseMap) => baseMap.id == layerId
    );
    if (isBaseMap) {
      let updatedCheckItems = [...checkedItems];
      props.map.layers.reorder(layerToToggle, 0);

      if (layerToToggle?.visible) {
        // for ui
        updatedCheckItems = updatedCheckItems.map((Item) => {
          if (Item.hasOwnProperty(layerId)) {
            Item[layerId] = false;
          }
          return Item;
        });
      } else {
        // for ui case
        let otherBaseMapIds = mapList
          .filter((map) => map.isBaseMap)
          .map((baseMap) => baseMap.id);
        updatedCheckItems = updatedCheckItems.map((Item) => {
          otherBaseMapIds.forEach((id) => {
            if (Item.hasOwnProperty(layerId)) {
              Item[layerId] = true;
              return;
            }
            if (Item.hasOwnProperty(id)) {
              Item[id] = false;
            }
          });
          return Item;
        });
      }

      let baseMapIds = mapList
        .filter((map) => map.isBaseMap)
        .map((map) => map.id);

      let baseMaps = props.map.layers.items.filter((baseMap) =>
        baseMapIds.includes(baseMap.id)
      );
      baseMaps.forEach((baseMap) => {
        if (baseMap.id != layerToToggle.id) {
          baseMap.visible = false;
        }
      });

      setCheckedItems([...updatedCheckItems]);
    }
    if (layerToToggle) {
      layerToToggle.visible = !layerToToggle?.visible;
    }
  };

  const onChangeCheckBox = (event, mapObj) => {
    const { name, checked } = event.target;
    const updatedCheckedItems = checkedItems.map((Item) =>
      Item.hasOwnProperty(name) ? { [name]: checked } : Item
    );
    setCheckedItems(updatedCheckedItems);
    toggleMapService(name, mapObj.isBaseMap);
  };

  const unCheckAllMaps = (list, customMapList) => {
    // for ui
    let updatedCheckedItems = list ? [...list] : [...checkedItems];
    let mapIds = (customMapList || mapList).map((baseMap) => baseMap.id);
    updatedCheckedItems = updatedCheckedItems.map((Item) => {
      mapIds.forEach((id) => {
        if (Item.hasOwnProperty(id)) {
          Item[id] = false;
        }
      });

      return Item;
    });
    setCheckedItems([...updatedCheckedItems]);

    // for map
  };

  const displayDefaultBaseMap = () => {
    props.map.basemap.baseLayers.items.forEach((item) => {
      if (item.id.indexOf("compareLayerList_") > -1) {
        props.map.basemap.baseLayers.remove(item);
      }
    });
    let defaultBaseMapIndex = props.map.basemap.baseLayers.items.length - 1;
    props.map.basemap.baseLayers.items[defaultBaseMapIndex].visible = true;
  };

  const onChangeInput = (e) => {
    setinputValues({ ...inputValues, [e.target.name]: e.target.value });
  };

  const submitDataInput = (e) => {
    if (inputValues.mapUrl.trim().length == 0) {
      message.warning(t("common:MapUrlRequired"));
    } else if (inputValues.mapName.trim().length == 0) {
      message.warning(t("common:MapNameRequired"));
    } else if (isMapNameDuplicated(inputValues.mapName)) {
      message.warning(t("common:MapNameDuplication"));
    } else {
      addMapService(inputValues);
      setinputValues({
        mapName: "",
        mapUrl: "",
        mapToken: "",
      });
    }
  };

  const isMapNameDuplicated = (mapName) => {
    let savedNames = mapList.map((mapObj) => mapObj.mapName);
    return savedNames.includes(mapName);
  };

  return (
    <Tabs
      selectedIndex={selectedTab}
      value={selectedTab}
      onSelect={(x) => setSelectedTab(x)}
    >
      <TabList>
        <Tab>
          <img src={link_icon} alt="link icon" />
          {t("sidemenu:link")}
        </Tab>

        <Tab>
          <CiFileOn />
          {t("sidemenu:file")}
        </Tab>
      </TabList>

      <TabPanel>
        <div
          style={{ display: "flex", flexDirection: "column", gap: "10px" }}
          className="mt-3 "
        >
          <div
            className="importFilesTabs"
            style={{ display: "flex", flexDirection: "column", gap: "0px" }}
          >
            <p className="pTextAlign" style={{ margin: "0" }}>
              {t("sidemenu:the_link")}
            </p>
            <Input
              className="searchInput"
              placeholder={t("sidemenu:enter_link")}
              name="mapUrl"
              value={inputValues.mapUrl}
              onChange={onChangeInput}
            />
          </div>
          {/* <div>
            <Checkbox
              onChange={(e) => {
                setIsBaseMap(e.target.checked);
              }}
              checked={isBaseMap}
              style={{ float: "inline-start" }}
            >
              <span className="mainBaseMap">{t("sidemenu:mainBaseMap")}</span>
            </Checkbox>
          </div> */}
          <div style={{ display: "flex", alignItems: "center" }}>
            <input
              type="checkbox"
              // style={{ marginTop: "-10px" }}
              onChange={(e) => {
                setIsBaseMap(e.target.checked);
              }}
              checked={isBaseMap}
              className="toc-gallery-content_checkbox"
            />
            <label className="checkBox_print">
              {t("sidemenu:mainBaseMap")}
            </label>
          </div>
          <div
            className="importFilesTabs"
            style={{ display: "flex", flexDirection: "column", gap: "0px" }}
          >
            <p className="pTextAlign" style={{ margin: "0" }}>
              {t("sidemenu:mapName")}
            </p>
            <Input
              className="searchInput"
              placeholder={t("sidemenu:enter_mapName")}
              name="mapName"
              value={inputValues.mapName}
              onChange={onChangeInput}
            />
          </div>
          {/* <div>
            <Checkbox
              onChange={(e) => {
                setAddToken(e.target.checked);
              }}
              checked={addToken}
              style={{ float: "inline-start" }}
            >
              <span className="add_token">{t("sidemenu:add_token")}</span>
            </Checkbox>
          </div> */}
          <div style={{ display: "flex", alignItems: "center" }}>
            <input
              type="checkbox"
              // style={{ marginTop: "-10px" }}
              onChange={(e) => {
                setAddToken(e.target.checked);
              }}
              checked={addToken}
              className="toc-gallery-content_checkbox"
            />
            <label className="checkBox_print">{t("sidemenu:add_token")}</label>
          </div>

          {addToken && (
            // <div
            //   style={{
            //     display: "flex",
            //     flexDirection: "column",
            //     gap: "7px",
            //   }}
            // >
            //   <p className="pTextAlign" style={{ margin: "0" }}>
            //     token
            //   </p>
            //   <Input
            //     className="searchInput"
            //     placeholder={t("sidemenu:enter_token")}
            //     name="mapToken"
            //     value={inputValues.mapToken}
            //     onChange={onChangeInput}
            //   />
            // </div>
            <div
              className="importFilesTabs"
              style={{ display: "flex", flexDirection: "column", gap: "0px" }}
            >
              <p className="pTextAlign" style={{ margin: "0" }}>
                {/* {t("sidemenu:mapName")} */}
                token
              </p>
              <Input
                className="searchInput"
                placeholder={t("sidemenu:enter_token")}
                name="mapToken"
                value={inputValues.mapToken}
                onChange={onChangeInput}
              />
            </div>
          )}
        </div>
        <div style={{ textAlign: "center" }} className="mt-4">
          <button
            className="SearchBtn SearchBtnImportFiles mt-3 "
            size="large"
            htmlType="submit"
            block
            onClick={submitDataInput}
          >
            {t("sidemenu:save")}
          </button>
        </div>
        <div
          style={{
            // height: "330px",
            overflow: "auto",
            marginTop: "20px",
          }}
        >
          {mapList.length > 0 &&
            checkedItems.length > 0 &&
            mapList.map((mapObj, index) => (
              <>
                <div
                  style={{ display: "flex",alignItems :"center", padding :"15px" }}
                  className="generalSearchCard"
                >
                  {/* <Checkbox
                    key={index}
                    name={mapObj.id}
                    onChange={(e) => {
                      onChangeCheckBox(e, mapObj);
                    }}
                    checked={
                      checkedItems.find((Item) =>
                        Item.hasOwnProperty(mapObj.id)
                      )[mapObj.id]
                    }
                    style={{ float: "inline-start" }}
                  ></Checkbox>
                  <label htmlFor="">{mapObj.mapName}</label> */}

                  <input
                    type="checkbox"
                    key={index}
                    name={mapObj.id}
                    onChange={(e) => {
                      onChangeCheckBox(e, mapObj);
                    }}
                    checked={
                      checkedItems.find((Item) =>
                        Item.hasOwnProperty(mapObj.id)
                      )[mapObj.id]
                    }
                    className="toc-gallery-content_checkbox"
                  />
                  <label className="checkBox_print">{mapObj.mapName}</label>
                </div>
              </>
            ))}
        </div>
      </TabPanel>

      <TabPanel>
        <div style={{ textAlign: "center" }}>
          <div
            className="my-4"
            style={{ display: "flex", gap: "10px", justifyContent: "center" }}
          >
            <div className="imgContainer">
              <img src={dwg_img} alt="dwg_img" />
            </div>
            <div className="imgContainer">
              <img src={kmz_img} alt="kmz_img" />
            </div>
            <div className="imgContainer">
              <img src={kml_img} alt="kml_img" />
            </div>
          </div>

          {/* <Upload
            name="fileUpload"
            multiple={true}
            onChange={props.setFile}
            accept=".kmz, .kml, .dwg"
            type="file"
            action={window.ApiUrl + "uploadMultifiles"}
            style={{display:"block"}}
          >
            <button
              className="SearchBtn SearchBtnImportFiles mt-3 "
              size="large"
              htmlType="submit"
              block
            >
              {t("sidemenu:import")}
            </button>
          </Upload> */}
          <Upload
            label={"إضافة صور/ملفات اخري"}
            name="fileUpload"
            fileType={".kmz,.kml,.dwg"}
            multiple={false}
            fileObjectEnabled={true}
            onInputChange={(attachments) => {
              let value = Array.isArray(attachments) && attachments?.map(file => file.data).join(', ') || "";
              setinputValues({ ...inputValues, attachment: attachments });
              if (!value) return;
              let fileExt = value.split(".");
              fileExt = fileExt[fileExt.length - 1];

              let params;
              let processingToolUrl;
              let fileType = "cad";
              let outputName = "output_value";

              if (fileExt == "kmz" || fileExt == "kml") {
                params = {
                  KML_File_Name: value,
                  
                };
                processingToolUrl = window.kmlToJSONGPUrl;
                outputName = "output_value";
                fileType = "kmz";
              } else if (fileExt == "dwg") {
                params = {
                  CAD_File_Name: value,
                };
                processingToolUrl = window.cadToJsonGPUrl;
                outputName = "output_value";
              }
           
              showLoading(true);
              let userObj = localStorage.getItem("user");
              if (userObj) userObj = JSON.parse(userObj);

              executeGPTool(
                `${processingToolUrl}?token=${
                  userObj ? userObj.esriToken : ""
                }`,
                params,
                (result) => {
                  debugger;
                  showLoading(false);
                  showGeneralDataTable({
                    type: "importGisFile",
                    data: result,
                    map: props.map,
                    uploadFileType: fileType,
                    show: true,
                    
                  });
                },
                (error) => {
                  showLoading(false);
                  message.error(t("sidemenu:addFileToMapError"));
                },
                outputName,
                "submitJob",
                userObj?.esriToken
              );
             
            }}
            value={
              (inputValues.attachment?.length && [
                ...inputValues.attachment.map(r => r.data),
              ]) ||
              []
            }
          />
                  <div style={{ textAlign: "center" }} className="mt-4">

            <button
                  onClick={()=>{
                    const {attachment}=inputValues;
                       debugger;
                 axios.post(`${window.ApiUrl}/ImportedFile`, {
                 name: getFileNameFromUrl(attachment?.[0]?.PrevFileName),
                 path: attachment?.[0]?.data,
              })
              
                  .then(() => {
                  message.success(t("تم الحفظ بنجاح"));       
                        })
                  setinputValues({...inputValues,attachment:[]})
               }}
             className="SearchBtn SearchBtnImportFiles mt-3 "
            size="large"
            htmlType="submit"
            block
         >
            {t("sidemenu:save")}
                </button>
                </div>
        </div>
      </TabPanel>
    </Tabs>
  );
}
