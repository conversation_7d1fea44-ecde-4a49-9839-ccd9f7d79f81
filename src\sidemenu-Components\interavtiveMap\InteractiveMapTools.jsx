import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import ToolsBox from "./ToolsBox";
import BiodiversityBox from "./BiodiversityBox";
import PeopleVehiclesBox from "./PeopleVehiclesBox";
import ViolationsBox from "./ViolationsBox";
import MoreInteractiveTools from "./MoreInteractiveTools";
import Point from "@arcgis/core/geometry/Point";
import Graphic from "@arcgis/core/Graphic";
import SimpleLineSymbol from "@arcgis/core/symbols/SimpleLineSymbol";
import Polyline from "@arcgis/core/geometry/Polyline";
import { project, showLoading } from "../../helper/common_func";
import PictureMarkerSymbol from "@arcgis/core/symbols/PictureMarkerSymbol";
import axios from "axios";
import { Button, Form, Input, message, Modal } from "antd";
import Extent from "@arcgis/core/geometry/Extent";
import eventBus from "../../helper/EventBus";

import interactiveGroup from "../../assets/icons/interactiveGroup.svg";
import ineractiveVector from "../../assets/icons/ineractiveVector.svg";
import { IoMdClose } from "react-icons/io";
import { FaCheck } from "react-icons/fa";

export default function InteractiveMapTools(props) {
  const { t } = useTranslation("sidemenu");

  const toolsBoxRef = useRef();
  const [selectedRegionId, setSelectedRegionId] = useState(undefined);
  const [selectedZoneId, setSelectedZoneId] = useState(undefined);
  const [zonesList, setZonesList] = useState([]);
  const [selectedZoneIndex, setSelectedZoneIndex] = useState("");
  const [selectedSymbolName, setSelectedSymbolName] = useState();
  const [selectedFileUrl, setSelectedFileUrl] = useState();
  const [selectedGraphicLayer, setSelectedGraphicLayer] = useState();
  const [generalSelectedItem, setGeneralSelectedItem] = useState(null);
  const [generalSelectedBox, setGeneralSelectedBox] = useState("");
  const [mapNameModalVisible, setMapNameModalVisible] = useState(false);
  const [mapsNames, setMapsNames] = useState([]);

  let pathPoints = [];
  let drawing = false;
  const pointerDown = useRef();
  const pointerMove = useRef();
  const pointerUp = useRef();
  const drag = useRef();

  useEffect(() => {
    eventBus.dispatch("setShowInteractivEditBar", {
      message: true,
      eventListeners: eventHandlers,
      graphicsHistory: history,
      map: props.map,
    });

    if (!props.editDrawingData) getPrivateDrawings();
  }, []);

  useEffect(() => {
    if (generalSelectedBox != "tools") {
      if (toolsBoxRef.current) {
        toolsBoxRef.current.cancelDraw();
      }
    }
  }, [generalSelectedBox]);

  useEffect(() => {
    if (selectedGraphicLayer && selectedSymbolName) {
      window.unSubscribeMapEvents();
      pointerDown.current = props.map.view.on("pointer-down", startDrawing);
      pointerMove.current = props.map.view.on("pointer-move", continuePath);
      pointerUp.current = props.map.view.on("pointer-up", endDrawing);
      drag.current = props.map.view.on("drag", stopEvtPropagation);

      window.handles.push(pointerDown.current);
      window.handles.push(pointerMove.current);
      window.handles.push(pointerUp.current);
      window.handles.push(drag.current);

      if (eventHandlers.current.animation.length == 0) {
        eventHandlers.current.animation.push(
          ...[pointerDown, pointerMove, pointerUp, drag]
        );
      }
      function stopEvtPropagation(event) {
        if (drawing && selectedSymbolName) event.stopPropagation();
      }

      function startDrawing(event) {
        if (selectedSymbolName) {
          event.stopPropagation();
          drawing = true;
          const point = props.map.view.toMap({ x: event.x, y: event.y });
          pathPoints = [point];
        }
      }

      function continuePath(event) {
        if (drawing && selectedSymbolName) {
          event.stopPropagation();
          const point = props.map.view.toMap({ x: event.x, y: event.y });
          if (
            pathPoints[pathPoints.length - 1].x != point.x ||
            pathPoints[pathPoints.length - 1].y != point.y
          ) {
            pathPoints.push(point);
          }
        }
      }

      function endDrawing(event) {
        if (drawing && selectedSymbolName) {
          event.stopPropagation();
          drawing = false;
          const point = props.map.view.toMap({ x: event.x, y: event.y });
          pathPoints.push(point);

          showLoading(true);

          if (pathPoints.length == 2) {
            addFirstPoint(pathPoints);
          } else if (pathPoints.length > 2) {
            animateSymbol(selectedGraphicLayer, pathPoints, selectedSymbolName);
          }
        }
      }
    }

    return () => {
      if (
        pointerUp.current &&
        pointerDown.current &&
        pointerMove.current &&
        drag.current
      ) {
        pointerUp.current.remove();
        pointerDown.current.remove();
        pointerMove.current.remove();
        drag.current.remove();
      }
    };
  }, [selectedGraphicLayer, selectedSymbolName, selectedFileUrl]);

  useEffect(() => {
    if (selectedZoneId) {
      const selectedZone = zonesList.find((zone) => zone.id == selectedZoneId);
      if (selectedZone.extent) {
        props.map.view.extent = Extent.fromJSON(
          JSON.parse(selectedZone.extent)
        );
      }
    }
  }, [selectedZoneId]);

  useEffect(() => {
    if (props.editDrawingData) {
      let savedGraphicsData = JSON.parse(props.editDrawingData.drawing);
      props.map.view.extent = Extent.fromJSON(savedGraphicsData.mapExtent);
      props.callDrawSavedGraphics(props.editDrawingData);
    }
  }, [props.editDrawingData]);

  const updateGraphicState = (graphicState) => {
    pathPoints = [];
    if (
      pointerUp.current &&
      pointerDown.current &&
      pointerMove.current &&
      drag.current
    ) {
      pointerUp.current.remove();
      pointerDown.current.remove();
      pointerMove.current.remove();
      drag.current.remove();
    }
    if (graphicState) {
      setSelectedGraphicLayer(graphicState.graphicsLayerName);
      setSelectedSymbolName(graphicState.symbolName);
      graphicState.fileURL
        ? setSelectedFileUrl(graphicState.fileURL)
        : setSelectedFileUrl(undefined);
    } else {
      setSelectedGraphicLayer(undefined);
      setSelectedSymbolName(undefined);
      setSelectedFileUrl(undefined);
    }
  };

  const eventHandlers = useRef({
    sketch: toolsBoxRef,
    animation: [],
    disableSelectedTool: setGeneralSelectedItem,
    disableGraphicState: updateGraphicState,
  });

  //// history stack ////

  const history = useRef({
    undoStack: [],
    redoStack: [],
  });

  //// animation ////

  const addFirstPoint = (points) => {
    project(points, 4326, (features) => {
      points = [...features];
    });

    let projectedPoint = points[0];
    const drawingLayer = props.map.findLayerById("InteractiveMapGraphicLayer");

    let pictureUrl = selectedSymbolName.includes(
      "SubAttachments/uploadMultifiles"
    )
      ? selectedSymbolName
      : selectedSymbolName != "location_marker" &&
        selectedSymbolName != "rocks" &&
        selectedSymbolName != "ruler" &&
        selectedSymbolName != "word" &&
        selectedSymbolName != "excel" &&
        selectedSymbolName != "engineer" &&
        selectedSymbolName != "pdf" &&
        selectedSymbolName != "castle"
      ? require(`../../assets/images/interactive-map/map-GIFs/${selectedSymbolName}_left.gif`)
      : require(`../../assets/images/interactive-map/${selectedSymbolName}.png`);

    const pointSymbol = new PictureMarkerSymbol({
      url: pictureUrl.default ? pictureUrl.default : pictureUrl,
      width: 40,
      height: 40,
    });

    const pointGraphic = new Graphic({
      geometry: projectedPoint,
      symbol: pointSymbol,
    });

    drawingLayer.graphics.forEach((grap) => {
      if (grap._animateId) cancelAnimationFrame(grap._animateId);
    });

    pointGraphic._layerName = selectedGraphicLayer;
    if (selectedSymbolName.includes("SubAttachments/uploadMultifiles")) {
      pointGraphic._fileURL = selectedSymbolName;
    }
    if (selectedFileUrl) {
      pointGraphic._fileURL = selectedFileUrl;
    }

    drawingLayer.graphics.add(pointGraphic);

    props.map.view.whenLayerView(drawingLayer).then((layerView) => {
      layerView.watch("updating", (isUpdating) => {
        if (!isUpdating) {
          showLoading(false);
          drawingLayer.graphics.forEach((grap) => {
            if (grap._animate) grap._animate();
          });
        }
      });
    });

    history.current.undoStack.push({
      action: "add",
      graphic: pointGraphic,
    });

    window.__isInteractiveMapSaved = false;
  };

  const animateSymbol = (graphicLayerId, paths, symbolName) => {
    project(paths, 4326, (features) => {
      paths = [...features];
    });

    const drawingLayer = props.map.findLayerById("InteractiveMapGraphicLayer");
    const sr = drawingLayer.spatialReference;

    const lineGeometry = new Polyline({
      paths: paths.map((point) => [point.x, point.y]),
      spatialReference: paths[0].spatialReference,
    });

    const lineSymbol = new SimpleLineSymbol({
      color: [0, 0, 0, 0.3],
      width: 2,
      style: "dash",
    });

    const lineGraphic = new Graphic({
      geometry: lineGeometry,
      symbol: lineSymbol,
    });

    lineGraphic.has_animation = true;
    lineGraphic._layerName = graphicLayerId;

    drawingLayer.graphics.add(lineGraphic);

    history.current.undoStack.push({
      action: "add",
      graphic: lineGraphic,
    });

    let pictureUrl = symbolName.includes("SubAttachments/uploadMultifiles/")
      ? symbolName
      : symbolName !== "location_marker" &&
        symbolName !== "rocks" &&
        symbolName !== "word" &&
        symbolName !== "excel" &&
        symbolName !== "engineer" &&
        symbolName !== "pdf" &&
        symbolName !== "ruler" &&
        symbolName !== "castle"
      ? null
      : require(`../../assets/images/interactive-map/map-GIFs/${symbolName}.png`);

    const pointSymbol = new PictureMarkerSymbol({
      url: pictureUrl
        ? pictureUrl.default
          ? pictureUrl.default
          : pictureUrl
        : null,
      width: 40,
      height: 40,
    });

    const pointGraphic = new Graphic({
      geometry: paths[0],
      symbol: pointSymbol,
    });

    const tempGraphic = new Graphic({
      id: "temp_graphic",
      geometry: paths[0],
      visible: false,
    });

    let pointIndex = 0;
    let fraction = 0;

    let directions;
    let leftSidePicture;
    let rightSidePicture;
    if (pictureUrl == null) {
      directions = analyzeGeometryDirections(paths);
      rightSidePicture = require(`../../assets/images/interactive-map/map-GIFs/${symbolName}_right.gif`);
      leftSidePicture = require(`../../assets/images/interactive-map/map-GIFs/${symbolName}_left.gif`);
      if (directions.Left && !directions.Right) {
        pointGraphic.symbol.url = rightSidePicture.default;
        tempGraphic.symbol = new PictureMarkerSymbol({
          url: leftSidePicture.default,
          width: 40,
          height: 40,
        });
      } else if (directions.Right && !directions.Left) {
        pointGraphic.symbol.url = leftSidePicture.default;
        tempGraphic.symbol = new PictureMarkerSymbol({
          url: rightSidePicture.default,
          width: 40,
          height: 40,
        });
      } else if (directions.Right && directions.Left) {
        if (directions.Right[0].from == 0) {
          pointGraphic.symbol.url = leftSidePicture.default;
          tempGraphic.symbol = new PictureMarkerSymbol({
            url: rightSidePicture.default,
            width: 40,
            height: 40,
          });
        } else {
          pointGraphic.symbol.url = rightSidePicture.default;
          tempGraphic.symbol = new PictureMarkerSymbol({
            url: leftSidePicture.default,
            width: 40,
            height: 40,
          });
        }
      }

      drawingLayer.graphics.add(tempGraphic);
    }

    function animate() {
      const startPoint = paths[pointIndex];
      const endPoint = paths[pointIndex + 1];

      const x = startPoint.x + fraction * (endPoint.x - startPoint.x);
      const y = startPoint.y + fraction * (endPoint.y - startPoint.y);

      pointGraphic.geometry = new Point({
        x: x,
        y: y,
        spatialReference: sr,
      });

      fraction += window.__speedFactor === 0 ? 0 : window.__speedFactor / 100;

      if (fraction > 1) {
        fraction = 0;
        pointIndex += 1;

        // Determine direction for current segment
        if (pictureUrl == null && directions) {
          let direction = "";
          if (
            directions.Right &&
            directions.Right.some(
              ({ from, to }) => from === pointIndex && to === pointIndex + 1
            )
          ) {
            direction = "left";
          } else if (
            directions.Left &&
            directions.Left.some(
              ({ from, to }) => from === pointIndex && to === pointIndex + 1
            )
          ) {
            direction = "right";
          }

          if (direction) {
            let directionUrl =
              direction == "right" ? rightSidePicture : leftSidePicture;

            pointGraphic.symbol.url = directionUrl.default
              ? directionUrl.default
              : pictureUrl;
          }
        }
      }

      if (pointIndex >= paths.length - 1) {
        pointIndex = 0;
      }

      pointGraphic._animateId = requestAnimationFrame(animate);
    }

    pointGraphic._animate = animate;
    pointGraphic._layerName = graphicLayerId;

    if (selectedFileUrl) {
      lineGraphic._fileURL = selectedFileUrl;
      pointGraphic._fileURL = selectedFileUrl;
    } else if (
      !selectedFileUrl &&
      pointGraphic.symbol.url?.includes("SubAttachments/uploadMultifiles/")
    ) {
      lineGraphic._fileURL = pointGraphic.symbol.url;
      pointGraphic._fileURL = pointGraphic.symbol.url;
    }
    drawingLayer.graphics.forEach((grap) => {
      if (grap._animateId) cancelAnimationFrame(grap._animateId);
    });

    drawingLayer.graphics.add(pointGraphic);

    history.current.undoStack.push({
      action: "add",
      graphic: pointGraphic,
    });

    props.map.view.whenLayerView(drawingLayer).then((layerView) => {
      layerView.watch("updating", (isUpdating) => {
        if (!isUpdating) {
          showLoading(false);
          drawingLayer.graphics.forEach((grap) => {
            if (grap._animate) grap._animate();
          });
        }
      });
    });

    window.__isInteractiveMapSaved = false;
  };

  function analyzeGeometryDirections(geometries) {
    const directions = [];
    const threshold = 0;

    for (let i = 0; i < geometries.length - 1; i++) {
      const currentGeometry = geometries[i];
      const nextGeometry = geometries[i + 1];

      const { x: x1, y: y1 } = currentGeometry;
      const { x: x2, y: y2 } = nextGeometry;

      const deltaX = x2 - x1;
      const deltaY = y2 - y1;

      let direction = "";

      if (deltaX > threshold) {
        direction = "Right"; // East-like movement
      } else if (deltaX < -threshold) {
        direction = "Left"; // West-like movement
      } else if (deltaY !== 0) {
        // For vertical movement, treat as horizontal equivalent
        direction = deltaY > 0 ? "Right" : "Left";
      }

      // Handle cases where movement is near zero (stationary)
      if (!direction) {
        const prevDirection = i > 0 ? directions[i - 1]?.direction : "Right";
        direction = prevDirection;
      }

      directions.push({ from: i, to: i + 1, direction });
    }

    // Adjust initial point direction
    if (directions.length > 0 && directions[0].direction === "") {
      const nextDirection = directions[1].direction;
      directions[0].direction = nextDirection;
    }

    // Group by directions
    const groupedDirections = directions.reduce(
      (acc, { from, to, direction }) => {
        if (!acc[direction]) acc[direction] = [];
        acc[direction].push({ from, to });
        return acc;
      },
      {}
    );

    return groupedDirections;
  }

  //// save and post draings ////

  let drawingObject = {
    mapExtent: undefined,
    Tools_InteractiveGraphicLayer: { animation: [], sketches: [] },
    BioDiversity_InteractiveGraphicLayer: { animation: [], sketches: [] },
    PeopleVehicles_InteractiveGraphicLayer: { animation: [], sketches: [] },
    Violation_InteractiveGraphicLayer: { animation: [], sketches: [] },
    More_InteractiveGraphicLayer: { animation: [], sketches: [] },
  };

  const [emptyNameWarning, setEmptyNameWarning] = useState(false);
  const [emptyGraphicsWarning, setEmptyGraphicsWarning] = useState(false);
  const [mapNameDublicationWarning, setMapNameDuplicationWarning] =
    useState(false);

  const renderWarningMessage = (text) => (
    <span
      style={{
        display: "inline-flex",
        alignItems: "center",
        padding: "4px 8px",
        borderRadius: "4px",
        fontSize: "16px",
        color: "#ff3333",
      }}
    >
      <span style={{ marginRight: "4px", fontSize: "18px" }}>⚠️</span>
      <span style={{ fontWeight: "bold" }}>{t(text, { ns: "common" })}</span>
    </span>
  );

  const handleSave = async () => {
    const drawingLayer = props.map.findLayerById("InteractiveMapGraphicLayer");
    let tempGraphics = drawingLayer.graphics.items.filter(
      (item) => item.id == "temp_graphic"
    );
    drawingLayer.graphics.removeMany(tempGraphics);
    let currentExtent;
    if (selectedZoneId) {
      const selectedZone = zonesList.find((zone) => zone.id == selectedZoneId);
      currentExtent = JSON.parse(selectedZone.extent);
    } else if (props.editDrawingData) {
      currentExtent = JSON.parse(props.editDrawingData.drawing).mapExtent;
    } else {
      currentExtent = props.map.view.extent.toJSON();
    }

    drawingObject.mapExtent = currentExtent;

    const distinctLayerNames = [
      ...new Set(
        props.map
          .findLayerById("InteractiveMapGraphicLayer")
          .graphics.items.map((item) => item._layerName)
      ),
    ];

    distinctLayerNames.forEach((layerName) =>
      saveGraphicsLayerDrawnGraphics(layerName)
    );

    if (formValues.mapName.trim().length == 0) {
      //  message.warning(t("emptyMapName", { ns: "common" }));
      setEmptyNameWarning(true);
    } else if (
      props.map.findLayerById("InteractiveMapGraphicLayer").graphics.length == 0
    ) {
      setEmptyGraphicsWarning(true);
    } else if (props.editDrawingData) {
      setEmptyNameWarning(false);
      setEmptyGraphicsWarning(false);
      setMapNameDuplicationWarning(false);
      let drawing = {
        id: props.editDrawingData.id,
        zoneId: props.editDrawingData.zoneId,
        is_public: props.editDrawingData.is_public,
        shared_departments: props.editDrawingData.shared_departments,
        shared_users: props.editDrawingData.shared_users,
        creator_name: props.editDrawingData.creator_name,
      };
      const result = await editDrawing(drawing);
      if (!result.success) {
        message.warning(t("ErrorRequest"));
      } else {
        window.__isInteractiveMapSaved = true;
        setMapNameModalVisible(false);
        message.warning(t("mapEditedSuccessfully", { ns: "common" }));
      }
    } else {
      setEmptyNameWarning(false);
      setEmptyGraphicsWarning(false);
      setMapNameDuplicationWarning(false);
      if (mapsNames.includes(formValues.mapName)) {
        setMapNameDuplicationWarning(true);
      } else {
        let result = await postDrawing(selectedZoneId);
        if (!result.success) {
          message.warning(t("ErrorRequest"));
        } else {
          window.__isInteractiveMapSaved = true;
          setFormValues(initialFormState);
          setMapNameModalVisible(false);
          message.warning(t("mapSavedSuccessfully", { ns: "common" }));
        }
      }
    }
  };

  const saveGraphicsLayerDrawnGraphics = (layerId) => {
    const layerGraphics = props.map
      .findLayerById("InteractiveMapGraphicLayer")
      .graphics.items.filter((graphic) => graphic._layerName == layerId);
    let animationIndexes = [];
    for (let index = 0; index < layerGraphics.length; index++) {
      let jsonGraphic = layerGraphics[index];
      if (jsonGraphic.symbol.url && jsonGraphic._animateId) {
        animationIndexes.push(index, index - 1);
        let symbolName = "";
        if (
          jsonGraphic._fileURL &&
          !jsonGraphic.symbol.url.includes("SubAttachments/uploadMultifiles")
        ) {
          symbolName = jsonGraphic._fileURL.endsWith(".docx")
            ? "word"
            : jsonGraphic._fileURL.endsWith(".pdf")
            ? "pdf"
            : jsonGraphic._fileURL.endsWith(".xlsx")
            ? "excel"
            : "";
        } else {
          if (
            !jsonGraphic._fileURL &&
            !jsonGraphic.symbol.url.includes("SubAttachments/uploadMultifiles")
          ) {
            symbolName = jsonGraphic.symbol.url.includes(
              "SubAttachments/uploadMultifiles"
            )
              ? jsonGraphic.symbol.url
              : jsonGraphic.symbol.url
                  .match(/\/([^/]+)\.[^/]+$/)[1]
                  .split(".")[0];

            symbolName = symbolName.replace(/_(right|left)/i, "");
          }
        }
        let graphicData = {
          symbol_name: symbolName,
          paths: layerGraphics[index - 1].geometry.toJSON(),
        };

        if (jsonGraphic._fileURL) {
          graphicData = {
            ...graphicData,
            fileURL: jsonGraphic._fileURL,
          };
        }
        drawingObject[layerId].animation.push(graphicData);
      } else if (
        jsonGraphic.symbol.url &&
        jsonGraphic._animateId == undefined
      ) {
        animationIndexes.push(index);
        let symbolName = "";
        if (
          jsonGraphic._fileURL &&
          !jsonGraphic.symbol.url.includes("SubAttachments/uploadMultifiles")
        ) {
          symbolName = jsonGraphic._fileURL.endsWith(".docx")
            ? "word"
            : jsonGraphic._fileURL.endsWith(".pdf")
            ? "pdf"
            : jsonGraphic._fileURL.endsWith(".xlsx")
            ? "excel"
            : "";
        } else {
          if (
            !jsonGraphic._fileURL &&
            !jsonGraphic.symbol.url.includes("SubAttachments/uploadMultifiles")
          ) {
            symbolName = jsonGraphic.symbol.url.includes(
              "SubAttachments/uploadMultifiles"
            )
              ? jsonGraphic.symbol.url
              : jsonGraphic.symbol.url
                  .match(/\/([^/]+)\.[^/]+$/)[1]
                  .split(".")[0];
          }
        }
        let graphicData = {
          symbol_name: symbolName,
          paths: {
            paths: [
              [
                [jsonGraphic.geometry.x, jsonGraphic.geometry.y],
                [jsonGraphic.geometry.x, jsonGraphic.geometry.y],
              ],
            ],
            spatialReference: jsonGraphic.geometry.spatialReference.toJSON(),
          },
        };

        if (jsonGraphic._fileURL) {
          graphicData = { ...graphicData, fileURL: jsonGraphic._fileURL };
        }
        if (
          jsonGraphic.symbol.url.includes("SubAttachments/uploadMultifiles")
        ) {
          graphicData = { ...graphicData, fileURL: jsonGraphic.symbol.url };
        }
        drawingObject[layerId].animation.push(graphicData);
      }
    }
    if (animationIndexes.length < layerGraphics.length) {
      const sketchesGraphics = layerGraphics.filter(
        (graphic, index) => !animationIndexes.includes(index)
      );
      sketchesGraphics.forEach((graphic) => {
        drawingObject[layerId].sketches.push(graphic.toJSON());
      });
    }
  };

  const postDrawing = async (zoneId) => {
    try {
      const postUrl = `${window.ApiUrl}/StudyDrawing`;
      const data = {
        name: formValues.mapName,
        zone_id: zoneId ? zoneId : null,
        region_id: selectedRegionId ? selectedRegionId : null,
        drawing: JSON.stringify(drawingObject),
      };
      const response = await axios.post(postUrl, data);
      return { success: true };
    } catch (error) {
      return { success: false };
    }
  };

  //// region and zone selection ////

  const getAllZones = async (regionId) => {
    await axios
      .get(
        `${window.ApiUrl}/StudyZone/GetAll?pageSize=100&filter_key=region_id&q=${regionId}`
      )
      .then((response) => {
        if (response.data.results.length > 0) {
          setZonesList([...response.data.results]);
        } else {
          setZonesList([]);
        }
      })
      .catch((error) => {
        message.warning(t("ErrorRequest"));
      });
  };

  const handleRegionSelection = async (region) => {
    setSelectedRegionId(region.id);
    setSelectedZoneIndex("");
    await getAllZones(region.id);
  };

  //// edit drawing ////

  const editDrawing = async (drawing) => {
    try {
      const putUrl = `${window.ApiUrl}/StudyDrawing/${drawing.id}`;
      await axios.put(putUrl, {
        zone_id: drawing.zoneId,
        drawing: JSON.stringify(drawingObject),
        name: formValues.mapName,
        is_public: drawing.is_public,
        creator_name: drawing.creator_name,
        shared_departments: drawing.shared_departments,
        shared_users: drawing.shared_users,
      });
      return { success: true };
    } catch (error) {
      return { success: false };
    }
  };

  /// input ///

  const handleChangeInput = (e) => {
    setFormValues({ ...formValues, [e.target.name]: e.target.value });
  };

  const initialFormState = { mapName: "" };
  const [formValues, setFormValues] = useState(initialFormState);

  const handleClearButton = () => {
    props.setEditDrawingData(undefined);
    props.defaultMapView();
    history.current.undoStack = [];
    history.current.redoStack = [];
  };

  const handleSaveButton = () => {
    setMapNameModalVisible(true);
    if (props.editDrawingData) {
      handleChangeInput({
        target: { name: "mapName", value: props.editDrawingData.name },
      });
    }
  };

  const getPrivateDrawings = async () => {
    await axios
      .get(`${window.ApiUrl}/StudyDrawing/GetAll?pageSize=100`)
      .then((response) => {
        let maps = response.data.results.map((map) => map.name);
        setMapsNames(maps);
      })
      .catch((error) => {
        message.warning(t("ErrorRequest"));
      });
  };

  return (
    <>
      <div
        className="interactiveMapTools"
        style={{
          position: "relative",
          overflow: "auto",
          // maxHeight: "70vh",
          maxHeight: "calc(100vh - 200px)",
          paddingInlineEnd: "5px",
          paddingBottom: "15px",
        }}
      >
        {!props.editDrawingData ? (
          <>
            {}
            {/* start amps */}
            <div
              style={{
                display: "flex",
                // alignItems: "center",
                flexDirection: "column",
                gap: "10px",
                // width: "90%",
                overflow: "auto",
                // paddingBlock: "10px",
                padding: "10px",
                marginBlock: "15px",
                whiteSpace: "nowrap",
              }}
              className="interactive_active_zonesList box"
            >
              <div style={{ display: "flex", gap: "5px" }}>
                <img
                  src={ineractiveVector}
                  alt="areas"
                  style={{
                    margin: "0 0 0 3px",
                    width: "16px",
                    filter:
                      "brightness(0) saturate(100%) invert(25%) sepia(14%) saturate(4134%) hue-rotate(190deg) brightness(92%) contrast(91%)",
                  }}
                />
                <span className="logo-span">{"مناطق الدراسة"}</span>
              </div>

              <div style={{ display: "flex", gap: "10px" }}>
                {props.regionsData.length > 0 &&
                  props.regionsData.map((region) => {
                    return (
                      <div
                        key={region.id}
                        id={region.id}
                        style={{
                          backgroundColor: selectedRegionId
                            ? selectedRegionId === region.id
                              ? "#338C9A"
                              : "#fff"
                            : "#fff",
                          color: selectedRegionId
                            ? selectedRegionId === region.id
                              ? "#fff"
                              : "#A8A8A8"
                            : "#A8A8A8",
                          padding: "5px 15px",
                          borderRadius: "20px",
                          cursor: "pointer",
                        }}
                        onClick={() => {
                          handleRegionSelection(region);
                        }}
                        className="selectedRegion"
                      >
                        {region.name}
                      </div>
                    );
                  })}
              </div>
            </div>
            {/* end amps */}

            {/* start areas */}
            {selectedRegionId && (
              <div
                style={{
                  display: "flex",
                  // alignItems: "center",
                  flexDirection: "column",
                  gap: "10px",
                  // width: "90%",
                  overflow: "auto",
                  // paddingBlock: "10px",
                  padding: "10px",
                  marginBlock: "15px",
                  whiteSpace: "nowrap",
                }}
                className="interactive_active_zonesList box"
              >
                <div style={{ display: "flex", gap: "5px" }}>
                  <img
                    src={interactiveGroup}
                    alt="areas"
                    style={{
                      margin: "0 0 0 3px",
                      width: "16px",
                      filter:
                        "brightness(0) saturate(100%) invert(25%) sepia(14%) saturate(4134%) hue-rotate(190deg) brightness(92%) contrast(91%)",
                    }}
                  />
                  <span className="logo-span">{"المناطق"}</span>
                </div>
                <div
                  style={{
                    display: "flex",
                    gap: "10px",
                  }}
                >
                  {zonesList.length > 0 &&
                    zonesList.map((element, indx) => {
                      return (
                        <div
                          key={indx}
                          id={element.id}
                          style={{
                            backgroundColor:
                              selectedZoneIndex === indx
                                ? "#338C9A"
                                : "#284587",
                            color: selectedZoneIndex === indx ? "#fff" : "#fff",
                            padding: "5px 15px",
                            borderRadius: "20px",
                            cursor: "pointer",
                          }}
                          onClick={(e) => {
                            setSelectedZoneIndex(indx);
                            setSelectedZoneId(element.id);
                          }}
                          className="selectedRegion"
                        >
                          {element.name}
                        </div>
                      );
                    })}
                </div>
              </div>
            )}
            {/* end areas */}
          </>
        ) : (
          <>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                gap: "10px",
                // width: "90%",
                overflow: "auto",
                paddingBlock: "10px",
                whiteSpace: "nowrap",
              }}
            >
              <div
                key={props.editDrawingData.id}
                id={props.editDrawingData.id}
                style={{
                  backgroundColor: "#B55433",
                  color: "#fff",
                  padding: "5px 15px",
                  borderRadius: "20px",
                  cursor: "pointer",
                }}
              >
                {props.editDrawingData.name}
              </div>
            </div>
          </>
        )}
        <div
          style={{
            // marginTop: "10px",
            display: "flex",
            gap: "10px",
            flexDirection: "column",
            marginBottom: "10px",
            // height: !selectedRegionId
            //   ? "calc(100vh - 340px)"
            //   : "calc(100vh - 450px)",
            // overflow: "auto",
            // paddingInlineEnd: "5px",
          }}
        >
          <ToolsBox
            ref={toolsBoxRef}
            map={props.map}
            updateGraphicState={updateGraphicState}
            generalSelectedItem={generalSelectedItem}
            setGeneralSelectedItem={setGeneralSelectedItem}
            generalSelectedBox={generalSelectedBox}
            setGeneralSelectedBox={setGeneralSelectedBox}
            history={history}
          />
          {/* <BiodiversityBox
            generalSelectedItem={generalSelectedItem}
            setGeneralSelectedItem={setGeneralSelectedItem}
            generalSelectedBox={generalSelectedBox}
            setGeneralSelectedBox={setGeneralSelectedBox}
            map={props.map}
            updateGraphicState={updateGraphicState}
            history={history}
          /> */}
          <PeopleVehiclesBox
            generalSelectedItem={generalSelectedItem}
            setGeneralSelectedItem={setGeneralSelectedItem}
            generalSelectedBox={generalSelectedBox}
            setGeneralSelectedBox={setGeneralSelectedBox}
            map={props.map}
            updateGraphicState={updateGraphicState}
            history={history}
          />
          <ViolationsBox
            generalSelectedItem={generalSelectedItem}
            setGeneralSelectedItem={setGeneralSelectedItem}
            generalSelectedBox={generalSelectedBox}
            setGeneralSelectedBox={setGeneralSelectedBox}
            map={props.map}
            updateGraphicState={updateGraphicState}
            history={history}
          />
          <MoreInteractiveTools
            generalSelectedItem={generalSelectedItem}
            setGeneralSelectedItem={setGeneralSelectedItem}
            generalSelectedBox={generalSelectedBox}
            setGeneralSelectedBox={setGeneralSelectedBox}
            map={props.map}
            updateGraphicState={updateGraphicState}
            history={history}
          />
        </div>

        <div
          style={{
            display: "flex",
            gap: "10px",
            flexDirection: "row",
            position: "fixed",
            bottom: "0",
            insetInlineStart: "10px",
            insetInlineEnd: "22px",
          }}
        >
          <button
            className="SearchBtn"
            size="large"
            block
            onClick={handleSaveButton}
          >
            {t("save")}
          </button>

          {props.editDrawingData && (
            <button
              className="SearchBtn mt-3"
              size="large"
              block
              onClick={handleClearButton}
            >
              {t("cancel", { ns: "common" })}
            </button>
          )}
        </div>
      </div>
      <Modal
        title={
          <div className="custom-modal-title">
            {t("enterMapName", { ns: "common" })}
          </div>
        }
        centered
        visible={mapNameModalVisible}
        onCancel={() => {
          setMapNameModalVisible(false);
          setEmptyNameWarning(false);
          setEmptyGraphicsWarning(false);
          setMapNameDuplicationWarning(false);
          setFormValues(initialFormState);
        }}
        // okText={t("confirmSave")}
        // cancelText={t("cancel")}
        className="shareModal sharmodalBookmark"
        closable={false}
        footer={
          <div className="footer_modal">
            <Button
              key="submit"
              type="primary"
              onClick={() => {
                handleSave();
              }}
              className="footer_buttonEdit_modal"
              style={{
                display: "flex",
                alignItems: "center",
                gap: "10px",
              }}
            >
              {/* <img src={PenImg} alt="" /> */}
              <FaCheck />
              <span className="me-1">{t("confirmSave", { ns: "common" })}</span>
            </Button>
            <Button
              key="cancel"
              onClick={() => {
                setMapNameModalVisible(false);
                setEmptyNameWarning(false);
                setEmptyGraphicsWarning(false);
                setMapNameDuplicationWarning(false);
                setFormValues(initialFormState);
              }}
              className="footer_buttonCancel_modal"
              style={{
                display: "flex",
                alignItems: "center",
                gap: "10px",
              }}
            >
              <IoMdClose color="red" />
              <span className="me-1">{t("close", { ns: "common" })}</span>
            </Button>
          </div>
        }
      >
        {/* <div
          style={{
            textAlign: "center",
            fontSize: "16px",
            fontWeight: "bold",
            marginBlock: "24px",
          }}
        >
          {"تعديل علي بيانات الخريطة" + `${formValues.mapName}`}
        </div> */}

        {/* <Input
          name="mapName"
          onChange={(e) => {
            handleChangeInput(e);
            setEmptyNameWarning(
              e.target.value === "" || e.target.value.trim().length == 0
            );
          }}
          value={formValues.mapName}
          placeholder={t("mapName")}
          maxLength={50}
        /> */}

        <Form.Item
          // label={t("bookmark")}
          label={
            <div>
              <span className="label_Bookmark">{t("mapName")}</span>
            </div>
          }
          className="select-cust"
          style={{ marginTop: "20px" }}
        >
          <Input
            name="mapName"
            onChange={(e) => {
              handleChangeInput(e);
              setEmptyNameWarning(
                e.target.value === "" || e.target.value.trim().length == 0
              );
            }}
            value={formValues.mapName}
            placeholder={t("mapName")}
            maxLength={50}
          />
        </Form.Item>

        {emptyNameWarning && renderWarningMessage("emptyMapName")}
        {emptyGraphicsWarning && renderWarningMessage("emptyGraphics")}
        {mapNameDublicationWarning &&
          renderWarningMessage("MapNameDuplication")}
        {/* 
        <div
          style={{
            display: "flex",
            gap: "10px",
            marginTop: "10px",
            justifyContent: "end",
          }}
        >
          <Button
            type="primary"
            style={{
              backgroundColor: "#b45333",
            }}
            onClick={() => {
              handleSave();
            }}
          >
            {t("confirmSave", { ns: "common" })}
          </Button>

          <Button
            type="primary"
            style={{
              backgroundColor: "#b45333",
            }}
            onClick={() => {
              setMapNameModalVisible(false);
              setEmptyNameWarning(false);
              setEmptyGraphicsWarning(false);
              setMapNameDuplicationWarning(false);
              setFormValues(initialFormState);
            }}
          >
            {t("close", { ns: "common" })}
          </Button>
        </div> */}
      </Modal>
    </>
  );
}
