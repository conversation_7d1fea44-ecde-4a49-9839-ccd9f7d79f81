import React, { Component, useEffect, useState } from "react";
import Fade from "react-reveal/Fade";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes } from "@fortawesome/free-solid-svg-icons";
import * as watchUtils from "@arcgis/core/core/watchUtils";
import { Resizable } from "re-resizable";

class GoogleMaps extends Component {
  state = {
    loc:
      this.props.map.view.extent.center.latitude +
      "," +
      this.props.map.view.extent.center.longitude,
    zoom: this.props.map.view.zoom,
    isDragging: false,
    diffX: 0,
    diffY: 0,
    position: { x: 60, y: 0 }, // initial position
  };

  componentDidMount() {
    watchUtils.whenTrue(this.props.map.view, "ready", () => {
      watchUtils.whenOnce(this.props.map.view, "extent", () => {
        watchUtils.when(this.props.map.view, "stationary", (evt) => {
          if (evt) {
            let point = this.props.map.view.extent.center;
            //latitude:
            //longitude:
            this.setState({
              loc: point.latitude + "," + point.longitude,
              zoom: this.props.map.view.zoom,
            });
          }
        });
      });
    });
    //window.handles.remove("map-events");
  }

  
  // Drag handlers ONLY attached to Heading_tocComponent
  onMouseDown = (e) => {
    e.preventDefault();

    const diffX = e.clientX - this.state.position.x;
    const diffY = e.clientY - this.state.position.y;

    this.setState({
      isDragging: true,
      diffX,
      diffY,
    });

    document.addEventListener("mousemove", this.onMouseMove);
    document.addEventListener("mouseup", this.onMouseUp);
  };

  onMouseMove = (e) => {
    if (!this.state.isDragging) return;

    const { diffX, diffY } = this.state;
    const newX = e.clientX - diffX;
    const newY = e.clientY - diffY;

    // Get viewport size
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // Get component size
    const box = this.containerRef.getBoundingClientRect();
    const boxWidth = box?.width;
    const boxHeight = box?.height;

    // Clamp values within the viewport and stop at bottom - 200px
    const clampedX = Math.min(Math.max(0, newX), windowWidth - boxWidth);
    const clampedY = Math.min(
      Math.max(0, newY),
      windowHeight - boxHeight + 120
    );

    this.setState({
      position: {
        x: clampedX,
        y: clampedY,
      },
    });
  };

  onMouseUp = () => {
    this.setState({ isDragging: false });
    document.removeEventListener("mousemove", this.onMouseMove);
    document.removeEventListener("mouseup", this.onMouseUp);
  };
  render() {
    const { x, y, isDragging } = this.state.position;

    return (
      <Fade left collapse>
        <div
          // className="SmallMapPopup"
          ref={(ref) => (this.containerRef = ref)}
          style={{
            position: "absolute",
            top: this.state.position.y,
            left: this.state.position.x,
            zIndex: 1000,
            cursor: isDragging ? "grabbing" : "default",
            userSelect: "none",
            minWidth: "350px",
            background: "white",
            border: "1px solid #ccc",
            borderRadius: "16px",
            boxShadow:
              "0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%)",
            // padding: "16px",
          }}
        >
          <Resizable
            className="leftToolMenu"
            defaultSize={{
              width: 500,
              height: "420",
            }}
            // minHeight={300}
            maxWidth={800}
            maxHeight={600}
            bounds="window"
          >
            <span
              style={{
                width: "100%",
                float: "left",
                textAlign: "left",
                marginLeft: "5px",
                
              }}
              className="Heading_tocComponent"
              onMouseDown={this.onMouseDown}
            >
              <FontAwesomeIcon
                icon={faTimes}
                style={{
                  marginTop: "5px",
                  marginRight: "5px",
                  cursor: "pointer",
                }}
                onClick={this.props.closeToolsData}
              />
            </span>
            <iframe
              src={
                "https://www.google.com/maps/embed/v1/place?q=" +
                this.state.loc +
                "&zoom=" +
                this.state.zoom +
                "&key=AIzaSyBFw0Qbyq9zTFTd-tUY6dZWTgaQzuU17R8"
              }
              style={{ width: "100%", height: "93%", border: "0" }}
              title="mapExplorer"
              frameborder="0"
              allowfullscreen
            ></iframe>
          </Resizable>
        </div>
        {/* <div className=" inquiryTool">
         
        </div> */}
      </Fade>
    );
  }
}
export default GoogleMaps;
