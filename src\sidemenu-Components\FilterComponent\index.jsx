import React, { Component } from "react";
import { Select, Input, message, Spin, DatePicker } from "antd";
import arrow_drop_down from "../../assets/icons/arrow_drop_down.svg";
import arrow_drop_up from "../../assets/icons/arrow_drop_up.svg";
import {
  clearGraphicLayer,
  convertHirjiDateToTimeSpan,
  getFeatureDomainName,
  getLayerId,
  isLayerExist,
  queryTask,
  showLoading,
  zoomToFeatureByFilter,
  convertToArabic,
  convertToEnglish,
} from "../../helper/common_func";
// import { layersSetting } from "../../helper/layers";
import locale from "antd/es/date-picker/locale/ar_EG";
import { DownCircleFilled } from "@ant-design/icons";
import Checkbox from "antd/lib/checkbox/Checkbox";
import Graphic from "@arcgis/core/Graphic";
import * as geometryEngine from "@arcgis/core/geometry/geometryEngine";
import globalStore from "../../helper/globalVarabileStore";
import HijriDatePicker from "../../components/hijriDatePicker/components/HijriDatePicker";
import { withTranslation } from "react-i18next";
import axios from "axios";
import Loader from "../../containers/Loader";
import { PARCEL_LANDS_LAYER_NAME } from "../../helper/constants";
import moment from "moment-hijri";
import i18n from "../../i18n";
import {
  alphbetNumbers,
  megawrahNumbersInString,
  municipilitiesForRoyal,
  randomPlanNumbers,
  subdivisionTypes,
} from "../../helper/layers";
import { getDistinctValuesFromArray } from "../../helper/utilsFunc";
import Box from "@mui/material/Box";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
// import Select, { SelectChangeEvent } from '@mui/material/Select';
// import { layer } from "@fortawesome/fontawesome-svg-core";
function getFetchUrlForPrivacyLandLists(params, fieldName, url) {
  let { munCode, planNo } = params;
  switch (fieldName) {
    case "DISTRICT_NAME":
      return url + `munCode=${munCode ? munCode : ""}`;
    case "PLAN_NO":
      return url + `munCode=${munCode ? munCode : ""}`;
    case "SUBDIVISION_TYPE":
      return (
        url + `munCode=${munCode ? munCode : ""}&planNo=${planNo ? planNo : ""}`
      );
    case "baladia_doc_no":
      return (
        url + `munCode=${munCode ? munCode : ""}&planNo=${planNo ? planNo : ""}`
      );
    default:
      break;
  }
}
function getFetchUrlForRoyalSalesLandLists(params, fieldName, url) {
  let { munCode, planNo } = params;
  switch (fieldName) {
    case "PLAN_NO":
      return url + `cityId=${munCode}`;
    default:
      return url + `cityId=${munCode}&planNo=${planNo ? planNo : ""}`;
  }
}
class FilterComponent extends Component {
  state = globalStore.searchCriteria || {
    searchLayer: null,
    searchLayers: [],
    formValues: {},
    searchFields: [],
    isActiveBufferSearch: false,
    showInfo: false,
    noData: false,
    loading: false,
    filterText: [],
    open1: false,
  };
  self = this;
  mapPoint = null;

  componentDidMount() {
    window.__moment = moment;
    let layersSetting = this.props.mainData.layers;
    this.setState({
      searchLayers: Object.keys(layersSetting)
        .filter((key) => !layersSetting[key].hideFromSearchs)
        .map((key) => {
          return {
            layerName: key,
            layer: layersSetting[key],
            name: layersSetting[key].name,
          };
        })
        .filter((l) => {
          if (
            [
              "PARCEL_PRIVACY",
              "LGR_ROYAL",
              "OLD_Landbase_Parcel",
              "SALES_LANDS",
            ].includes(l.layerName)
          )
            return true;
          else
            return (
              l.layer.isPublicSearchable &&
              l.layer.searchFields &&
              isLayerExist(this.props.map.__mapInfo, l.layerName)
            );
        }),
    });
    let areMoreThenOneSearchInput = layersSetting[
      this.state.searchLayer
    ]?.searchFields.filter((x) => x.isSearch);
    const filterText =
      areMoreThenOneSearchInput && areMoreThenOneSearchInput?.length > 1
        ? this.state.searchLayer &&
          layersSetting[this.state.searchLayer].searchFields.filter(
            (x) => x.isSearch
          )
        : this.state.searchLayer &&
          layersSetting[this.state.searchLayer].searchFields.find(
            (x) => x.isSearch
          );
    this.setState({ filterText });
  }
  componentWillUnmount() {
    clearGraphicLayer("ZoomGraphicLayer", this.props.map);
  }
  customMunicipilitySort(a, b, fieldName = "MUNICIPALITY_NAME") {
    const priorityNames = ["الدمام", "الخبر", "الظهران"];
    const indexA = priorityNames.indexOf(a.attributes[fieldName].toLowerCase()); // Use toLowerCase for case-insensitive comparison
    const indexB = priorityNames.indexOf(b.attributes[fieldName].toLowerCase());

    // If either is in the priority list, prioritize it
    if (indexA !== -1) return indexA - (indexB !== -1 ? indexB : Infinity);
    if (indexB !== -1) return Infinity - indexA;

    // Otherwise, sort alphabetically (regular string comparison)
    return a.attributes[fieldName].localeCompare(b.attributes[fieldName], "ar");
  }
  handleLayerSelect = () => (layer) => {
    this.setState({
      searchLayer: layer,
      showInfo: false,
      noData: false,
      formValues: {},
      searchFields: [],
      isActiveBufferSearch: false,
      open1: !this.state.open1,
    });
    if (["PARCEL_PRIVACY", "LGR_ROYAL", "SALES_LANDS"].includes(layer))
      this.getListsValueExternalServices(layer);
    else this.getListsValue(layer);
  };

  // for gis layers/tables
  getListsValue = (layer, getListsAfterFieldName, parentFilter) => {
    let layersSetting = this.props.mainData.layers;
    let isOldLandsBlock = layer === "OLD_Landbase_Parcel";
    let isPrivateLandLayer = layer === "PARCEL_PRIVACY";

    //get all filters
    let promiseQueries = [];
    let fieldsName = [],
      filterQuery = "";
    let layerdId = getLayerId(
      this.props.map.__mapInfo,
      [
        "PARCEL_PRIVACY",
        "LGR_ROYAL",
        "OLD_Landbase_Parcel",
        "SALES_LANDS",
      ].includes(layer)
        ? "Landbase_Parcel"
        : layer
    );
    layersSetting[layer]?.searchFields
      ?.filter((x) => !x.isSearch)
      .forEach((item, index) => {
        console.log({ item });
        if (!getListsAfterFieldName) {
          // if(item.dontAddToEsriQuery) { return;}
          fieldsName.push(item.field);

          filterQuery = parentFilter
            ? parentFilter + " and " + item.field + " is not null"
            : "1=1";

          if (isOldLandsBlock)
            filterQuery =
              filterQuery +
              ` and PLAN_NO IN ('${randomPlanNumbers.join("','")}')`;
          else if (isPrivateLandLayer && item.field === "PARCEL_PLAN_NO")
            filterQuery = filterQuery + ` and PARCEL_MAIN_LUSE=30`; //PARCEL_MAIN_LUSE =30 خدمات عامة
          console.log({ filterQuery });
          promiseQueries.push(
            queryTask({
              url: window.mapUrl + "/" + layerdId,
              where: filterQuery,
              outFields:
                item.zoomLayer &&
                item.zoomLayer.filterField &&
                !item.zoomLayer.isNotSameAttributeNameInLayer
                  ? [item.field, item.zoomLayer.filterField]
                  : [item.field],
              returnGeometry: false,
              returnExecuteObject: true,
              returnDistinctValues: true,
              notShowLoading: true,
            })
          );
        } else {
          if (item.field == getListsAfterFieldName)
            getListsAfterFieldName = null;
        }
      });

    if (promiseQueries.length > 0) showLoading(true);
    else {
      this.setState(
        {
          searchFields: layersSetting[layer]?.searchFields?.filter(
            (x) => !x.isSearch
          ),
        },
        () => {
          showLoading(false);
        }
      );
    }

    if (!this.state.formValues["MUNICIPALITY_NAME"]) {
      promiseQueries = promiseQueries[0] ? [promiseQueries[0]] : [];
      fieldsName = fieldsName[0] ? [fieldsName[0]] : [];
      let isPriortyZoneFieldExist = layersSetting[layer]?.searchFields.find(
        (i) => i.field === "PRIORITYZONENAME"
      );
      if (isPriortyZoneFieldExist) {
        promiseQueries.push(
          queryTask({
            url: window.mapUrl + "/" + layerdId,
            where: filterQuery,
            outFields: "PRIORITYZONENAME",
            returnGeometry: false,
            returnExecuteObject: true,
            returnDistinctValues: true,
            notShowLoading: true,
          })
        );
        fieldsName.push("PRIORITYZONENAME");
      }
    }
    if (promiseQueries.length)
      Promise.all(promiseQueries)
        .then((resultsData) => {
          this.mapResultWithDomain(resultsData, fieldsName, layerdId).then(
            (data) => {
              // showLoading(true);
              data.forEach((item, index) => {
                let searchField = layersSetting[layer]?.searchFields?.find(
                  (x) => x.field == fieldsName[index]
                );
                if (item.features.length > 0) {
                  searchField.dataList =
                    fieldsName[index] === "MUNICIPALITY_NAME"
                      ? [
                          ...item.features
                            .filter(
                              (f) =>
                                typeof f.attributes["MUNICIPALITY_NAME"] ===
                                "string"
                            )
                            // todo: sort municipilities [damam, khobar, zahran, ...rest]
                            .sort(
                              this.customMunicipilitySort,
                              "MUNICIPALITY_NAME"
                            ),
                        ]
                      : [...item.features].filter((i) => {
                          if (searchField?.zoomLayer?.filterField) {
                            return i.attributes[
                              searchField.zoomLayer.filterField
                            ];
                          } else return i;
                        });
                  if (fieldsName[index] === "PRIORITYZONENAME")
                    searchField.dataList.unshift({
                      attributes: {
                        PRIORITYZONENAME: "الكل",
                      },
                    });
                } else {
                  searchField.dataList = [];
                  if (fieldsName[index] === "PRIORITYZONENAME")
                    searchField.dataList.unshift({
                      attributes: {
                        PRIORITYZONENAME: "الكل",
                      },
                    });
                }
              });
              this.setState(
                {
                  searchFields: this.state.formValues["MUNICIPALITY_NAME"]
                    ? layersSetting[layer]?.searchFields?.filter(
                        (x) => !x.isSearch
                      )
                    : layersSetting[layer]?.searchFields?.filter(
                        (x) =>
                          x.field == "MUNICIPALITY_NAME" ||
                          x.field === "PRIORITYZONENAME"
                      ),
                  formValues: { ...this.state.formValues },
                  loading: false,
                },
                () => {
                  showLoading(false);
                }
              );
              // showLoading(false);
            }
          );
        })
        .catch((err) => {
          console.log({ err });
          this.setState({ loading: false });
          if (err?.response?.status === 401) {
            //logut
            message.error(this.props.t("common:sessionFinished"));
            localStorage.removeItem("user");
            localStorage.removeItem("token");
            window.open(window.hostURL + "/home/<USER>", "_self");
          } else message.error(this.props.t("common:retrievError"));
        });
  };

  // for fetched external data like ParcelPrivacy
  getListsValueExternalServices = async (layer, getListsAfterFieldName) => {
    let { mainData } = this.props;
    let layersSetting = mainData.layers;

    let token = mainData.user?.token;
    if (!getListsAfterFieldName) {
      let munFieldName = "MUNICIPALITY_NAME";
      // fetch the mun data and parse to to an array like the gis data lists
      let fetchURL = layersSetting[layer]?.searchFields?.find(
        (x) => x.field === munFieldName
      )?.fetchURL;
      try {
        let { data: munList } = await axios.get(window.webApiUrl + fetchURL, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        munList = munList.map((item) => {
          return {
            attributes: {
              [munFieldName]: item.name,
              [munFieldName + "_Code"]: this.getGISMunCode(item.name),
              [munFieldName + "_NonGISCode"]: item.code,
            },
          };
        });
        let searchField = layersSetting[layer]?.searchFields?.find(
          (x) => x.field === munFieldName
        );
        // todo: sort municipilities [damam, khobar, zahran, ...rest]
        searchField.dataList = [
          ...munList
            .filter((f) => typeof f.attributes[munFieldName] === "string")
            .sort(this.customMunicipilitySort, munFieldName),
        ];
        this.setState(
          {
            searchFields: this.state.formValues[munFieldName]
              ? layersSetting[layer]?.searchFields?.filter((x) => !x.isSearch)
              : layersSetting[layer]?.searchFields?.filter(
                  (x) =>
                    x.field === munFieldName || x.field === "PRIORITYZONENAME"
                ),
            formValues: { ...this.state.formValues },
            loading: false,
          },
          () => {
            showLoading(false);
          }
        );
      } catch (err) {
        console.log({ err });
        this.setState({ loading: false });
        if (err?.response?.status === 401) {
          //logut
          message.error(this.props.t("common:sessionFinished"));
          localStorage.removeItem("user");
          localStorage.removeItem("token");
          window.open(window.hostURL + "/home/<USER>", "_self");
        } else message.error(this.props.t("common:retrievError"));
      }
    } else {
      if (layer === "PARCEL_PRIVACY")
        this.getListsByFieldNameForPrivacyParcels(
          getListsAfterFieldName,
          layer
        );
      else
        this.getListsByFieldNameForRoyalSalesParcels(
          getListsAfterFieldName,
          layer
        );
    }
  };
  mapResultWithDomain = (results, fieldsName, layerId) => {
    return new Promise((resolve, reject) => {
      let count = fieldsName.length;

      results.forEach((item, index) => {
        getFeatureDomainName(item.features, layerId).then((domainResult) => {
          item.features = domainResult;

          --count;
          if (count < 1) {
            resolve(results);
          }
        });
      });
    });
  };

  selectChange = (name, item) => (e, option) => {
    console.log(item, e, name, option);
    let layersSetting = this.props.mainData.layers;
    // let isOldLandsBlock = this.state.searchLayer === "OLD_Landbase_Parcel";

    this.setState({ showInfo: false, noData: false });
    if (!e) {
      clearGraphicLayer("ZoomGraphicLayer", this.props.map);
      if (
        this.state.searchLayer === "PARCEL_PRIVACY" &&
        name === "SUBDIVISION_TYPE"
      )
        this.setState({
          [name]: undefined,
          SUBDIVISION_DESCRIPTION: undefined,
        });
      else this.setState({ [name]: undefined });
    }

    // let engNum = convertToEnglish(e);
    this.setState(
      { formValues: { ...this.state.formValues, [name]: e } },
      () => {
        // showLoading(true);
        let searchField = layersSetting[
          this.state.searchLayer
        ].searchFields.find(
          (i) =>
            [i.field, i?.zoomLayer?.filterField].includes(name) && !i.isSearch
        );
        if (searchField) {
          let filterQuery = [];

          if (searchField.zoomLayer) {
            let item = searchField.dataList.find(
              (x) =>
                (x.attributes[name + "_Code"] || x.attributes[name]) ==
                this.state.formValues[name]
            );

            if (item) {
              let where = "";
              if (searchField.zoomLayer.isNotSameAttributeNameInLayer) {
                where =
                  searchField.zoomLayer.filterField +
                  "=" +
                  "'" +
                  (item.attributes[searchField.field + "_Code"] ||
                    item.attributes[searchField.zoomLayer.field]) +
                  "'";
              } else {
                let layer = this.state.searchLayer;
                where = getWhereClauseForZoom(item, this.state.formValues);
                function getWhereClauseForZoom(item, formValues) {
                  let whQuery = [];
                  if (
                    !["PARCEL_PRIVACY", "LGR_ROYAL", "SALES_LANDS"].includes(
                      layer
                    )
                  ) {
                    if (
                      item.attributes[
                        searchField.zoomLayer.filterField + "_Code"
                      ] ||
                      item.attributes[searchField.zoomLayer.filterField]
                    ) {
                      return (
                        searchField.zoomLayer.filterField +
                        "=" +
                        "'" +
                        (item.attributes[
                          searchField.zoomLayer.filterField + "_Code"
                        ] ||
                          item.attributes[searchField.zoomLayer.filterField]) +
                        "'"
                      );
                    } else {
                      Object.keys(formValues).forEach((key) => {
                        if (key !== "PRIORITYZONENAME" && formValues[key])
                          whQuery.push(key + "='" + formValues[key] + "'");
                        else if (
                          key === "PRIORITYZONENAME" &&
                          formValues[key] === "الكل"
                        )
                          whQuery.push(key + " IS NOT Null");
                      });
                      return whQuery.join(" and ");
                    }
                  } else if (layer === "LGR_ROYAL") {
                    if (
                      item.attributes[
                        searchField.zoomLayer.filterField + "_Code"
                      ] ||
                      item.attributes[searchField.zoomLayer.filterField]
                    ) {
                      return (
                        searchField.zoomLayer.filterField +
                        "=" +
                        "'" +
                        (item.attributes[
                          searchField.zoomLayer.filterField + "_Code"
                        ] ||
                          item.attributes[searchField.zoomLayer.filterField]) +
                        "'"
                      );
                    } else {
                      Object.keys(formValues).forEach((key) => {
                        if (
                          key !== "PRIORITYZONENAME" &&
                          formValues[key] &&
                          !["districtNo", "centerNo", "categoryNo"].includes(
                            key
                          )
                        )
                          whQuery.push(key + "='" + formValues[key] + "'");
                        else if (
                          key === "PRIORITYZONENAME" &&
                          formValues[key] === "الكل"
                        )
                          whQuery.push(key + " IS NOT Null");
                      });
                      return whQuery.join(" and ");
                    }
                  } else {
                    if (
                      item.attributes[
                        searchField.zoomLayer.filterField + "_Code"
                      ] ||
                      item.attributes[searchField.zoomLayer.filterField]
                    ) {
                      return (
                        searchField.zoomLayer.filterField +
                        "=" +
                        "'" +
                        (item.attributes[
                          searchField.zoomLayer.filterField + "_Code"
                        ] ||
                          item.attributes[searchField.zoomLayer.filterField]) +
                        "'"
                      );
                    }
                    Object.keys(formValues).forEach((key) => {
                      if (key !== "PRIORITYZONENAME" && formValues[key])
                        whQuery.push(key + "='" + formValues[key] + "'");
                      else if (
                        key === "PRIORITYZONENAME" &&
                        formValues[key] === "الكل"
                      )
                        whQuery.push(key + " IS NOT Null");
                    });
                    return whQuery.join(" and ");
                  }
                }
              }
              let isPArcelNumberInPrivacyOrRoyal =
                name === "PARCEL_PLAN_NO" &&
                ["LGR_ROYAL", "PARCEL_PRIVACY", "SALES_LANDS"].includes(
                  this.state.searchLayer
                );
              if (e && !isPArcelNumberInPrivacyOrRoyal)
                zoomToFeatureByFilter(
                  // isOldLandsBlock ? where +` and PLAN_NO IN ('${randomPlanNumbers.join("','")}')`:
                  where,
                  searchField.zoomLayer.name,
                  this.props.map
                );
            }
          }

          this.state.formValues = this.deleteChildValues(name);

          Object.keys(this.state.formValues).forEach((key) => {
            if (key !== "PRIORITYZONENAME" && this.state.formValues[key])
              filterQuery.push(key + "='" + this.state.formValues[key] + "'");
            else if (
              key === "PRIORITYZONENAME" &&
              this.state.formValues[key] === "الكل"
            )
              filterQuery.push(key + " IS NOT Null");
          });
          if (
            ["LGR_ROYAL", "PARCEL_PRIVACY", "SALES_LANDS"].includes(
              this.state.searchLayer
            )
          )
            this.getListsValueExternalServices(
              this.state.searchLayer,
              name,
              filterQuery.join("&")
            );
          else
            this.getListsValue(
              this.state.searchLayer,
              name,
              filterQuery.join(" and ")
            );
        }
        // showLoading(false);
      }
    );
  };

  deleteChildValues = (name) => {
    let layersSetting = this.props.mainData.layers;
    let found = false;
    layersSetting[this.state.searchLayer].searchFields.forEach((item) => {
      if (found) {
        delete this.state.formValues[item.field];
        delete this.state[item.field];
      }
      if (item.field == name) {
        found = true;
      }
    });

    return this.state.formValues;
  };

  handleChangeInput = (e) => {
    console.log({ name: e.target.name, value: e.target.value });
    this.setState({
      showInfo: false,
      noData: false,
      formValues: { ...this.state.formValues, [e.target.name]: e.target.value },
    });
  };

  handleBufferSearch = (e) => {
    this.setState({
      showInfo: false,
      noData: false,
      buffer_distance: e.target.value,
    });
  };

  searchForData = (e) => {
    this.props.map.findLayerById("identifyGraphicLayer").removeAll();
    this.props.map.view.graphics.removeAll();
    if (this.state.isActiveBufferSearch) {
      this.setState({ showInfo: true });
      window.unSubscribeMapEvents();
      window.handles.push(
        this.props.map.view.on("click", (event) => {
          this.props.map.view.graphics.removeAll();
          const buffer = geometryEngine.buffer(
            event.mapPoint,
            +this.state.buffer_distance,
            "meters"
          );
          var bufferGraphic = new Graphic({
            geometry: buffer,
            symbol: {
              type: "simple-fill",
              color: "rgba(255, 0, 0, 0.3)",
            },
          });
          this.props.map.view.graphics.add(bufferGraphic);

          //handler.remove();
          this.setState({ showInfo: false });

          this.getSearchData(buffer);
        })
      );
    } else {
      this.getSearchData();
    }
  };

  async getRoyalPrivateSalesParcelData(type) {
    try {
      showLoading(true);
      let url = "",
        whereClause = "";
      let parcelSpId = this.state.formValues["PARCEL_SPATIAL_ID"];
      let districtCode = this.state.formValues["DISTRICT_NAME"];
      let parcelNoValue; // = this.state.formValues["PARCEL_PLAN_NO"];

      if (parcelSpId) {
        let parcelList = this.state.searchFields.find(
          (i) => i.field === "PARCEL_PLAN_NO"
        );
        parcelNoValue = parcelList?.dataList?.find(
          (i) => i.attributes["PARCEL_SPATIAL_ID"] === parcelSpId
        )?.attributes?.PARCEL_PLAN_NO;
      }
      if (type === "royal") {
        url = window.webApiUrl + "/RoyalGrants/LandsInfo?isBrief=false";
        let munCode = this.state.formValues["MUNICIPALITY_NAME"];
        let munCodeNonGIS, districtNoName, centerNoName, categoryNoName;
        let munList =
          this.state.searchFields.find(
            (i) => i.field === "MUNICIPALITY_NAME"
          ) || [];
        let districtList =
          this.state.searchFields.find((i) => i.field === "districtNo") || [];
        let centersList =
          this.state.searchFields.find((i) => i.field === "centerNo") || [];
        let categoryList =
          this.state.searchFields.find((i) => i.field === "categoryNo") || [];
        if (munCode)
          munCodeNonGIS = munList?.dataList?.find(
            (i) => i.attributes["MUNICIPALITY_NAME_Code"] === munCode
          )?.attributes?.MUNICIPALITY_NAME_NonGISCode;
        let planNo = this.state.formValues["PLAN_NO"];
        let districtNo = this.state.formValues["districtNo"];
        if (districtNo)
          districtNoName = districtList?.dataList?.find(
            (i) => i.attributes["districtNo_Code"] === districtNo
          )?.attributes?.["districtNo"];
        let centerNo = this.state.formValues["centerNo"];
        if (centerNo)
          centerNoName = centersList?.dataList?.find(
            (i) => i.attributes["centerNo_Code"] === centerNo
          )?.attributes?.["centerNo"];
        let categoryNo = this.state.formValues["categoryNo"];
        if (categoryNo)
          categoryNoName = categoryList?.dataList?.find(
            (i) => i.attributes["categoryNo_Code"] === categoryNo
          )?.attributes?.["categoryNo"];
        let blockNo = this.state.formValues["blockNo"];
        let parcelNum = this.state.formValues["PARCEL_PLAN_NO"];
        if (munCode) {
          whereClause = ` MUNICIPALITY_NAME=${munCode} `;
          url += `&cityId=${munCodeNonGIS}`;
        }
        if (planNo) {
          whereClause += `${whereClause ? " AND " : ""} PLAN_NO='${planNo}' `;
          url += `&planNo=${planNo}`;
        }
        if (districtNo) {
          whereClause += this.getWhereClauseForRoyalSales(
            "districtNo",
            districtNoName
          );
          url += `&districtNo=${districtNo}`;
        }

        if (centerNo) {
          whereClause += this.getWhereClauseForRoyalSales(
            "centerNo",
            centerNoName
          );
          url += `&centerNo=${centerNo}`;
        }
        if (categoryNo) {
          whereClause += this.getWhereClauseForRoyalSales(
            "categoryNo",
            categoryNoName
          );
          url += `&categoryNo=${categoryNo}`;
        }
        if (blockNo) {
          whereClause += ` ${
            whereClause ? " AND " : ""
          } PARCEL_BLOCK_NO='${blockNo}' `;
          url += `&blockNo=${blockNo}`;
        }
        if (this.state.formValues["PARCEL_SPATIAL_ID"]) {
          url += `&spId=${parcelSpId}`;
        }
        if (parcelNum) {
          url += `&landNo=${parcelNum}`;
        }
        url += `&fromDate=${
          this.state.formValues["fromDate"]
            ? this.state.formValues["fromDate"]
            : ""
        }&toDate=${
          this.state.formValues["toDate"] ? this.state.formValues["toDate"] : ""
        }&`;
      } else if (type === "sales") {
        url = window.webApiUrl + "/SalesLands/LandsInfo?isBrief=false";
        let munCode = this.state.formValues["MUNICIPALITY_NAME"];
        let munCodeNonGIS,
          // districtNoName, centerNoName,
          categoryNoName;
        let munList =
          this.state.searchFields.find(
            (i) => i.field === "MUNICIPALITY_NAME"
          ) || [];
        // let districtList =
        // this.state.searchFields.find(
        //   (i) => i.field === "districtNo"
        // ) || [];
        // let centersList =
        // this.state.searchFields.find(
        //   (i) => i.field === "centerNo"
        // ) || [];
        let categoryList =
          this.state.searchFields.find((i) => i.field === "categoryNo") || [];
        if (munCode)
          munCodeNonGIS = munList?.dataList?.find(
            (i) => i.attributes["MUNICIPALITY_NAME_Code"] === munCode
          )?.attributes?.MUNICIPALITY_NAME_NonGISCode;
        let planNo = this.state.formValues["PLAN_NO"];
        // let districtNo = this.state.formValues["districtNo"];
        // if (districtNo)
        //   districtNoName = districtList?.dataList?.find(
        //     (i) => i.attributes["districtNo_Code"] === districtNo
        //   )?.attributes?.["districtNo"];
        // let centerNo =
        //   this.state.formValues["centerNo"];
        // if (centerNo)
        //   centerNoName = centersList?.dataList?.find(
        //     (i) => i.attributes["centerNo_Code"] === centerNo
        //   )?.attributes?.["centerNo"];
        let categoryNo = this.state.formValues["categoryNo"];
        if (categoryNo)
          categoryNoName = categoryList?.dataList?.find(
            (i) => i.attributes["categoryNo"] === categoryNo
          )?.attributes?.["categoryNo"];
        let blockNo = this.state.formValues["blockNo"];
        let parcelNum = this.state.formValues["PARCEL_PLAN_NO"];
        if (munCode) {
          whereClause = ` MUNICIPALITY_NAME=${munCode} `;
          url += `&cityId=${munCodeNonGIS}`;
        }
        if (planNo) {
          whereClause += `${whereClause ? " AND " : ""} PLAN_NO='${planNo}' `;
          url += `&planNo=${planNo}`;
        }
        // if (districtNo) {
        //   whereClause += this.getWhereClauseForRoyalSales('districtNo', districtNoName);
        //   url += `&districtNo=${districtNo}`;

        // }

        // if (centerNo) {
        //   whereClause += this.getWhereClauseForRoyalSales('centerNo', centerNoName);
        //   url += `&centerNo=${centerNo}`;
        // }
        if (categoryNo) {
          // whereClause += this.getWhereClauseForRoyalSales('categoryNo', categoryNoName);
          url += `&categoryNo=${categoryNo}`;
        }
        if (blockNo) {
          whereClause += ` ${
            whereClause ? " AND " : ""
          } PARCEL_BLOCK_NO='${blockNo}' `;
          url += `&blockNo=${blockNo}`;
        }
        if (this.state.formValues["PARCEL_SPATIAL_ID"]) {
          url += `&spId=${parcelSpId}`;
        }
        if (parcelNum) {
          url += `&landNo=${parcelNum}`;
        }
        url += `&fromDate=${
          this.state.formValues["fromDate"]
            ? this.state.formValues["fromDate"]
            : ""
        }&toDate=${
          this.state.formValues["toDate"] ? this.state.formValues["toDate"] : ""
        }&`;
      } else {
        let districtValue;
        let districtList =
          this.state.searchFields.find((i) => i.field === "DISTRICT_NAME") ||
          [];
        if (districtCode)
          districtValue = districtList?.dataList?.find(
            (i) => i.attributes["DISTRICT_NAME_Code"] === districtCode
          )?.attributes?.DISTRICT_NAME;
        if (this.state.formValues["MUNICIPALITY_NAME"])
          whereClause = `MUNICIPALITY_NAME=${this.state.formValues["MUNICIPALITY_NAME"]}`;
        if (this.state.formValues["PLAN_NO"])
          whereClause += `${whereClause ? "AND" : ""} PLAN_NO='${
            this.state.formValues["PLAN_NO"]
          }'`;
        if (this.state.formValues["DISTRICT_NAME"])
          whereClause += `${whereClause ? "AND" : ""} DISTRICT_NAME=${
            this.state.formValues["DISTRICT_NAME"]
          }`;
        if (this.state.formValues["SUBDIVISION_TYPE"])
          whereClause += `${whereClause ? "AND" : ""} SUBDIVISION_TYPE=${
            this.state.formValues["SUBDIVISION_TYPE"]
          }`;
        if (this.state.formValues["SUBDIVISION_TYPE"])
          whereClause += `${whereClause ? "AND" : ""} SUBDIVISION_TYPE=${
            this.state.formValues["SUBDIVISION_TYPE"]
          }`;
        if (this.state.formValues["SUBDIVISION_DESCRIPTION"])
          whereClause += `${whereClause ? "AND" : ""} SUBDIVISION_DESCRIPTION=${
            this.state.formValues["SUBDIVISION_DESCRIPTION"]
          }`;
        if (this.state.formValues["PARCEL_SPATIAL_ID"])
          whereClause += `${
            whereClause ? "AND" : ""
          } PARCEL_SPATIAL_ID=${parcelSpId}`;
        url =
          window.webApiUrl +
          `GetGovernmentLandsDataNew?munCode=${
            this.state.formValues["MUNICIPALITY_NAME"]
              ? this.state.formValues["MUNICIPALITY_NAME"]
              : ""
          }&planNo=${
            this.state.formValues["PLAN_NO"]
              ? this.state.formValues["PLAN_NO"]
              : ""
          }&baladiadocno=${
            this.state.formValues["baladia_doc_no"]
              ? this.state.formValues["baladia_doc_no"]
              : ""
          }&districtName=${districtValue ? districtValue : ""}&fromDate=${
            this.state.formValues["fromDate"]
              ? this.state.formValues["fromDate"]
              : ""
          }&toDate=${
            this.state.formValues["toDate"]
              ? this.state.formValues["toDate"]
              : ""
          }&spid=${parcelSpId ? parcelSpId : ""}&subdivision_type=${
            this.state.formValues["SUBDIVISION_TYPE"]
              ? this.state.formValues["SUBDIVISION_TYPE"]
              : ""
          }&subdivision_description=${
            this.state.formValues["SUBDIVISION_DESCRIPTION"]
              ? this.state.formValues["SUBDIVISION_DESCRIPTION"]
              : ""
          }&isBrief=false&countPerPage=50`;
      }
      let token = this.props.mainData.user?.token;

      let { data } = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      let features = data?.results || [];
      // features[0].parcel_part_no = undefined;     //for test
      if (features.length) {
        globalStore.searchCriteria = { ...this.state };
        //mun
        let munList = this.state.searchFields.find(
          (i) => i.field === "MUNICIPALITY_NAME"
        );
        let selectedMun = munList?.dataList?.find(
          (d) =>
            d.attributes["MUNICIPALITY_NAME_Code"] ===
            this.state.formValues["MUNICIPALITY_NAME"]
        );
        let munData = selectedMun?.attributes["MUNICIPALITY_NAME"];
        //planNo
        // let planData = this.state.formValues["PLAN_NO"];
        let mappingRes = features.map((f) => {
          return {
            layerName: this.state.searchLayer,
            id: f["spatial_id"] || f["parcel_part_no"],
            ...f,
            MUNICIPALITY_NAME: munData,
            MUNICIPALITY_NAME_Code:
              selectedMun?.attributes["MUNICIPALITY_NAME_Code"],
            // PLAN_NO: planData,
            geometry: null,
            where: whereClause,
          };
        });
        // let hasSpatialIds = mappingRes?.filter(i=>i.id)?.map(i=>i.id)||[];

        this.props.setOuterSearchResult({
          count: data.count,
          totalPages: data.totalPages,
          currentPage: 1,
          fetchURL: url,
          layerName: this.state.searchLayer,
          whereClause: whereClause,
          list: mappingRes,
          MUNICIPALITY_NAME: munData,
          MUNICIPALITY_NAME_Code:
            selectedMun?.attributes["MUNICIPALITY_NAME_Code"],

          // queryObj: paginationObj,
          statisticsInfo: {
            COUNT: data.count,
          },
        });

        if (features.length > 1) {
          this.props.generalOpenResultMenu();
        } else {
          if (
            ["PARCEL_PRIVACY", "LGR_ROYAL", "SALES_LANDS"].includes(
              this.state.searchLayer
            )
          )
            this.props.outerOpenResultdetails(mappingRes[0]);
        }
      } else {
        showLoading(false);
        this.setState({ noData: true });
      }
    } catch (err) {
      showLoading(false);
      if (err?.response?.status === 401) {
        //logut
        message.error(this.props.t("common:sessionFinished"));
        localStorage.removeItem("user");
        localStorage.removeItem("token");
        window.open(window.hostURL + "/home/<USER>", "_self");
      } else message.error(this.props.t("common:retrievError"));
    }
  }
  getListsByFieldNameForPrivacyParcels(fieldName, layer) {
    let { mainData } = this.props;
    let layersSetting = mainData.layers;

    let token = mainData.user?.token;
    // fetch other field data like districs, plan number subdivision ...etc
    //get all filters
    let promiseQueries = [];
    let fieldsName = [];
    let searchField = layersSetting[layer]?.searchFields?.find(
      (x) => x.field === fieldName
    );
    if (fieldName === "SUBDIVISION_TYPE") {
      let subDiscriptionField = layersSetting[layer]?.searchFields?.find(
        (x) => x.field === "SUBDIVISION_DESCRIPTION"
      );
      if (this.state.formValues["SUBDIVISION_TYPE"]) {
        let dataListsFromSubTypeValue = searchField.dataList.find(
          (i) =>
            i.attributes.SUBDIVISION_TYPE ===
            this.state.formValues["SUBDIVISION_TYPE"]
        );
        subDiscriptionField.dataList = dataListsFromSubTypeValue.attributes
          .descriptionList?.length
          ? dataListsFromSubTypeValue.attributes.descriptionList.map((i) => {
              return {
                attributes: {
                  SUBDIVISION_DESCRIPTION: i,
                },
              };
            })
          : [];
      } else subDiscriptionField.dataList = [];
    }
    let iterateFields = searchField?.reflectedFields || [];

    layersSetting[layer]?.searchFields
      ?.filter((x) => iterateFields.includes(x.field))
      .forEach((item, index) => {
        console.log({ item });
        fieldsName.push(item.field);

        promiseQueries.push(
          new Promise(async (resolve, reject) => {
            let url = window.webApiUrl + item.fetchURL;
            let munCode = this.state.formValues["MUNICIPALITY_NAME"];
            let planNo = this.state.formValues["PLAN_NO"];
            let docNumber = this.state.formValues["baladia_doc_no"];
            let subDivisionType = this.state.formValues["SUBDIVISION_TYPE"];
            let subDivisionDes =
              this.state.formValues["SUBDIVISION_DESCRIPTION"];
            let districtCode = this.state.formValues["DISTRICT_NAME"];
            let districtValue;
            let districtList =
              this.state.searchFields.find(
                (i) => i.field === "DISTRICT_NAME"
              ) || [];
            if (districtCode)
              districtValue = districtList?.dataList?.find(
                (i) => i.attributes["DISTRICT_NAME_Code"] === districtCode
              )?.attributes?.DISTRICT_NAME;
            if (item.field !== "PARCEL_PLAN_NO") {
              url = getFetchUrlForPrivacyLandLists(
                {
                  munCode,
                  planNo,
                },
                item.field,
                window.webApiUrl + item.fetchURL
              );
            } else {
              if (munCode) url += `&munCode=${munCode}`;
              if (planNo) url += `&planNo=${planNo}`;
              if (docNumber) url += `&baladiadocno=${docNumber}`;
              if (subDivisionType)
                url += `&subdivision_type=${subDivisionType}`;
              if (subDivisionDes)
                url += `&subdivision_description=${subDivisionDes}`;
              if (districtValue) url += `&districtName=${districtValue}`;
              url += "&countPerPage=50";
            }
            try {
              let { data } = await axios.get(url, {
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              });
              resolve({
                data,
                field: item.field,
              });
            } catch (err) {
              reject(err);
            }
          })
        );
      });

    if (promiseQueries.length > 0) showLoading(true);
    else {
      this.setState(
        {
          searchFields: layersSetting[layer]?.searchFields?.filter(
            (x) => !x.isSearch
          ),
        },
        () => {
          showLoading(false);
        }
      );
    }

    if (!this.state.formValues["MUNICIPALITY_NAME"]) {
      promiseQueries = promiseQueries[0] ? [promiseQueries[0]] : [];
      fieldsName = fieldsName[0] ? [fieldsName[0]] : [];
    }
    if (promiseQueries.length)
      Promise.all(promiseQueries)
        .then((resultsData) => {
          // showLoading(true);
          resultsData.forEach((item, index) => {
            // todo: handle subdivison
            let itemList =
              item.field === "PARCEL_PLAN_NO"
                ? item.data.results
                : item.data.filter((i) => i);
            var searchField = layersSetting[layer]?.searchFields?.find(
              (x) => x.field === fieldsName[index]
            );
            if (itemList?.length > 0) {
              if (item.field !== "PARCEL_PLAN_NO")
                itemList = itemList.map((listItem) => {
                  if (item.field === "SUBDIVISION_TYPE")
                    return {
                      attributes: {
                        [item.field]: listItem.subdivision_type,
                        descriptionList: listItem.subdivision_descriptions,
                      },
                    };
                  else if (item.field === "DISTRICT_NAME") {
                    return {
                      attributes: {
                        [item.field]: listItem.name,
                        [item.field + "_Code"]: listItem.code,
                      },
                    };
                  } else {
                    return {
                      attributes: {
                        [item.field]: listItem,
                      },
                    };
                  }
                });
              else
                itemList = itemList.reduce((out, cur) => {
                  if (!out?.length)
                    out = [
                      {
                        attributes: {
                          [item.field]: cur.part_no,
                          PARCEL_SPATIAL_ID: cur.parcel_part_no,
                        },
                      },
                    ];
                  else {
                    let isItemExistBefore = out.find(
                      (i) =>
                        i.attributes[item.field] === cur.part_no &&
                        i.attributes?.PARCEL_SPATIAL_ID &&
                        i.attributes?.PARCEL_SPATIAL_ID === cur.parcel_part_no
                    );
                    if (!isItemExistBefore)
                      out.push({
                        attributes: {
                          [item.field]: cur.part_no,
                          PARCEL_SPATIAL_ID: cur.parcel_part_no,
                        },
                      });
                  }
                  return out;
                }, []);
              if (item.field === "PARCEL_PLAN_NO") {
                searchField.count = item.data.count;
                searchField.totalPages = item.data.totalPages;
                searchField.next = item.data.next;
              }
              searchField.dataList =
                fieldsName[index] === "MUNICIPALITY_NAME"
                  ? [
                      ...itemList
                        .filter(
                          (f) =>
                            typeof f.attributes["MUNICIPALITY_NAME"] ===
                              "string" && f.attributes["MUNICIPALITY_NAME"]
                        )
                        .sort((a, b) =>
                          a.attributes["MUNICIPALITY_NAME"].localeCompare(
                            b.attributes["MUNICIPALITY_NAME"],
                            "ar"
                          )
                        ),
                    ]
                  : [...itemList].filter((i) => {
                      if (searchField?.zoomLayer?.filterField) {
                        return i.attributes[searchField.zoomLayer.filterField];
                      } else {
                        if (i) return i;
                        else return undefined;
                      }
                    });
              if (fieldsName[index] === "SUBDIVISION_TYPE") {
                let subDivisionDescriptionField = layersSetting[
                  layer
                ]?.searchFields?.find(
                  (x) => x.field === "SUBDIVISION_DESCRIPTION"
                );

                let subDivisionTypesField = layersSetting[
                  layer
                ]?.searchFields?.find((x) => x.field === "SUBDIVISION_TYPE");
                let subDivTypeVal = this.state.formValues["SUBDIVISION_TYPE"];
                let subDivDescriptionList =
                  subDivisionTypesField.dataList.find(
                    (i) => i.attributes?.SUBDIVISION_TYPE === subDivTypeVal
                  )?.attributes?.descriptionList || [];

                subDivisionDescriptionField.dataList =
                  subDivDescriptionList?.length
                    ? [...subDivDescriptionList].map((i) => {
                        return {
                          attributes: {
                            SUBDIVISION_DESCRIPTION: i,
                          },
                        };
                      })
                    : [];
                if (subDivisionDescriptionField.dataList?.length)
                  subDivisionDescriptionField.dataList =
                    getDistinctValuesFromArray(
                      subDivisionDescriptionField.dataList
                    );
              }
            } else {
              searchField.dataList = [];
            }
          });
          this.setState(
            {
              searchFields: this.state.formValues["MUNICIPALITY_NAME"]
                ? layersSetting[layer]?.searchFields?.filter((x) => !x.isSearch)
                : layersSetting[layer]?.searchFields?.filter(
                    (x) =>
                      x.field === "MUNICIPALITY_NAME" ||
                      x.field === "PRIORITYZONENAME"
                  ),
              formValues: { ...this.state.formValues },
              loading: false,
            },
            () => {
              showLoading(false);
            }
          );
          // showLoading(false);
        })
        .catch((err) => {
          console.log({ err });
          this.setState({ loading: false });
          if (err?.response?.status === 401) {
            //logut
            message.error(this.props.t("common:sessionFinished"));
            localStorage.removeItem("user");
            localStorage.removeItem("token");
            window.open(window.hostURL + "/home/<USER>", "_self");
          } else message.error(this.props.t("common:retrievError"));
        });
  }
  getListsByFieldNameForRoyalSalesParcels(fieldName, layer) {
    let { mainData } = this.props;
    let layersSetting = mainData.layers;

    let token = mainData.user?.token;
    // fetch other field data like districs, plan number subdivision ...etc
    //get all filters
    let promiseQueries = [];
    let fieldsName = [];
    let searchField = layersSetting[layer]?.searchFields?.find(
      (x) => x.field === fieldName
    );
    if (fieldName === "SUBDIVISION_TYPE") {
      let subDiscriptionField = layersSetting[layer]?.searchFields?.find(
        (x) => x.field === "SUBDIVISION_DESCRIPTION"
      );
      if (this.state.formValues["SUBDIVISION_TYPE"]) {
        let dataListsFromSubTypeValue = searchField.dataList.find(
          (i) =>
            i.attributes.SUBDIVISION_TYPE ===
            this.state.formValues["SUBDIVISION_TYPE"]
        );
        subDiscriptionField.dataList = dataListsFromSubTypeValue.attributes
          .descriptionList?.length
          ? dataListsFromSubTypeValue.attributes.descriptionList.map((i) => {
              return {
                attributes: {
                  SUBDIVISION_DESCRIPTION: i,
                },
              };
            })
          : [];
      } else subDiscriptionField.dataList = [];
    }
    let iterateFields = searchField?.reflectedFields || [];
    let munCode = this.state.formValues["MUNICIPALITY_NAME"];
    let munList =
      this.state.searchFields.find((i) => i.field === "MUNICIPALITY_NAME") ||
      [];
    if (munCode)
      munCode = munList?.dataList?.find(
        (i) => i.attributes["MUNICIPALITY_NAME_Code"] === munCode
      )?.attributes?.MUNICIPALITY_NAME_NonGISCode;
    let planNo = this.state.formValues["PLAN_NO"];
    let districtNo = this.state.formValues["districtNo"];
    let categoryNo = this.state.formValues["categoryNo"];
    let centerNo = this.state.formValues["centerNo"];
    let blockNo = this.state.formValues["blockNo"];
    let subDivisionType = this.state.formValues["SUBDIVISION_TYPE"];
    let subDivisionDes = this.state.formValues["SUBDIVISION_DESCRIPTION"];
    layersSetting[layer]?.searchFields
      ?.filter((x) => iterateFields.includes(x.field))
      .forEach((item, index) => {
        console.log({ item });
        fieldsName.push(item.field);
        if (
          ["districtNo", "categoryNo", "centerNo"].includes(item.field) &&
          !planNo
        )
          promiseQueries.push(
            new Promise((resolve) => {
              resolve({ data: [], field: item.field });
            })
          );
        else
          promiseQueries.push(
            new Promise(async (resolve, reject) => {
              let url = window.webApiUrl + item.fetchURL;
              // let parcelNum = this.state.formValues["PARCEL_PLAN_NO"];
              if (item.field !== "PARCEL_PLAN_NO")
                url = getFetchUrlForRoyalSalesLandLists(
                  {
                    munCode,
                    planNo,
                  },
                  item.field,
                  window.webApiUrl + item.fetchURL
                );
              else {
                if (munCode) url += `&cityId=${munCode}`;
                if (planNo) url += `&planNo=${planNo}`;
                if (districtNo) url += `&districtNo=${districtNo}`;
                if (centerNo) url += `&centerNo=${centerNo}`;
                if (categoryNo) url += `&categoryNo=${categoryNo}`;
                if (blockNo) url += `&blockNo=${blockNo}`;
                if (subDivisionType)
                  url += `&subdivision_type=${subDivisionType}`;
                if (subDivisionDes)
                  url += `&subdivision_description=${subDivisionDes}`;
                url += "&countPerPage=50";
              }
              try {
                let { data } = await axios.get(url, {
                  headers: {
                    Authorization: `Bearer ${token}`,
                  },
                });
                resolve({
                  data,
                  field: item.field,
                });
              } catch (err) {
                reject(err);
              }
            })
          );
      });

    if (promiseQueries.length > 0) showLoading(true);
    else {
      this.setState(
        {
          searchFields: layersSetting[layer]?.searchFields?.filter(
            (x) => !x.isSearch
          ),
        },
        () => {
          showLoading(false);
        }
      );
    }

    if (!this.state.formValues["MUNICIPALITY_NAME"]) {
      promiseQueries = promiseQueries[0] ? [promiseQueries[0]] : [];
      fieldsName = fieldsName[0] ? [fieldsName[0]] : [];
    }
    if (promiseQueries.length)
      Promise.all(promiseQueries)
        .then((resultsData) => {
          // showLoading(true);
          resultsData.forEach((item, index) => {
            console.log({ item }, index);
            // todo: handle subdivison
            let itemList =
              item.field === "PARCEL_PLAN_NO" ? item.data.results : item.data;
            var searchField = layersSetting[layer]?.searchFields?.find(
              (x) => x.field === fieldsName[index]
            );
            if (itemList?.length > 0) {
              if (item.field !== "PARCEL_PLAN_NO") {
                itemList = itemList
                  .map((listItem) => {
                    if (
                      ["districtNo", "centerNo", "categoryNo"].includes(
                        item.field
                      ) &&
                      this.state.searchLayer === "LGR_ROYAL"
                    )
                      return {
                        attributes: {
                          [item.field]: listItem.name,
                          [item.field + "_Code"]: listItem.code,
                        },
                      };
                    else if (item.field === "SUBDIVISION_TYPE")
                      return {
                        attributes: {
                          [item.field]: listItem.subdivision_type,
                          descriptionList: listItem.subdivision_descriptions,
                        },
                      };
                    else {
                      return {
                        attributes: {
                          [item.field]: listItem,
                        },
                      };
                    }
                  })
                  ?.filter((listItem) => {
                    if (
                      ["districtNo", "centerNo", "categoryNo"].includes(
                        item.field
                      ) &&
                      this.state.searchLayer === "LGR_ROYAL"
                    )
                      return listItem?.attributes[item.field + "_Code"] !== "0";
                    else return listItem?.attributes[item.field] !== "0";
                  });
              } else {
                itemList = itemList.reduce((out, cur) => {
                  if (!out?.length)
                    out = [
                      {
                        attributes: {
                          [item.field]: cur.land_no,
                        },
                      },
                    ];
                  else {
                    let isItemExistBefore = out.find(
                      (i) => i.attributes[item.field] === cur.land_no
                    );
                    if (!isItemExistBefore)
                      out.push({
                        attributes: {
                          [item.field]: cur.land_no,
                        },
                      });
                  }
                  return out;
                }, []);
              }
              if (item.field === "PARCEL_PLAN_NO") {
                searchField.count = item.data.count;
                searchField.totalPages = item.data.totalPages;
                searchField.next = item.data.next;
              }
              searchField.dataList =
                fieldsName[index] === "MUNICIPALITY_NAME"
                  ? [
                      ...itemList
                        .filter(
                          (f) =>
                            typeof f.attributes["MUNICIPALITY_NAME"] ===
                              "string" &&
                            f.attributes["MUNICIPALITY_NAME"] &&
                            f.attributes["MUNICIPALITY_NAME_Code"] !== "0"
                        )
                        .sort((a, b) =>
                          a.attributes["MUNICIPALITY_NAME"].localeCompare(
                            b.attributes["MUNICIPALITY_NAME"],
                            "ar"
                          )
                        ),
                    ]
                  : [...itemList].filter((i) => {
                      if (searchField?.zoomLayer?.filterField) {
                        return i.attributes[searchField.zoomLayer.filterField];
                      } else {
                        if (i) return i;
                        else return undefined;
                      }
                    });
              if (fieldsName[index] === "SUBDIVISION_TYPE") {
                let subDivisionDescriptionField = layersSetting[
                  layer
                ]?.searchFields?.find(
                  (x) => x.field === "SUBDIVISION_DESCRIPTION"
                );

                let subDivisionTypesField = layersSetting[
                  layer
                ]?.searchFields?.find((x) => x.field === "SUBDIVISION_TYPE");
                let subDivTypeVal = this.state.formValues["SUBDIVISION_TYPE"];
                let subDivDescriptionList =
                  subDivisionTypesField.dataList.find(
                    (i) => i.attributes?.SUBDIVISION_TYPE === subDivTypeVal
                  )?.attributes?.descriptionList || [];

                subDivisionDescriptionField.dataList =
                  subDivDescriptionList?.length
                    ? [...subDivDescriptionList].map((i) => {
                        return {
                          attributes: {
                            SUBDIVISION_DESCRIPTION: i,
                          },
                        };
                      })
                    : [];
                if (subDivisionDescriptionField.dataList?.length)
                  subDivisionDescriptionField.dataList =
                    getDistinctValuesFromArray(
                      subDivisionDescriptionField.dataList
                    );
              }
            } else {
              searchField.dataList = [];
            }
          });
          this.setState(
            {
              searchFields: this.state.formValues["MUNICIPALITY_NAME"]
                ? layersSetting[layer]?.searchFields?.filter((x) => !x.isSearch)
                : layersSetting[layer]?.searchFields?.filter(
                    (x) =>
                      x.field === "MUNICIPALITY_NAME" ||
                      x.field === "PRIORITYZONENAME"
                  ),
              formValues: { ...this.state.formValues },
              loading: false,
            },
            () => {
              showLoading(false);
            }
          );
          // showLoading(false);
        })
        .catch((err) => {
          console.log({ err });
          this.setState({ loading: false });
          if (err?.response?.status === 401) {
            //logut
            message.error(this.props.t("common:sessionFinished"));
            localStorage.removeItem("user");
            localStorage.removeItem("token");
            window.open(window.hostURL + "/home/<USER>", "_self");
          } else message.error(this.props.t("common:retrievError"));
        });
  }
  getSearchData = (e) => {
    let selectedLayer =
      this.state.searchLayer === "OLD_Landbase_Parcel"
        ? "Landbase_Parcel"
        : this.state.searchLayer;

    if (
      ["PARCEL_PRIVACY", "LGR_ROYAL", "SALES_LANDS"].includes(
        this.state.searchLayer
      )
    ) {
      if (this.state.searchLayer === "PARCEL_PRIVACY") {
        let reqFieldsDone =
          this.state.formValues["MUNICIPALITY_NAME"] &&
          (this.state.formValues["PLAN_NO"] ||
            this.state.formValues["DISTRICT_NAME"] ||
            this.state.formValues["SUBDIVISION_TYPE"] ||
            this.state.formValues["PARCEL_PLAN_NO"] ||
            this.state.formValues["baladia_doc_no"] ||
            (this.state.formValues["fromDate"] &&
              this.state.formValues["toDate"]));
        if (!reqFieldsDone) {
          //todo: show warning
          message.open({
            type: "warning",
            content: this.props.t("common:enterAnotherField"),
          });
          return;
        } else this.getRoyalPrivateSalesParcelData("privateParcels");
      } else if (
        ["LGR_ROYAL", "SALES_LANDS"].includes(this.state.searchLayer)
      ) {
        let munValue;
        let munCode = this.state.formValues["MUNICIPALITY_NAME"];
        let munList =
          this.state.searchFields.find(
            (i) => i.field === "MUNICIPALITY_NAME"
          ) || [];
        if (munCode)
          munValue = munList?.dataList?.find(
            (i) => i.attributes["MUNICIPALITY_NAME_Code"] === munCode
          )?.attributes?.MUNICIPALITY_NAME_NonGISCode;
        let planNoValue = this.state.formValues["PLAN_NO"];

        let parcelNoValue = this.state.formValues["PARCEL_PLAN_NO"];
        if (!munValue) {
          //todo: show warning
          message.open({
            type: "warning",
            content: this.props.t("common:enterMun"),
          });
          return;
        } else if (munValue && !planNoValue && !parcelNoValue) {
          //todo: show warning
          message.open({
            type: "warning",
            content: this.props.t("common:enterPlanNoOrParcelNo"),
          });
          return;
        } else
          this.getRoyalPrivateSalesParcelData(
            this.state.searchLayer === "SALES_LANDS" ? "sales" : "royal"
          );
      }
    } else {
      let layersSetting = this.props.mainData.layers;
      let isOldLandsBlock = this.state.searchLayer === "OLD_Landbase_Parcel";

      let filterQuery = [];

      Object.keys(this.state.formValues).forEach((key) => {
        if (
          this.state.formValues[key] === "الكل" &&
          key === "PRIORITYZONENAME"
        ) {
          filterQuery.push(key + " IS NOT NULL");
        } else {
          if (this.state.formValues[key]) {
            let field = layersSetting[this.state.searchLayer].searchFields.find(
              (x) => x.field == key
            );

            if (field.isSearch) {
              filterQuery.push(
                key + " like '%" + this.state.formValues[key] + "%'"
              );
            } else if (field.isDate) {
              filterQuery.push(
                key +
                  field.operator +
                  convertHirjiDateToTimeSpan(
                    this.state.formValues[key],
                    field.isEndDate
                  )
              );
            } else {
              filterQuery.push(key + "='" + this.state.formValues[key] + "'");
            }
          }
        }
      });

      if (isOldLandsBlock)
        filterQuery =
          filterQuery.join(" and ") +
          ` and PLAN_NO IN ('${randomPlanNumbers.join("','")}')`;
      else filterQuery = filterQuery.join(" and ");
      let layerdId = getLayerId(this.props.map.__mapInfo, selectedLayer);

      debugger;
      let queryObj = {
        url: window.mapUrl + "/" + layerdId,
        where: filterQuery ? filterQuery : "1=1",
        outFields: layersSetting?.[this.state.searchLayer]?.outFields || ["*"],
        returnGeometry: true,
        queryWithGemoerty: this.state.isActiveBufferSearch,
        //distance: this.state.buffer_distance,
        geometry: e,
        layerdId: layerdId,
        layerName: this.state.searchLayer,
        //spatialRelationship: "overlaps"
      };

      let paginationObj = {
        ...queryObj,
        start: 0,
        num: window.paginationCount,
      };

      let promiseQueries = [];

      //for query
      promiseQueries.push(
        queryTask({ ...paginationObj, returnExecuteObject: true })
      );

      //for statistics
      promiseQueries.push(
        queryTask({
          ...queryObj,
          returnGeometry: false,
          statistics: layersSetting[this.state.searchLayer].statistics || [
            {
              type: "count",
              field: "OBJECTID",
              name: "count",
            },
          ],
          returnExecuteObject: true,
        })
      );

      // 0 for features
      // 1 for statistics

      showLoading(true);

      Promise.all(promiseQueries)
        .then((resultsData) => {
          let features = resultsData[0].features;
          if (features.length) {
            getFeatureDomainName(features, layerdId).then((res) => {
              showLoading(false);

              globalStore.searchCriteria = { ...this.state };

              let mappingRes = res.map((f) => {
                return {
                  layerName: this.state.searchLayer,
                  id: f.attributes["OBJECTID"],
                  ...f.attributes,
                  geometry: f.geometry,
                };
              });

              this.props.setOuterSearchResult({
                layerName: this.state.searchLayer,
                list: mappingRes,
                queryObj: paginationObj,
                statisticsInfo: resultsData[1].features[0].attributes,
              });

              if (res.length > 1) {
                this.props.generalOpenResultMenu();
              } else {
                this.props.outerOpenResultdetails(mappingRes[0]);
              }
            });
          } else {
            showLoading(false);
            this.setState({ noData: true });
          }
        })
        .catch((err) => {
          showLoading(false);
          if (err?.response?.status === 401) {
            //logut
            message.error(this.props.t("common:sessionFinished"));
            localStorage.removeItem("user");
            localStorage.removeItem("token");
            window.open(window.hostURL + "/home/<USER>", "_self");
          } else message.error(this.props.t("common:retrievError"));
        });
    }
  };

  onChange = (e) => {
    this.setState({ isActiveBufferSearch: !this.state.isActiveBufferSearch });
  };
  changeDate = (name) => (e) => {
    this.setState({ formValues: { ...this.state.formValues, [name]: e } });
  };

  onSearch = async (item, filterValue) => {
    let { mainData } = this.props;
    // if(filterValue?.length<1) return;
    let layersSetting = mainData.layers;
    let isPrivateRoyalSalesLayer = [
      "PARCEL_PRIVACY",
      "LGR_ROYAL",
      "SALES_LANDS",
    ].includes(this.state.searchLayer);
    let isPrivateLandLayer = this.state.searchLayer === "PARCEL_PRIVACY";
    let isSalesLandsLayer = this.state.searchLayer === "SALES_LANDS";
    let isRoyalLandLayer = this.state.searchLayer === "LGR_ROYAL";
    let isOldLandsBlock = this.state.searchLayer === "OLD_Landbase_Parcel";
    if (item.isServerSideSearch) {
      if (this.searchTimeOut) clearTimeout(this.searchTimeOut);

      this.searchTimeOut = setTimeout(async () => {
        this.setState({ fetching: true });

        let filterQuery = [];

        Object.keys(this.state.formValues).forEach((key) => {
          if (
            this.state.formValues[key] &&
            key !== item.field &&
            key !== item.isServerSideSearch
          ) {
            if (isPrivateLandLayer) {
              let sField = layersSetting[
                this.state.searchLayer
              ].searchFields.find((x) => x.field === key);
              filterQuery.push(
                sField.apiParamFieldName +
                  "='" +
                  this.state.formValues[key] +
                  "'"
              );
            } else if (isRoyalLandLayer || isSalesLandsLayer) {
              let munCode = this.state.formValues["MUNICIPALITY_NAME"];
              let munCodeNonGIS;
              let munList =
                this.state.searchFields.find(
                  (i) => i.field === "MUNICIPALITY_NAME"
                ) || [];
              if (munCode)
                munCodeNonGIS = munList?.dataList?.find(
                  (i) => i.attributes["MUNICIPALITY_NAME_Code"] === munCode
                )?.attributes?.MUNICIPALITY_NAME_NonGISCode;
              let sField = layersSetting[
                this.state.searchLayer
              ].searchFields.find((x) => x.field === key);
              let val =
                key === "MUNICIPALITY_NAME"
                  ? munCodeNonGIS || ""
                  : this.state.formValues[key];
              filterQuery.push(sField.apiParamFieldName + "='" + val + "'");
            } else
              filterQuery.push(key + "='" + this.state.formValues[key] + "'");
          }
        });

        if (filterValue && !isPrivateRoyalSalesLayer) {
          filterQuery.push(item.field + " like '%" + filterValue + "%'");
        } else if (filterValue) {
          if (isPrivateRoyalSalesLayer) {
            filterQuery.push(item.apiParamFieldName + "=" + filterValue);
          } else filterQuery.push(item.field + "='" + filterValue + "'");
        }

        let layerdId = getLayerId(
          this.props.map.__mapInfo,
          isPrivateRoyalSalesLayer || isOldLandsBlock
            ? PARCEL_LANDS_LAYER_NAME
            : this.state.searchLayer
        );

        if (isOldLandsBlock)
          filterQuery =
            filterQuery.join(" and ") +
            ` and PLAN_NO IN ('${randomPlanNumbers.join("','")}')`;
        else if (isPrivateRoyalSalesLayer && item.field === "PARCEL_PLAN_NO") {
          //PARCEL_MAIN_LUSE = 30 ---> خدمات عامة
          // filterQuery = filterQuery.join(" and ") + ` and PARCEL_MAIN_LUSE=30`;
          filterQuery = filterQuery.join("&");
        } else filterQuery = filterQuery.join(" and ");
        if (!isPrivateRoyalSalesLayer)
          queryTask({
            url: window.mapUrl + "/" + layerdId,
            where: filterQuery,
            outFields: isPrivateRoyalSalesLayer
              ? [item.field, item.zoomLayer?.filterField || ""]
              : [item.field],
            orderByFields: [item.field + " ASC"],
            returnDistinctValues: true,
            returnGeometry: false,
            callbackResult: ({ features }) => {
              let searchField = layersSetting[
                this.state.searchLayer
              ].searchFields.find((x) => x.field == item.field);

              if (features.length > 0) searchField.dataList = [...features];

              this.setState(
                {
                  searchFields: [
                    ...layersSetting[
                      this.state.searchLayer
                    ].searchFields.filter((x) => !x.isSearch),
                  ],
                  // formValues: { ...this.state.formValues },
                  fetching: false,
                },
                () => {
                  showLoading(false);
                }
              );
            },
          });
        else {
          let token = mainData.user?.token;
          try {
            let { data } = await axios.get(
              window.webApiUrl +
                item.fetchURL +
                "&countPerPage=100&" +
                filterQuery?.replaceAll("'", ""),
              {
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              }
            );
            let searchField = layersSetting[
              this.state.searchLayer
            ].searchFields.find((x) => x.field == item.field);

            let itemList = data?.results || [];
            if (itemList.length > 0) {
              if (isPrivateLandLayer)
                itemList = itemList.map((listItem) => {
                  return {
                    attributes: {
                      [item.field]: listItem.part_no,
                      PARCEL_SPATIAL_ID: listItem.parcel_part_no,
                    },
                  };
                });
              else
                itemList = itemList.reduce((out, cur) => {
                  if (!out?.length)
                    out = [
                      {
                        attributes: {
                          [item.field]: cur.land_no,
                        },
                      },
                    ];
                  else {
                    let isItemExistBefore = out.find(
                      (i) => i.attributes[item.field] === cur.land_no
                    );
                    if (!isItemExistBefore)
                      out.push({
                        attributes: {
                          [item.field]: cur.land_no,
                        },
                      });
                  }
                  return out;
                }, []);
              if (item.field === "PARCEL_PLAN_NO") {
                searchField.count = data.count;
                searchField.totalPages = data.totalPages;
                searchField.next = data.next;
              }
              searchField.dataList = [...itemList].filter((i) => {
                if (searchField?.zoomLayer?.filterField) {
                  return i.attributes[searchField.zoomLayer.filterField];
                } else {
                  if (i) return i;
                  else return undefined;
                }
              });
            } else {
              searchField.dataList = [];
            }

            this.setState(
              {
                searchFields: [
                  ...layersSetting[this.state.searchLayer].searchFields.filter(
                    (x) => !x.isSearch
                  ),
                ],
                // formValues: { ...this.state.formValues },
                fetching: false,
              },
              () => {
                showLoading(false);
              }
            );
          } catch (err) {
            console.log({ err });
            this.setState({ loading: false });
            if (err?.response?.status === 401) {
              //logut
              message.error(this.props.t("common:sessionFinished"));
              localStorage.removeItem("user");
              localStorage.removeItem("token");
              window.open(window.hostURL + "/home/<USER>", "_self");
            } else message.error(this.props.t("common:retrievError"));
          }
        }
      }, 500);
    }
  };
  showAlias(item, isNotConfigLayer) {
    const { t } = this.props;

    const langAr = this.props.languageState === "ar";
    if (item?.alias) {
      const isAliasIncludesArChar = (item?.alias).match(
        "[\u0600-\u06ff]|[\u0750-\u077f]|[\ufb50-\ufbc1]|[\ufbd3-\ufd3f]|[\ufd50-\ufd8f]|[\ufd92-\ufdc7]|[\ufe70-\ufefc]|[\uFDF0-\uFDFD]"
      );
      if (isNotConfigLayer) {
        if (langAr && isAliasIncludesArChar) {
          return item.alias;
        } else if (langAr && !isAliasIncludesArChar)
          return t(`layers:${item.alias}`);
        else return item.field;
      } else {
        if (langAr && isAliasIncludesArChar) {
          return item.alias;
        } else return t(`layers:${item.alias}`);
      }
    }
  }
  handleChangeDatePicker(date, dateString, name) {
    this.setState({
      showInfo: false,
      noData: false,
      formValues: { ...this.state.formValues, [name]: dateString },
    });
  }
  getLocaleObj() {
    let localeObj = { ...locale };
    localeObj.lang =
      i18n.language === "ar"
        ? {
            ...localeObj.lang,
            shortWeekDays: ["ح", "ن", "ث", "ع", "خ", "ج", "س"],

            shortMonths: [
              "يناير",
              "فبراير",
              "مارس",
              "أبريل",
              "مايو",
              "يونيو",
              "يوليو",
              "أغسطس",
              "سبتمبر",
              "أكتوبر",
              "نوفمبر",
              "ديسمبر",
            ],
          }
        : {
            ...localeObj.lang,
            shortWeekDays: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
            shortMonths: [
              "Jan",
              "Feb",
              "Mar",
              "Apr",
              "May",
              "Jun",
              "Jul",
              "Aug",
              "Sep",
              "Oct",
              "Nov",
              "Dec",
            ],
          };

    return localeObj;
  }

  getGISMunCode(name) {
    return municipilitiesForRoyal.find((i) => {
      if (name && name.includes(i.name)) return i;
      return i.name.includes(name);
    })?.GISCode;
  }

  getWhereClauseForRoyalSales(fieldName, value) {
    let where = "";
    if (fieldName === "distrcitNo") {
      where = `SUBDIVISION_TYPE=${subdivisionTypes.megawrah} AND SUBDIVISION_DESCRIPTION LIKE '%مجاور%' `;
      let numbers = value.match(/\d+/g);
      let megawrahObj = megawrahNumbersInString.find(
        (i) => numbers[0] === i.value
      );
      if (typeof megawrahObj.like === "string") {
        let megawarahWhere = ` AND (SUBDIVISION_DESCRIPTION LIKE '%${megawrahObj.like}%')`;
        where += megawarahWhere;
      } else {
        // like: [{OR:["أحد","حادي"]}, {AND:"عشر"}]

        if (megawrahObj.like?.OR && !megawrahObj.like?.AND) {
          where +=
            " AND " +
            megawrahObj.like?.OR.map(
              (word) => `(SUBDIVISION_DESCRIPTION LIKE '%${word}%')`
            ).join(" OR ");
        } else if (megawrahObj.like?.OR && megawrahObj.like?.AND) {
          where +=
            " AND " +
            megawrahObj.like?.OR.map(
              (word) =>
                `(SUBDIVISION_DESCRIPTION LIKE '%${word}%' AND SUBDIVISION_DESCRIPTION LIKE '%${megawrahObj.like.AND}%')`
            ).join(" OR ");
        }
      }
      if (megawrahObj.NOT) {
        where += ` AND ( SUBDIVISION_DESCRIPTION NOT LIKE '%${megawrahObj.NOT}%')`;
      }
    } else if (fieldName === "centerNo") {
      // todo:
      where = `SUBDIVISION_TYPE=${subdivisionTypes.megawrah} OR SUBDIVISION_TYPE=${subdivisionTypes.center} AND (SUBDIVISION_DESCRIPTION LIKE '%مجاور%' OR SUBDIVISION_DESCRIPTION LIKE '%فئة%' OR SUBDIVISION_DESCRIPTION LIKE '%فئه%')`;
    } else if (fieldName === "categoryNo") {
      // todo:
      let categoryAlphabet = value.match(/\(\s*(.)\s*\)/);
      // if(categoryAlphabet.length===2) categoryAlphabet = categoryAlphabet[1];
      // if(categoryAlphabet.length===1) categoryAlphabet = categoryAlphabet[0];
      let valueFromAlphabetCatregory = alphbetNumbers.find((al) => {
        if (typeof al.name === "string") {
          if (categoryAlphabet?.length === 1)
            return al.name === categoryAlphabet[0];
          else if (categoryAlphabet?.length > 1)
            return categoryAlphabet?.includes(al.name);
        } else {
          if (categoryAlphabet?.length === 1)
            return al.name.includes(categoryAlphabet[0]);
          else {
            let bool = false;
            categoryAlphabet.forEach((item) => {
              if (al.name.includes(item)) bool = true;
              //  return al.name.includes(categoryAlphabet)
            });
            return bool;
          }
        }
      });
      where = `(SUBDIVISION_TYPE=${subdivisionTypes.category} OR SUBDIVISION_TYPE=${subdivisionTypes.district}) AND (SUBDIVISION_DESCRIPTION LIKE '%${valueFromAlphabetCatregory.name}%' OR SUBDIVISION_DESCRIPTION LIKE '%${valueFromAlphabetCatregory.value}%' OR SUBDIVISION_DESCRIPTION LIKE '%فئة%' OR SUBDIVISION_DESCRIPTION LIKE '%فئه%') `;
    }

    return where;
  }

  render() {
    const { t } = this.props;
    const { filterText, open1 } = this.state;
    let layersSetting = this.props.mainData.layers;

    return (
      <div style={{ textAlign: "right", display: "grid", gridGap: "10px" }}>
        {/* {this.state.loading && <Loader />} */}
        {/* <form class="form-floating"> */}
        <div style={{ display: "grid" }} className="select-cust">
          <label className="selectLabelStyle"> {t("common:searchLayer")}</label>
          {/**Layer */}
          <Select
            // className="form-select"
            //  id="floatingSelect"
            virtual={false}
            onDropdownVisibleChange={(flag) => {
              this.setState({ open1: flag });
            }}
            // suffixIcon={<DownCircleFilled style={{color:"#284587"}} />}
            suffixIcon={
              open1 ? (
                <img src={arrow_drop_up} alt="" />
              ) : (
                <img src={arrow_drop_down} alt="" />
              )
            }
            showSearch
            // optionLabelProp={t("common:searchLayer")}
            // labelRender={t("common:searchLayer")}
            // className="dont-show"
            onChange={this.handleLayerSelect()}
            value={this.state.searchLayer}
            placeholder={t("common:searchLayerSelect")}
            getPopupContainer={(trigger) => trigger.parentNode}
            optionFilterProp="v"
            filterOption={(input, option) =>
              option.v && option.v?.indexOf(input) >= 0
            }
          >
            {this.state.searchLayers?.map((s, index) => (
              <Select.Option
                key={index + "searchLay"}
                v={s.name}
                value={s.layerName}
                id={s.layerName}
              >
                {this.props?.languageState === "ar"
                  ? s?.layer?.arname || s?.layer?.name
                  : s?.layer?.englishName}
              </Select.Option>
            ))}
          </Select>
        </div>
        {this.state.searchFields.map((item, index) => {
          return (
            <div className="">
              <div style={{ display: "grid" }} key={index + "searchF"}>
                {item.isDate ? (
                  <>
                    <label className="selectLabelStyle">
                      {this.showAlias(
                        item,
                        this.state.searchLayer?.notInConfig
                      )}
                    </label>

                    <HijriDatePicker
                      disableOnClickOutside
                      placeholder={this.showAlias(
                        item,
                        layersSetting[this.state.searchLayer]?.notInConfig
                      )}
                      input={{
                        value: this.state.formValues[item.field],
                        onChange: this.changeDate(item.field),
                      }}
                    />
                  </>
                ) : (item.dataList &&
                    !["districtNo", "centerNo", "categoryNo"].includes(
                      item.field
                    )) ||
                  (["districtNo", "centerNo", "categoryNo"].includes(
                    item.field
                  ) &&
                    item?.dataList?.length) ? (
                  <>
                    <div className="select-cust">
                      <label className="selectLabelStyle">
                        {item?.required &&
                        this.state.formValues["MUNICIPALITY_NAME"] &&
                        !this.state.formValues["PLAN_NO"]
                          ? "* "
                          : ""}
                        {this.showAlias(
                          item,
                          layersSetting[this.state.searchLayer]?.notInConfig
                        )}
                      </label>

                      <Select
                        virtual={false}
                        onDropdownVisibleChange={(flag) => {
                          this.setState({ [`open[${index}]`]: flag });
                        }}
                        // disabled={item.dataList && item.dataList.length == 0}
                        suffixIcon={
                          this.state[`open[${index}]`] ? (
                            <img src={arrow_drop_up} alt="" />
                          ) : (
                            <img src={arrow_drop_down} alt="" />
                          )
                        }
                        showSearch
                        allowClear
                        notFoundContent={
                          this.state.fetching ? <Spin size="small" /> : null
                        }
                        onChange={this.selectChange(
                          ["PARCEL_PLAN_NO", "OWNERNAME"].includes(item.field)
                            ? item.zoomLayer?.filterField || item.field
                            : item.field,
                          item
                        )}
                        value={
                          this.state.formValues[
                            ["PARCEL_PLAN_NO", "OWNERNAME"].includes(item.field)
                              ? item.zoomLayer?.filterField || item.field
                              : item.field
                          ]
                        }
                        placeholder={this.showAlias(
                          item,
                          layersSetting[this.state.searchLayer]?.notInConfig
                        )}
                        onSearch={(e) => {
                          // let engNum = convertToEnglish(e);
                          // let arabNum = convertToArabic(engNum);
                          this.onSearch(item, e);
                          this.setState({
                            [["PARCEL_PLAN_NO", "OWNERNAME"].includes(
                              item.field
                            )
                              ? item.zoomLayer?.filterField || item.field
                              : item.field]: e,
                          });
                        }}
                        getPopupContainer={(trigger) => trigger.parentNode}
                        optionFilterProp="v"
                        filterOption={(input, option) => {
                          // console.log(convertToArabic(option.v));
                          // console.log(input,option);
                          if (option.v) {
                            return (
                              convertToEnglish(option.v)
                                .toLowerCase()
                                .indexOf(input.toLowerCase()) >= 0
                            );
                          } else {
                            return false;
                          }
                        }}
                      >
                        {item.dataList &&
                          item.dataList
                            .filter((e, i) => {
                              if (this.state[item.field]) {
                                if (
                                  e.attributes[item.field] &&
                                  typeof e.attributes[item.field] === "string"
                                )
                                  return (
                                    e.attributes[item.field] &&
                                    e.attributes[item.field]
                                      .toLowerCase()
                                      .indexOf(
                                        this.state[item.field].toLowerCase()
                                      ) >= 0
                                  );
                                else
                                  return (
                                    e.attributes[item.field] &&
                                    String(e.attributes[item.field])
                                      .toLowerCase()
                                      .indexOf(
                                        String(
                                          this.state[item.field]
                                        ).toLowerCase()
                                      ) >= 0
                                  );
                              } else {
                                return i < 100 && e.attributes[item.field];
                              }
                            })
                            .slice(0, 50)
                            .map((m, i) => {
                              //  let isPrivateOrRoyalLayer = ["PARCEL_PRIVACY", "LGR_ROYAL"].includes(this.state.searchLayer);

                              return (
                                <MenuItem
                                  key={item.field + i}
                                  v={m.attributes[item.field]}
                                  id={
                                    ["PARCEL_PLAN_NO", "OWNERNAME"].includes(
                                      item.field
                                    )
                                      ? m.attributes[
                                          item.zoomLayer?.filterField ||
                                            item.field
                                        ]
                                      : m.attributes[item.field + "_Code"] ||
                                        m.attributes[item.field]
                                  }
                                  value={
                                    ["PARCEL_PLAN_NO", "OWNERNAME"].includes(
                                      item.field
                                    )
                                      ? m.attributes[
                                          item.zoomLayer?.filterField ||
                                            item.field
                                        ]
                                      : m.attributes[item.field + "_Code"] ||
                                        m.attributes[item.field]
                                  }
                                >
                                  {convertToArabic(m.attributes[item.field])}
                                </MenuItem>
                              );
                            })}
                      </Select>
                    </div>
                  </>
                ) : null}
              </div>
            </div>
          );
        })}

        {/* </FormControl>
        </Box> */}

        {this.state.searchLayer && (
          <div>
            {filterText && !Array.isArray(filterText) ? (
              <div style={{ display: "grid" }}>
                <label className="selectLabelStyle">
                  {filterText?.required &&
                  this.state.formValues["MUNICIPALITY_NAME"] &&
                  !this.state.formValues["PLAN_NO"]
                    ? "* "
                    : ""}{" "}
                  {this.showAlias(
                    filterText,
                    layersSetting[this.state.searchLayer]?.notInConfig
                  )}
                </label>

                <Input
                  name={filterText.field}
                  onChange={this.handleChangeInput}
                  value={this.state.formValues[filterText.field]}
                  placeholder={this.showAlias(
                    filterText,
                    layersSetting[this.state.searchLayer]?.notInConfig
                  )}
                />
              </div>
            ) : (
              filterText?.map((fText) => {
                return (
                  <div key={fText?.field} style={{ display: "grid" }}>
                    <label className="selectLabelStyle">
                      {filterText?.required ? "* " : ""}{" "}
                      {this.showAlias(
                        fText,
                        layersSetting[this.state.searchLayer]?.notInConfig
                      )}
                    </label>

                    {!fText.field?.includes("Date") ? (
                      <Input
                        name={fText.field}
                        onChange={this.handleChangeInput}
                        value={this.state.formValues[fText.field]}
                        placeholder={this.showAlias(
                          fText,
                          layersSetting[this.state.searchLayer]?.notInConfig
                        )}
                      />
                    ) : (
                      <DatePicker
                        onChange={(date, dateString) =>
                          this.handleChangeDatePicker(
                            date,
                            dateString,
                            fText.field
                          )
                        }
                        // value={this.state.formValues[fText.field]}
                        allowClear
                        name={fText.field}
                        placeholder={this.showAlias(
                          fText,
                          layersSetting[this.state.searchLayer]?.notInConfig
                        )}
                        format={"DD-MM-YYYY"}
                        locale={this.getLocaleObj()}
                      />
                    )}
                  </div>
                );
              })
            )}

            <div style={{ display: "grid" }} className="checkbox_cust">
              {!["PARCEL_PRIVACY", "LGR_ROYAL", "SALES_LANDS"].includes(
                this?.state?.searchLayer
              ) && (
                <>
                  <Checkbox
                    style={{
                      margin: "20px 0",
                      textAlign: "right",
                      justifyContent: "end",

                      display: "flex",
                      alignItems: "center",
                    }}
                    checked={this.state.isActiveBufferSearch}
                    onChange={this.onChange}
                  >
                    <span style={{ color: "#284587" }}>
                      {" "}
                      {t("common:geoSearch")}
                    </span>
                  </Checkbox>

                  {this.state.isActiveBufferSearch && (
                    <div style={{ display: "grid" }} className="select-cust">
                      <label className="selectLabelStyle">
                        {t("common:distance")}
                      </label>

                      <Input
                        name="buffer_distance"
                        onChange={this.handleBufferSearch}
                        value={this.state.buffer_distance}
                        placeholder={t("common:distance2")}
                      />
                    </div>
                  )}
                </>
              )}

              <div className="searchInfoStyle">
                {this.state.showInfo && <p>{t("common:clickMapToSearch")}</p>}
                {this.state.noData && <p>{t("common:noDataAvail")}</p>}
              </div>
              <div style={{ textAlign: "center" }}>
                <button
                  onClick={this.searchForData}
                  className="SearchBtn mx-auto w-16"
                  size="large"
                  htmlType="submit"
                >
                  {t("common:search")}
                </button>
              </div>
              <div style={{ textAlign: "center" }}>
                <button
                  onClick={() => {
                    let searchObj = {
                      searchLayer: null,
                      //searchLayers: [],
                      formValues: {},
                      searchFields: [],
                      isActiveBufferSearch: false,
                      showInfo: false,
                      noData: false,
                      loading: false,
                      filterText: [],
                      open1: false,
                    };
                    this.setState({ ...searchObj }, () => {
                      globalStore.searchCriteria = { ...searchObj };
                      this.props.generalOpenSearchInputs();
                      this.props.setOuterSearchResult(null);
                      // this.props.map
                      //   .findLayerById("identifyGraphicLayer")
                      //   .removeAll();
                      // this.props.map.view.graphics.removeAll();
                      // this.props.map.view.goTo(window.__fullExtent);
                    });
                  }}
                  className="SearchBtn mx-auto w-16"
                  size="large"
                  htmlType="submit"
                >
                  {t("common:delete")}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }
}
export default withTranslation(["common", "layers"])(FilterComponent);
//  <Box sx={{ minWidth: 120 }}>
//     <FormControl fullWidth>
//       {this.state.loading && <Loader />}
//       <div style={{ display: "grid" }}>
//         <InputLabel className="selectLabelStyle"> {t("common:searchLayer")}</InputLabel>
//         {/**Layer */}
//         <Select
//           virtual={false}
//           suffixIcon={<DownCircleFilled />}
//           showSearch
//           // labelInValue={true}
//           // labelRender={t("common:searchLayer")}
//           className="dont-show"
//           onChange={this.handleLayerSelect()}
//           value={this.state.searchLayer}
//           placeholder={t("common:searchLayerSelect")}
//           getPopupContainer={(trigger) => trigger.parentNode}
//           optionFilterProp="v"
//           filterOption={(input, option) =>
//             option.v && option.v.indexOf(input) >= 0
//           }
//         >
//           {this.state?.searchLayers?.length>0&&this.state?.searchLayers?.map((s, index) => (
//             <MenuItem
//               key={index + "searchLay"}
//               v={s.name}
//               value={s.layerName}
//               id={s.layerName}
//             >
//               {this.props?.languageState === "ar"
//                 ? s?.layer?.arname || s?.layer?.name
//                 : s?.layer?.englishName}
//             </MenuItem>
//           ))}
//         </Select>
