import React, { useEffect, useRef, useState } from "react";
import Fade from "react-reveal/Fade";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes } from "@fortawesome/free-solid-svg-icons";
import TocComponent from "./TocComponent";

import ArcGISLegend from "@arcgis/core/widgets/Legend";
import closeIconFigma from "../../assets/icons/closeIconFigma.svg";

export default function LegendComponent(props) {
  const containerRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [diff, setDiff] = useState({ x: 0, y: 0 });
  const [position, setPosition] = useState({ x: 55, y: 10 });

  

  useEffect(() => {
    const legend = new ArcGISLegend({
      view: props.map.view,
      style: "classic",
      container: "legendContainer",
    });
  }, [props.map]);

  // Get header height once mounted
  const headerHeightRef = useRef(40);

  const handleMouseMove = (e) => {
    if (!isDragging || !containerRef.current) return;

    const newX = e.clientX - diff.x;
    const newY = e.clientY - diff.y;

    const box = containerRef.current.getBoundingClientRect();
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    const boxWidth = box?.width || 300;
    const boxHeight = box?.height;

    const clampedX = Math.max(0, Math.min(newX, windowWidth - boxWidth));
    const clampedY = Math.max(
      0,
      Math.min(newY, windowHeight - boxHeight)
    );

    setPosition({ x: clampedX, y: clampedY });
  };

  const handleMouseUp = () => setIsDragging(false);

  useEffect(() => {
    const header = containerRef.current?.querySelector(".Heading_tocComponent");
    if (header) {
      headerHeightRef.current = header.offsetHeight;
    }

  

    window.addEventListener("mousemove", handleMouseMove);
    window.addEventListener("mouseup", handleMouseUp);
    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isDragging, diff]);

  // useEffect(() => {
  //   window.handles.remove("map-events");
  // }, []);

  const onMouseDown = (e) => {
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    setDiff({
      x: e.clientX - rect.left + 2,
      y: e.clientY - rect.top + 50,
    });
    setIsDragging(true);
  };
  return (
    <Fade left collapse>
      <div
        className="layersMenu"
        style={{
          // borderRadius: "16px",
          position: "absolute",
          // bottom: 0,
          top: "0",
          // maxHeight: "300px",
        }}
      >
        <div
          ref={containerRef}
          style={{
            position: "absolute",
            left: position.x,
            top: position.y,
            zIndex: 9999,
            cursor: isDragging ? "grabbing" : "default",
            userSelect: "none",
            minWidth: "350px",
            background: "white",
            border: "1px solid #ccc",
            borderRadius: "16px",
            boxShadow:
              "0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%)",
            padding: "16px",
          }}
        >
          {/* <FontAwesomeIcon
            icon={faTimes}
            style={{
              marginTop: "5px",
              marginRight: "5px",
              cursor: "pointer",
              color: "rgba(40, 69, 135, 1)",
            }}
            onClick={() => {
              props.closeToolsData();
              props.Cleanup();
            }}
          />

          <div
            style={{
              fontSize: "16px",
              fontWeight: "400",
              color: "rgba(40, 69, 135, 1)",
              fontFamily: "Droid Arabic Kufi",
            }}
          >
            مفتاح الخريطة
          </div> */}
          <div onMouseDown={onMouseDown} className="Heading_tocComponent">
            <img
              onClick={() => {
                props.closeToolsData();
                props.Cleanup();
              }}
              src={closeIconFigma}
              alt=""
            />
            <label> مفتاح الخريطة</label>
          </div>
          <div id="legendContainer" className="legend"></div>
        </div>
      </div>
    </Fade>
  );
}
