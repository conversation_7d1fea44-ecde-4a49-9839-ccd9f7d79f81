/*general style*/
@import "./arcgis-main.css";

* {
  box-sizing: border-box !important;
  font-family: "Droid Arabic Kufi", sans-serif !important;
}

@font-face {
  font-family: "Droid Arabic Kufi";
  src: url("./assets/fonts/alfont_com_AlFont_com_DroidKufi-Regular.ttf")
    format("truetype");
  font-weight: normal;
  font-style: normal;
}

body {
  margin: 0;
  /* font-family: "Sans" !important; */
  font-family: "Droid Arabic Kufi", sans-serif !important;
}

html {
  font-family: "Droid Arabic Kufi", sans-serif !important;
}

/* @font-face {
  font-family: "NeoSansArabic";
  src: url(./ArbFONTS-NotoKufiArabic-Regular.ttf);
}

@font-face {
  font-family: arabicNums;
  src: url(./arabicNumber.ttf);
}

p {
  font-family: "NeoSansArabic";
} */

/* @media print {
  body {
    -webkit-print-color-adjust: exact;
  }
} */

/* span {
  font-family: arabicNums;
}

label {
  font-family: "NeoSansArabic";
} */

/* html {
  overflow: hidden;
} */

.ant-select-item-option {
  padding: 12px !important;
  font-size: 16px !important;
}

.ant-select-item-option:hover,
.ant-select-item-option-selected {
  background-color: #284587 !important;
  color: #fff !important;
}

.ant-form-rtl
  .ant-form-item.ant-form-item-has-success
  .ant-form-item-children-icon,
.ant-form-rtl
  .ant-form-item.ant-form-item-has-warning
  .ant-form-item-children-icon,
.ant-form-rtl
  .ant-form-item.ant-form-item-has-error
  .ant-form-item-children-icon,
.ant-form-rtl
  .ant-form-item.ant-form-item-is-validating
  .ant-form-item-children-icon {
  left: 20px !important;
  top: 18px !important;
}

.ant-form-rtl .ant-form-item > .ant-select .ant-select-arrow,
.ant-form-rtl .ant-form-item > .ant-select .ant-select-clear,
.ant-form-rtl
  .ant-form-item
  :not(.ant-input-group-addon)
  > .ant-select
  .ant-select-arrow,
.ant-form-rtl
  .ant-form-item
  :not(.ant-input-number-group-addon)
  > .ant-select
  .ant-select-arrow {
  left: 9px !important;
}

.ant-form-rtl
  .ant-form-item
  :not(.ant-input-group-addon)
  > .ant-select
  .ant-select-clear,
.ant-form-rtl
  .ant-form-item
  :not(.ant-input-number-group-addon)
  > .ant-select
  .ant-select-clear {
  left: 35px !important;
}

.ant-select-arrow,
.ant-select-clear svg {
  /* font-size: 17px !important; */
}

.ant-select-arrow svg {
  color: #223973 !important;
  font-size: 12px !important;
  font-weight: bold;
}

.ant-select-clear {
  left: 15px !important;
  display: none !important;
}

.ant-select-focused:not(.ant-select-disabled).ant-select:not(
    .ant-select-customize-input
  )
  .ant-select-selector,
.ant-select-selector:hover {
  border-color: #d9d9d9 !important;
  box-shadow: none !important;
}

.ant-input:focus,
.ant-input-focused {
  /* border-color: #d9d9d9 !important; */
  box-shadow: none !important;
}

.ant-select-selector .anticon-close-circle svg {
  font-size: 20px !important;
}

.ant-select-selector svg {
  font-size: 16px !important;
}

@keyframes fadeInLeft {
  0% {
    transform: translateX(500px);
  }

  100% {
    transform: translateX(0);
  }
}

button:focus {
  outline: none !important;
}

/* .ant-form-item-control {
  animation: fadeInLeft 0.7s ease-in alternate;
} */

/* td, */
/* th {
  
} */

::-webkit-scrollbar {
  width: 7px;
  height: 7px;
  -webkit-appearance: none;
}

::-webkit-scrollbar-thumb {
  background-color: #284587 !important;
  /* color of the scroll thumb */
  border-radius: 5px;
  /* roundness of the scroll thumb */
  border: 1px solid #284587;
  /* creates padding around scroll thumb */
}

.ant-checkbox-checked .ant-checkbox-inner,
.ant-checkbox-checked .ant-checkbox-inner:hover {
  background-color: #0a8eb9 !important;
  border: solid 2px #0a8eb9 !important;
}

.ant-checkbox:focus,
.ant-checkbox:hover {
  border-color: #0a8eb9 !important;
}

.checkDiv {
  font-size: 15px;
  font-weight: bold;
  text-align: right;
}

.ant-checkbox-inner {
  width: 20px !important;
  height: 20px !important;
}

.ant-checkbox-wrapper:hover .ant-checkbox-inner,
.ant-checkbox:hover .ant-checkbox-inner,
.ant-checkbox-input:focus + .ant-checkbox-inner,
.ant-checkbox-input + .ant-checkbox-inner {
  border-color: #0a8eb9 !important;
}

.ant-checkbox-checked .ant-checkbox-inner::after {
  border-color: #fff !important;
}

.ant-select-selector,
input {
  height: 30px !important;
  border-radius: 5px !important;
  border: solid 1px #d4d6de;
  background-color: #ffffff;
  /*  */
  font-size: 14px;
  line-height: 1.36;
  width: 100%;
  color: #364464 !important;
  margin: auto;
}

.ant-form-item-label > label::before {
  display: none !important;
}

.ant-form-item-explain-error {
  text-align: right;
  padding: 5px 0;
}

.ant-select {
  width: 100% !important;
  caret-color: transparent !important;
}

.ant-select-dropdown,
.ant-input,
.ant-select-selection-item,
.ant-select-selection-search-input {
  text-align: right !important;
  direction: rtl !important;
  /*  */
}

.ant-form-item-label {
  /*  */
  font-size: 16px;
  line-height: 1.63;
  text-align: right;
  font-weight: 600;
  color: #382f2d;
}

/*SideMenu Styles*/
.SideMenu {
  overflow-y: hidden !important;
  z-index: 999;
}

.SideMenu .MuiList-root {
  overflow-y: auto !important;
  overflow-x: hidden !important;
  line-height: 2.7;
}

.sideMenuFooter {
  font-weight: 400;
  font-size: 11px;
  line-height: 16px;
  text-align: center;
  color: #284587;
  background: #d9d9d9e5;
  text-wrap: initial;
  border-radius: 24px;
  padding: 8px 4px;
  margin-bottom: 0;
  direction: rtl;
}

.sideMenuTitle {
  text-align: right;
  /* position: absolute;
  right: 0 !important; */
  white-space: break-spaces;
  font-family: "NeoSansArabic";
  font-size: 14px;
  padding-right: 5px;
  padding-top: 10px;
  margin: auto;
}

.TitleEdition {
  text-align: right;
  font-family: "NeoSansArabic";
  font-size: 10px;
  padding-right: 5px;
  margin: auto;
}

.SideMenu .css-9mgopn-MuiDivider-root {
  border-color: #25b6bd;
}

.css-11o2879-MuiDrawer-docked .MuiDrawer-paper,
.css-uqx0qm-MuiDrawer-docked .MuiDrawer-paper {
  box-shadow: -6px 0px 4px 0 rgb(0 0 0 / 32%), -2px -2px 2px 0 rgb(0 0 0 / 20%);
}

.css-1u2mxbp {
  min-height: 60px !important;
}

.SideMenu .MuiListItem-button {
  text-align: right !important;
}

.SideMenu .MuiTypography-root {
  text-decoration: none !important;
  color: #707070 !important;
  font-family: "NeoSansArabic";
  text-decoration-line: none !important;
  font-weight: 100 !important;
  /* text-align: center; */
  font-size: 0.8rem;
}

.changeLanguageAREN {
  color: #707070;
  font-family: "NeoSansArabic";
}

.MuiListItem-root:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
  background-color: transparent !important;
}

.mainPage {
  height: 100vh;
  overflow: hidden;
}

.mainPage .css-k008qs {
  direction: rtl !important;
}

.mainPage .css-1191obr-MuiPaper-root-MuiAppBar-root {
  margin-left: unset !important;
  margin-right: 240px !important;
}

.languageIcon {
  position: absolute;
}

.fullScreenIcon svg,
.homeIcon svg,
.languageIcon {
  color: #117074 !important;
}

.sideLinkDiv {
  text-align: right;
  border-bottom: 1px solid #f0f0f0;
  /* padding-bottom: 10px;
  padding-top: 10px;
height: 90px; */
  vertical-align: middle;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  box-shadow: 0 0 1px transparent;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.sideLinkDiv .MuiButtonBase-root {
  height: 100% !important;
  /* display: block !important; */
  /* display: flex; */
  /* align-items: center; */
  /* justify-content: center; */
}

.sideLinkDiv img {
  /* filter: invert(39%) sepia(0%) saturate(30%) hue-rotate(136deg) brightness(108%) contrast(74%) !important; */
  filter: brightness(0) saturate(100%) invert(20%) sepia(99%) saturate(776%)
    hue-rotate(195deg) brightness(95%) contrast(93%);
  width: 22px;
}

/* .image img {
  filter: brightness(0) saturate(100%) invert(20%) sepia(99%) saturate(776%)
    hue-rotate(195deg) brightness(95%) contrast(93%) !important;
} */

.sideLinkDiv:hover img {
  /* filter: invert(30%) sepia(72%) saturate(7467%) hue-rotate(169deg) brightness(90%) contrast(97%) !important; */
  /* filter: unset !important; */
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7500%)
    hue-rotate(315deg) brightness(108%) contrast(101%) !important;
}

.sideLinkDiv:hover .marsadImg {
  filter: invert(100%) sepia(2%) saturate(11%) hue-rotate(105deg)
    brightness(105%) contrast(100%) !important;
}

.sideLinkDiv:hover .changeLanguageAREN {
  color: #fff;
}

@keyframes hoverAnimation {
  16.65% {
    -webkit-transform: translateY(8px);
    transform: translateY(8px);
  }

  33.3% {
    -webkit-transform: translateY(-6px);
    transform: translateY(-6px);
  }

  49.95% {
    -webkit-transform: translateY(4px);
    transform: translateY(4px);
  }

  66.6% {
    -webkit-transform: translateY(-2px);
    transform: translateY(-2px);
  }

  83.25% {
    -webkit-transform: translateY(1px);
    transform: translateY(1px);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

.sideLinkDiv {
  position: relative;
  z-index: 0;
  overflow: hidden;
  border: none;
  /* background-color: #FFFFFFD9 !important; */
}

.sideLinkDiv::before {
  content: "";
  position: absolute;
  top: 0;
  right: -100%;
  /* Start completely off-screen on the right */
  width: 100%;
  height: 100%;
  background: #284587;
  transition: right 0.5s ease;
  z-index: -1;
  /* border-radius: 10px 20px 20px 10px; */
  border-radius: 24px;
}

.sideLinkDiv:hover::before {
  right: 0;
  /* Slide from right to left and stop at the left */
}

.sideLinkDiv .MuiTypography-root {
  color: #284587 !important;
}

.sideLinkDiv:hover .MuiTypography-root {
  color: #fff !important;
}

/* 
.fullScreenIcon svg,
.homeIcon svg {
  font-size: 25px !important;
} */
.languageIcon button {
  font-size: 18px;
  height: 35px;
  width: 35px;
  font-weight: bold;
}

.fullScreenIcon button,
.homeIcon button,
.languageIcon button {
  padding: 5px !important;
  background: #fff !important;
  top: 10px !important;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  cursor: pointer;
}

.fullScreenIcon button:hover,
.homeIcon button:hover,
.languageIcon button:hover {
  color: #fff !important;
  background-color: #117074 !important;
}

.fullScreenIcon button:hover svg,
.homeIcon button:hover svg {
  color: #fff !important;
  background-color: #117074 !important;
}

.css-zxdg2z {
  padding: unset !important;
}

/*Map*/

#mapDiv {
  padding: 0;
  margin: 0;
  height: 100%;
  width: 100%;
}

#mapOverviewDiv {
  padding: 0;
  margin: 0;
  width: 100%;
}

.mapOverviewDrag {
  cursor: move;
  z-index: 10;
  border: 1px solid rgb(0, 0, 0);
  background-color: rgb(0, 0, 0);
  display: block;
  opacity: 0.5;
  left: 35%;
  top: 45%;
  position: absolute;
  width: 110px;
  height: 70px;
}

.mapOuterSearch {
  position: absolute;
  top: 10px;
}

.outerSearchIcon {
  color: #fff;
  background-color: #0a8eb9;
  padding: 8px;
  border-radius: 50px;
  width: 40px;
  text-align: center;
  top: 12px;
}

.outerSearchInput input {
  height: 25px !important;
}

.outerSearchInput {
  border-radius: 10px !important;
  margin-bottom: 5px !important;
}

.outerSearchInput .ant-input-affix-wrapper-focused {
  box-shadow: none !important;
  border-color: #d4d6de !important;
}

/* .outerSearchForm button {
  background-color: #0a8eb9 !important;
  border-radius: 10px !important;
  color: #fff;
  left: 0;
  height: 40px;
  margin-bottom: 10px;
}

.outerSearchForm button:hover {
  color: #0a8eb9;
  background-color: transparent !important;
  border: 1px solid #0a8eb9;
} */

.outerSearchInput .ant-input-affix-wrapper {
  border-radius: 10px !important;
  background: none !important;
}

.outerSearchForm {
  position: absolute;
  padding: 0px 12px 0px 0px !important;
  /* background: rgba(255, 255, 255, 0.5) !important; */
  /* box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%); */
  border-radius: 50px;
  /* width: 300px; */
  padding-top: 4px !important;
  padding-bottom: 4px !important;
  background: #ffffffd9;
  backdrop-filter: blur(8px);
}

.import_file {
  direction: rtl;
}

.layersMenuPage .box,
.interactiveMap .box,
.import_file .box {
  /* box-shadow: 0.5px 0.5px 3px #b45333; */
  /* border: 1px solid #fff; */
  border: 1px solid #28458740;
  border-radius: 16px;
  padding: 8px;
}

.topBar {
  position: absolute;
  top: 10px;
  z-index: 99999;
  /* background-color: rgb(51 51 51 / 60%) !important; */
  background-color: #ffffffc9;
  border-radius: 30px;
  padding: 10px;
}

.interactiveMap .box .images .image,
.topBar .images .image {
  border: 1px solid #284587;
  padding: 5px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36.6px;
  height: 36.6px;
  position: relative;
}

.interactiveMap .box .images .image img,
.topBar .images .image img {
  width: 25px;
  height: 25px;
}

/* .interactiveMap button:not(.SearchBtn) */
.layersMenuPage button {
  background-color: #fff !important;
  color: #b45333 !important;
  border-radius: 15px;
  font-size: 12px;
  border-color: #fff !important;
  box-shadow: none !important;
}

.layersMenuPage .card {
  border-radius: 10px;
  /* box-shadow: 0.5px 0.5px 1px #b45333; */
  border: 1px solid #fff;
  padding: 8px;
  text-wrap: wrap;
  text-align: center;
  cursor: pointer;
  font-size: 10px;
  background-color: transparent;
}

.layersMenuPage .card img {
  width: 35px;
  height: 35px;
  margin-inline: auto;
  display: block;
}

.layersMenuPage .card.active {
  background-color: #b45333;
  color: #fff;
  border-color: #b45333 !important;
}

.import_file .react-tabs__tab-list {
  display: flex;
  gap: 10px;
  margin-block: 10px;
  border-bottom: none !important;
}

.import_file .react-tabs__tab {
  text-align: center;
  border-radius: 50px;
  background-color: #fcfcfc99;
  flex: 1;
  /* color: #978772 !important; */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 7px;
  border: none !important;
}

/* .import_file .react-tabs__tab img {
  filter: brightness(0) saturate(100%) invert(66%) sepia(10%) saturate(875%) hue-rotate(353deg) brightness(96%) contrast(91%);
} */

.import_file .react-tabs__tab.react-tabs__tab--selected {
  background-color: #28458726 !important;
  color: #284587 !important;
}

/* .import_file .react-tabs__tab.react-tabs__tab--selected img {
  filter: brightness(0) saturate(100%) invert(37%) sepia(56%) saturate(659%) hue-rotate(329deg) brightness(95%) contrast(93%);
} */

.import_file .react-tabs__tab:focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.import_file .react-tabs__tab:focus:after {
  display: none !important;
}

.outerSearchAutoComplete {
  text-align: right;
  padding-right: 10px;
  padding-top: 10px;
  height: 300px;
  overflow: auto;
  /* background-color: #dbdee0 !important; */
  margin-left: 11px;
}

.outerSearchAutoComplete label {
  cursor: pointer;
}

.outerSearchAutoComplete div:hover {
  font-weight: bold;
  color: #117074;
}

.outerSearchFormOpen {
  width: 380px;
}

.outerSearchFormOpen .ant-input {
  width: 100% !important;
}

.searchResult_outerSearch {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
  background: #fff;
  padding: 10px;
  border-radius: 20px;
}

.searchResult_Button_container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0px 10px 0px 16px;
  margin-top: 15px;
}

.searchResult_Button {
  margin-top: 10px;
  margin-bottom: 6px;
  background-color: #284587;
  width: 100% !important;
  font-family: Droid Arabic Kufi;
  font-weight: 700;
  font-size: 16px;
  color: #ffffff;
  border-radius: 16px;
  margin: 0 !important;
  margin-bottom: 15px !important;
  height: 50px;
}

/* .leftIconMenu {
  position: absolute;
  top: 8px;
  left: 5px;
  right: unset;
  cursor: pointer;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  background-color: #fff;
  padding: 10px;
  width: 45px;
  border-radius: 50%;
  text-align: center;
  z-index: 99 !important;
} */
.leftIconMenu {
  position: absolute;
  top: 8px;
  left: 9px;
  right: unset;
  cursor: pointer;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  background-color: #ffffff73;
  /* padding: 10px; */
  width: 35px;
  height: 35px;
  line-height: 32px;
  border-radius: 50%;
  text-align: center;
  z-index: 99 !important;
}

.hashTest,
.sideLinkDiv .hashTestSideMenu {
  color: #284587 !important;
  font-size: 22px;
}

.CgArrowsExpandLeft {
  transform: rotate(90deg);
}

.openedservicesMenu li:hover .hashTest {
  color: #fff !important;
}

.serviceButton:hover .hashTest {
  color: #fff !important;
}

.sideLinkDiv:hover .hashTestSideMenu {
  color: #fff !important;
}

.hashTestSideMenu {
  color: #fff !important;
}

.leftIconMenu img {
  width: 25px;
  filter: invert(30%) sepia(72%) saturate(7467%) hue-rotate(169deg)
    brightness(90%) contrast(97%);
}

.mapTools {
  position: absolute;
  bottom: 2%;
  z-index: 1;
}

.mapTools ul {
  direction: ltr;
  padding-right: 15px !important;
}

.painting {
  direction: rtl;
}

.mapTools ul,
.painting ul {
  list-style-type: none !important;
}

.mapTools li {
  background: #fff !important;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  padding: 5px 10px 5px 10px;
  color: #0a8eb9 !important;
  cursor: pointer;
  margin-bottom: 15px;
  text-align: center;
  border-radius: 50px;
}

.mapTools li:hover {
  background: #0a8eb9 !important;
  color: #fff !important;
}

.merge {
  background-image: url("./mergeBG.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

/*SideMenu Sections*/

/*Coordinate*/
.coordinates .nav-item {
  padding: 5px 10px !important;
}

.coordinates h3,
.SideMenu h3 {
  font-weight: 700;
  font-size: 16px;
  text-align: right;
  color: #284587;
  background-color: #ffffffe5 !important;
  padding: 12px 15px;
  border-radius: 16px;
  width: 100%;
  margin: 10px 5px 18px 10px;
}

.HeadingSideMenu {
  direction: rtl;
  display: grid;
  grid-gap: 22px;
  padding: 10px;
  /* margin: 11px !important; */
}

.HeadingSideMenu img {
  margin-left: 5px;
  cursor: pointer;
  /* margin-bottom: 2px; */
}

/* .SideMenu h3 {
  position: relative;
  right: righ;
  right: 50%;
  top: 10px;
} */

.coordinates .nav-tabs .nav-link,
.coordinates .nav-tabs .nav-link:focus {
  border: none !important;
  background: transparent !important;
  outline: none !important;
}

.coordinates .nav-link.active {
  color: #25b6bd !important;
  font-weight: bold;
}

.coordinates .nav-link {
  font-family: "NeoSansArabic";
  font-size: 14px;

  letter-spacing: normal;
  color: #687781;
  margin-right: auto;
}

.coordinates .nav-tabs {
  border: 1px solid #eeeeee21 !important;
  background-color: rgba(231, 233, 241, 0.59);
  direction: rtl;
  flex-wrap: nowrap;
  padding: 0;
  border-radius: 20px;
  margin: 10px;
}

.backBtn h3 {
  margin: auto;
}

#SearchSidemenu .css-1u2mxbp {
  display: -webkit-box !important;
}

#h3SideSearch {
  /* margin: unset !important; */
  /* margin-left: auto !important;
  padding-right: 35px; */
}

.bookmarkDiv svg {
  color: #25b6bd;
}

.backBar {
  margin-left: auto;
}

.custom-image-wrapper {
  width: 100%;
  height: 400px;
  background: #f8f8f8;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.custom-gallery-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.backBar svg {
  transform: rotate(90deg);
  color: #25b6bd !important;
}

.backBtn svg {
  /* color: #fff !important; */
}

.coordinateForm h5,
.generalSearchCard h5 {
  font-family: "NeoSansArabic";
  font-size: 13px;
  line-height: 1.94;
  text-align: right;
  color: #284587;
}

.coordinateForm input {
  height: 40px;
  text-align: right;
  border-radius: 5px;
}

.coordinates {
  /* overflow-y: scroll !important; */
  height: 100%;
  overflow-x: hidden !important;
}

/*Measurement*/
.spaceMeasureBtn,
.distanceMeasureBtn,
.CoordinateMeasureBtn {
  border: none !important;
  box-shadow: none !important;
  padding: 5px;
}

.spaceMeasureBtn:hover,
.distanceMeasureBtn:hover,
.CoordinateMeasureBtn:hover {
  /* background-color: #d4d6de6e !important; */
  background-color: #284587 !important;
}

.MuiTooltip-tooltip {
  font-size: 13px !important;

  text-align: right !important;
}

#activeSpaceBtn,
#activeDistanceBtn,
#activeCooBtn {
  background-color: #338c9a !important;
  /* width: inherit; */
  color: #fff;
}

.measurePage h6 {
  font-family: "NeoSansArabic";
  font-size: 15px;
  line-height: 1.93;
  text-align: center;
  font-weight: bold;
  color: #25b6bd;
  padding-bottom: 3px;
  border-bottom: 1px solid #25b6bd;
  margin: auto;
  width: 50%;
}

.measurePage p {
  text-align: center;
  color: #25b6bd;
  font-family: "NeoSansArabic";
  white-space: normal;
  text-align: right;
  font-weight: bold;
}

.measurmentToolCopy p {
  padding: 0 !important;
}

.unitSpan {
  font-weight: bold;
}

.measurePage th {
  text-align: center;
  color: #25b6bd;
  font-weight: bold;
}

.measurePage td {
  text-align: center;
  color: #25b6bd;
}

.measurePage h5 {
  font-family: "NeoSansArabic";
  line-height: 1.93;
  font-size: 13px;
  text-align: right;
  font-weight: bold;
  color: #25b6bd;
  padding-top: 10px;
  border-top: 1px solid #25b6bd;
  white-space: break-spaces !important;
}

/*Painting*/
.painting ul {
  padding: unset !important;
}

.painting li {
  padding: 5px 10px 5px 10px;
  color: #33332e;
  cursor: pointer;
  margin-bottom: 10px;
  text-align: right;
  border-radius: 3px;
  font-family: "NeoSansArabic";
  width: 100%;
  padding: 10px !important;
  padding-right: 5px !important;
}

.painting li:hover {
  background-color: #284587;
  color: #fff !important;
  border-radius: 8px;
}

.painting li:hover .paintingText {
  color: #fff !important;
}

.painting li:hover .paintingIcons {
  color: #fff !important;
}

/* .painting svg {
  color: #25b6bd;
  font-size: 20px;
} */

.btnsGroup {
  padding: 5px;
  margin: auto;
}

.btnsGroup button,
.btnsGroup button:focus,
.btnsGroup button:active {
  border-radius: 4px;
  margin: 5px;
  box-shadow: none !important;
  background-color: #0b2548 !important;
  font-family: "NeoSansArabic";
  font-size: 16px;
  text-align: center;
  outline: none !important;
  border: none !important;
  color: #fffefb !important;
}

.btnsGroup button:hover {
  background-color: #fffefb !important;
  border: 1px solid #4e4f50 !important;
  color: #4e4f50 !important;
}

.ant-input::placeholder {
  color: #284587 !important;
}

.addMark {
  border-radius: 4px;
  margin: 0;
  box-shadow: none !important;
  background-color: #284587 !important;
  font-family: "NeoSansArabic";
  font-size: 16px;
  font-weight: 700;
  text-align: center;
  outline: none !important;
  border: none !important;
  color: #fff !important;
  width: 100%;
  border-radius: 16px !important;
  height: 56px !important;
}

/* .addMark:hover {
  background-color: #0a8eb9 !important;
  color: #fff !important;
} */

.bookmarkDiv {
  padding: 10px;
  margin: 10px 5px;
  background-color: #f5f5f5;
  cursor: pointer;
}

.bookmarkDiv p {
  font-family: "NeoSansArabic";
  font-size: 16px;
  text-align: center;
}

.bookmarkDiv svg {
  margin: 5px;
  cursor: pointer;
}

.starMark {
  font-size: 25px;
}

/*ImportFile*/
.importTableHidden,
.searchTableHidden {
  /* width: 100%; */
  bottom: 0 !important;
  z-index: 999 !important;
  overflow-x: auto;
  background-color: #fff;
  position: absolute;
  transition: height 1s;
  height: 60px !important;
  /* right: 50px; */
  left: 0px;
}

.dataTableHidden {
  /* width: 100%; */
  bottom: 0 !important;
  z-index: 999 !important;
  overflow-x: auto;
  /* background-color: #fff; */
  background-color: rgb(255 255 255 / 60%);
  backdrop-filter: blur(8px);
  position: absolute;
  transition: height 1s;
  height: 200px !important;
  /* right: 50px; */
  left: 0px;
}

.importTableShown {
  /* width: 100%; */
  width: 84%;
  bottom: 0;
  height: 60vh;
  background-color: #fff;
  position: absolute;
  z-index: 999 !important;
  transition: height 1s;
  color: #284587;
  margin: auto;
  border-radius: 20px;
  zoom: 0.9;
}

.searchTableShown table {
  border: none !important;
}

.searchTableShown {
  /* width: 100%; */
  width: 84%;
  bottom: 0;
  height: 60vh;
  background-color: #ffffff7a;
  position: absolute;
  z-index: 999 !important;
  transition: height 1s;
  color: #284587;
  margin: auto;
  border-radius: 20px;
  zoom: 0.9;
  background: rgb(255 255 255 / 60%);
  backdrop-filter: blur(8px);
}

.importTableHidden th,
.importTableShown th,
.searchTableHidden th,
.searchTableShown th {
  font-family: "NeoSansArabic";
  text-align: center;
  color: #000;
  cursor: pointer;
  font-weight: bold !important;
  vertical-align: middle !important;
}

.importTableHidden td,
.importTableShown td,
.searchTableHidden td,
.searchTableShown td {
  vertical-align: middle;
  text-align: center;
  font-family: "NeoSansArabic";
  font-size: 15px;
  text-align: right;
  /* padding: 10px !important; */
  color: #0b2548;
  width: 200px;
}

.searchTableShown td {
  /* padding: 0px !important; */
  text-align: center !important;
  border-left: 2px solid rgb(40, 69, 135);
  border-bottom: 0px !important;
  vertical-align: middle;
  /* padding: 0 !important; */
}

#searchMeta thead > tr {
  background: #0a8eb942;
  /* .dashDataTable tr:nth-child(odd) { */
  /* background-color: #f2f2f2; */
}

.esri-legend__layer-row {
  display: flex !important;
  flex-direction: row-reverse !important;
  gap: 10px !important;
}

.esri-legend__layer-cell--info {
  font-size: 16px;
  font-weight: 400;
  color: rgba(40, 69, 135, 1);
  font-family: "Droid Arabic Kufi";
}

.esri-legend__service-label {
  display: none;
}

/* .esri-legend__layer-caption{
        display: none;

} */

.tableArrow {
  z-index: 9999 !important;
  position: absolute;
  font-size: 20px;
  color: #fbfcfe;
  margin: auto;
  text-align: center;
  padding: 2px 14px;
  border-radius: 4px;
  cursor: pointer;
  /* background-color: #0a8eb9 !important; */
  transform: rotate(90deg);
  background: transparent;
}

.searchTableArrow {
  left: 40%;
  top: 0px;
}

.importTableArrow {
  left: 50%;
}

.fa-arrow-left.tableArrow {
  z-index: 9999 !important;
  position: absolute;
  font-size: 20px;
  color: #fbfcfe;
  margin: auto;
  text-align: center;
  padding: 2px 14px;
  border-radius: 4px;
  opacity: 0.8;
  background-color: #0b2548 !important;
  left: 45%;
}

@media (max-width: 576px) {
  .tableArrow {
    left: 20%;
  }

  .modal-content {
    width: 100%;
  }
}

.closeImportTable,
.closeSearchTable {
  display: none;
}

.importFileInput::-webkit-file-upload-button {
  visibility: hidden;
}

.importFileInput::before {
  content: "";
  /* -webkit-user-select: none; */
  cursor: pointer;
}

.tableHeaderIconsDiv {
  position: relative;
  /* box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%); */
  background-color: transparent !important;
  padding: 10px;
  text-align: right;
}

.tableHeaderIconsDiv svg {
  color: #284587;
  font-size: 19px;
}

.tableHeaderBtn {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  background-color: transparent !important;
}

.table-responsive {
  height: 100%;
}

.generalResultDetails .table-responsive {
  height: 100%;
  background: #fff;
  border: 1px solid #eee;
  border-radius: 21px;
  line-height: 3;
  overflow-x: hidden;
  margin: 5px;
  padding: 10px;
  font-size: 12px;
}

.resultsNumber {
  font-family: NeoSansArabic;
  color: #284587;
  font-size: 15px;
  text-align: right;
  /* padding: 5px 20px; */
  float: right;
}

/* #searchMeta svg {
  color: #0b2548;
} */

.openMetaHelp {
  height: 100%;
}

#searchMeta .ant-table-body {
  padding-bottom: 10px !important;
  overflow: auto scroll;
  max-height: 76% !important;
  height: 39vh !important;
  position: absolute !important;
  width: 100% !important;
}

#searchMeta .ant-spin-container,
#searchMeta .ant-table-container,
#searchMeta .ant-spin-nested-loading,
#searchMeta .ant-table-wrapper,
#searchMeta .ant-table {
  height: 100% !important;
}

.metaheaderBtn {
  padding: 5px;
  border-radius: 5px !important;
  /* margin-left: 10px; */
  border: none !important;
  background: transparent !important;
  padding: 0 !important;
}

.metaSide {
  margin-top: 5px;
  padding: 5px;
  border-radius: 7px;
  margin-left: 20px;
  max-height: 97%;
  height: 100%;
  position: absolute;
  margin: 10px;
  width: 97%;
}

.metaSideScroll {
  overflow-y: auto;
  height: 50vh;
  /* box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%); */
  padding-bottom: 60px;
}

.metaRow {
  height: 100% !important;
}

.metaSideDiv {
  text-align: right;
  font-family: "NeoSansArabic";
  padding: 10px;
  /* border-bottom: 1px solid #f0f0f0; */
  cursor: pointer;
  font-size: 16px;
}

.metaSideDiv:hover {
  background-color: #edf1f5;
}

.closedToolsMenu,
.closedservicesMenu {
  display: none;
}

.openedToolsMenu {
  display: block;
  position: absolute;
  top: 50px;
  left: 0%;
  direction: ltr;
}

.openedToolsMenu ul,
.openedservicesMenu ul {
  list-style-type: none;
  /* background-color: #fff; */
  /* box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%); */
  border-radius: 5px;
  padding: 2px;
}

.openedservicesMenu {
  top: 0%;
  left: 50px;
  display: block;
  position: absolute;
}

.openedservicesMenuImg {
  cursor: pointer;
  /* filter: invert(30%) sepia(72%) saturate(7467%) hue-rotate(169deg)
    brightness(90%) contrast(97%) !important; */
  /* width: 20px;
  height: 20px; */
  width: 100%;
  height: 100%;
}

.nearbyIcon:hover {
  filter: invert(30%) sepia(72%) saturate(7467%) hue-rotate(169deg)
    brightness(90%) contrast(97%) !important;
}

.pieChartClass .recharts-surface {
  height: 160px !important;
}

.recharts-default-tooltip {
  z-index: 10 !important;
}

.indicatorTitle {
  width: 100%;
  text-align: center;
  font-size: 15px;
  font-weight: bold;
  background: #ffffffe0;
}

.indicatorTable .table td {
  padding: 0.25rem;
  vertical-align: top;
  text-align: center;
  max-width: 200px;
  white-space: pre-wrap;
}

.openedservicesMenu li,
#openedToolsMenuLi {
  cursor: pointer;
  background-color: #ffffff73;
  margin: 7px 7px;
  width: 34px;
  height: 34px;
  line-height: 38px;
  /* padding: 8px; */
  text-align: center;
  backdrop-filter: blur(8px);
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center !important;
}

.openedservicesMenu li img {
  width: 100%;
  height: 100%;
}

.openedservicesMenu li {
  display: inline-block !important;
  /* margin: 7px 15px; */
  margin: 7px;
}

.activeService {
  /* background: #284587 !important; */
}

.activeMetaSide {
  background: #284587 !important;
  color: #fff;
  border-radius: 11px;
  margin: 5px;
}

.activeService img {
  filter: unset !important;
}

/* #openedToolsMenuLi {
  cursor: pointer;
  margin: 15px 7px;
  position: relative;
  background-color: #fff;
  width: 40px;
  height: 40px;
  padding: 5px;
  text-align: center;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  border-radius: 50px;
} */

.moreLessIcon {
  /* border-left: 2px solid #364464;
  line-height: 3; */
  padding-left: 10px;
}

.serviceSearch {
  padding: 10px !important;
  background: #fff !important;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  border-radius: 7px;
  position: absolute;
  top: 13%;
  width: 300px;
  left: 10%;
}

.toolsMenu {
  text-align: right;
  padding: 10px !important;
  background: #fff !important;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  border-radius: 7px;
  position: absolute;
  left: 110%;
  /* bottom: 130px; */
  z-index: 9999999 !important;
  top: 0%;
  /* max-height: 500px;
  overflow-x: hidden;
  overflow-y: scroll; */
}

.inquiryTool {
  /* width: 400px; */
  text-align: right;
  padding: 10px !important;
  background: #fff !important;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  border-radius: 7px;
  /* position: relative; */
  /* left: 20%; */
  z-index: 99999999 !important;
  top: 0%;
  /* background: #ffffffd9 !important; */
  /* backdrop-filter: blur(8px) !important; */

  border-radius: 16px !important;
  padding: 16px !important;
  /* max-height: 500px;
  overflow-x: hidden;
  overflow-y: scroll; */
}

.leftToolMenu {
  position: absolute !important;
  min-width: 400px;
  text-align: right;
  padding: 10px !important;
  background: #fff !important;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  border-radius: 7px;
  /* position: absolute !important;
  top: -50px;
  left: 120%; */
  overflow: hidden !important;
  z-index: 99999999 !important;
  /* max-height: 500px;
  overflow-x: hidden;
  overflow-y: scroll; */
}

.trafficMenu {
  top: 25% !important;
}

.selectLandMenu {
  top: 88% !important;
}

.selectLand_container {
  width: 350px;
  padding: 15px 0px 5px 0px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.selectLand_container p {
  margin: 0;
}

.selectLand_container span {
  display: block;
  /* margin-top: 7px; */
}

.SmallMapPopup {
  position: fixed !important;
  top: 0 !important;
  left: 120% !important;
}

.allToolsPage > div {
  height: 0 !important;
}

.layersMenu {
  /* width: 300px; */
}

.legend {
  width: 300px;
  max-height: 500px;
  margin-top: 10px;
}

.layersMenu svg {
  color: #fff;
}

.layersMenu li {
  display: block !important;
  background-color: #3336;
  border-radius: 5px;
  font-family: "NeoSansArabic";
}

.gmnoprint {
  display: none !important;
}

.css-78trlr-MuiButtonBase-root-MuiIconButton-root:focus,
.css-78trlr-MuiButtonBase-root-MuiIconButton-root:hover {
  outline: none !important;
  background-color: transparent !important;
}

/*Traffic*/
.traffic-container {
  padding: 10px;
}

.trafficMenu li {
  margin: 2px;
  /*  */
}

.traffic-ul {
  padding: 5px 0px !important;
  display: flex;
  align-items: center;
}

.trafficMenu {
  /* text-align: right; */
  /* padding: 0px !important; */
  /* background: #fff !important; */
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  /* border-radius: 7px; */
  /* position: relative; */
  /* left: 28%; */
  /* bottom: 65px !important; */
}

.trafficMenu .red {
  background-color: #e60000;
  width: 20px;
  height: 10px;
}

.trafficMenu .darkRed {
  background-color: #9e1313;
  width: 20px;
  height: 10px;
}

.trafficMenu .orange {
  background-color: #f07d02;
  width: 20px;
  height: 10px;
}

.trafficMenu .green {
  background-color: #84ca50;
  width: 20px;
  height: 10px;
}

/*GeneralSearch*/
.generalSearchResult {
  margin-bottom: 20px;
}

.generalSearchCard {
  padding: 10px;
  background: #fff;
  /* box-shadow: -2px -4px 4px 0 #0a8eb9, 0 0 2px 0 #0a8eb9; */
  /* box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%); */
  /* box-shadow: 0px 0px 20px 0px #ddd; */
  /* border-radius: 10px; */
  border-radius: 16px;
  margin-block: 10px;
  cursor: pointer;
  position: relative;
  color: #284587;
  overflow: hidden;
  font-weight: bold;

  /* display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 5px;
  border-radius: 16px; */
  background: rgba(255, 255, 255, 0.6);
}

.menuIcons {
  list-style-type: none !important;
}

.menuIcons li {
  background: #fff !important;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  padding: 5px 10px 5px 10px;
  color: #0a8eb9 !important;
  cursor: pointer;
  margin-bottom: 15px;
  text-align: center;
  border-radius: 50px;
  display: inline;
  margin: 5px;
}

.menuIcons li:hover {
  background: #0a8eb9 !important;
  color: #fff !important;
}

.generalSearchCard p {
  font-family: "NeoSansArabic";
  font-size: 14px;
  text-align: right;
}

.generalSearchCard .munSpan {
  /* color: #117074; */
}

.generalSearchCard .distSpan {
  /* color: #25b6bd; */
}

.generalSearchCard .googleIcon,
.generalSearchCard .zoomIcon {
  font-size: 17px;
  color: #0a8eb9;
  margin-right: 4px;
  margin-left: 4px;
}

.generalResultDetails table {
  text-align: right;
  direction: rtl;
}

.generalResultDetails .react-tabs__tab-list {
  text-align: center;
}

.generalResultDetails .react-tabs__tab {
  padding: 4px !important;
}

.generalResultDetails .react-tabs__tab-list svg {
  color: #284587;
  font-size: 15px;
}

.generalResultDetails .react-tabs__tab-list button:hover svg {
  color: #284587;
}

.generalResultDetails .react-tabs__tab-list li {
  /* margin: 0px 3px; */
  /* border: 1px solid#284587; */
  /* border-radius: 50%; */
}

.generalResultDetails .react-tabs__tab--selected {
  border: none !important;
  background-color: transparent !important;
  border-radius: 50%;
  padding: 1px 1px !important;
  filter: brightness(0) saturate(100%) invert(52%) sepia(10%) saturate(2334%)
    hue-rotate(140deg) brightness(90%) contrast(91%) !important;
}

#outerSVG {
  filter: invert(46%) sepia(11%) saturate(6%) hue-rotate(359deg) brightness(92%)
    contrast(88%) !important;
}

.generalResultDetails .react-tabs__tab--selected svg,
.generalResultDetails .react-tabs__tab--selected #outerSVG,
.identifyScreen div div .activeBtn #outerSVG {
  color: #fff !important;
  filter: unset !important;
}

.generalResultDetails .react-tabs__tab-list {
  /* background: #f7f7f7 !important; */
  padding: 5px;
}

.tooltipButton,
.tooltipButton:hover,
.tooltipButton:focus {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 5px !important;
  background-color: transparent !important;
}

/*breadcrumbs*/
.searchStepsWizard {
  text-align: right !important;
  direction: rtl;
}

.breadcrumbs {
  padding: 10px;
  border-radius: 0.3rem;
  display: flex;
  overflow: hidden;
  margin-top: 5px;
  justify-content: flex-start;
  align-items: center;
}

.breadcrumbs li {
  list-style-type: none;
}

.breadcrumbs__item {
  background: #fff;
  color: #333;
  outline: none;
  padding: 10px 35px;
  position: relative;
  cursor: pointer;
  text-decoration: none;
  /* clip-path: polygon(100% 0%, 79% 50%, 100% 100%, 25% 100%, 7% 51%, 25% 0%); */
  /* background-color: #edf1f5; */
}

.breadcrumbs .first {
  clip-path: unset !important;
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.breadcrumbs .second,
.breadcrumbs .third {
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding: 0;
}

.breadcrumbs__item:hover {
  /* background: #0a8eb9; */
  color: #fff;
}

.breadcrumbs__itemActive {
  background: transparent;
  color: #284587 !important;
  padding: 0;
}

/*Help*/

/*ReactJoyride*/
.react-joyride__tooltip button[title="التالي"],
.react-joyride__tooltip button[title="الأخيرة"],
.react-joyride__tooltip button[title="السابق"] {
  background-color: #0a8eb9 !important;
  font-size: 0 !important;
  color: #fff !important;
  z-index: 99999999 !important;
}

.react-joyride__tooltip button[title="الأخيرة"] {
  animation: float 2s ease-out infinite;
}

.react-joyride__tooltip button[title="إغلاق"] {
  animation: float 2s ease-out infinite;
}

.react-joyride__tooltip button[title="Skip"],
.react-joyride__tooltip button[title="Open the dialog"] {
  font-size: 0 !important;
  display: none !important;
  z-index: 0 !important;
}

.react-joyride__beacon,
.react-joyride__beacon > span {
  font-size: 0 !important;
  display: none !important;
  z-index: 0 !important;
}

.react-joyride__tooltip button[title="التالي"]::after {
  content: "\f061" !important;
  font-family: "Font Awesome 5 Free" !important;
  font-weight: 900 !important;
  font-size: 15px !important;
}

.react-joyride__tooltip button[title="السابق"]::after {
  content: "\f060" !important;
  font-family: "Font Awesome 5 Free" !important;
  font-weight: 900 !important;
  font-size: 15px !important;
}

.react-joyride__tooltip button[title="الأخيرة"]::after {
  content: "\f00c";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  font-size: 15px;
}

.react-joyride__tooltip button[title="إغلاق"] {
  right: auto !important;
  color: #0a8eb9 !important;
}

.react-joyride__tooltip button[title="التالي"],
.react-joyride__tooltip button[title="السابق"] {
  transform: scale(-1) !important;
}

.react-joyride__tooltip button[title="السابق"] {
  position: absolute;
  right: 10px;
}

.helpFirstTime #react-joyride-step-0 .__floater {
  left: 90px !important;
}

.__floater__body {
  margin-left: 20px !important;
}

.react-joyride__tooltip button[title="التالي"],
.react-joyride__tooltip button[title="الأخيرة"] {
  position: absolute;
  left: 10px;
}

.react-joyride__tooltip > div > div {
  padding: 20px 10px 0px !important;
}

.react-joyride__beacon,
.react-joyride__beacon > span {
  font-size: 0 !important;
  display: none !important;
  z-index: 0 !important;
}

.react-joyride__overlay {
  background-color: rgb(255 255 255 / 10%) !important;
  z-index: unset !important;
}

.react-joyride__spotlight {
  border: 1px solid #25b6bd !important;
  background-color: #25b6bd !important;
  opacity: 0.3 !important;
}

/* .__floater {
  top: -12px !important;
} */
.ant-tooltip-inner {
  color: #fff !important;
  background-color: #338c9a !important;
  border-radius: 20px !important;
}

.ant-tooltip-arrow-content {
  background-color: #338c9a !important;
}

.generalSearchCard:hover svg,
.generalSearchCard:hover p,
.generalSearchCard:hover h5,
.generalSearchCard:hover span {
  /* color: #fff !important; */
}

/* .generalSearchCard:hover img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7494%)
    hue-rotate(198deg) brightness(105%) contrast(104%);
} */

.SideMenuOpenArrow {
  position: absolute;
  z-index: 99;
  top: 10px !important;
}

.SideMenuOpenArrow svg,
.closeMenuIcon svg {
  font-size: 25px !important;
  color: rgba(40, 69, 135, 1) !important;
}

.closeMenuIcon {
  margin-left: 0px !important;
}

.SideMenuOpenArrow button,
.closeMenuIcon {
  padding: 8px !important;
  background: transparent !important;
  top: 10px !important;
  width: 40px;
  /* box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%); */
  cursor: pointer;
}

.SideMenuOpenArrow button {
  margin: 0 !important;
  padding: 0 !important;
  /* width: 0 !important; */
  background: red !important;
  padding: 5px !important;
  width: auto !important;
  background: rgba(255, 255, 255, 0.85) !important;
  right: -20px !important;
}

/* .SideMenuOpenArrow button:hover svg,
.SideMenuOpenArrow button:hover,
.closeMenuIcon:hover {
  color: #fff !important;
  background-color: #25b6bd !important;
} */

/* .closeMenuIcon:hover svg {
  color: #fff !important;
} */

/*Animations*/

.generalSearchCard:after {
  content: "";
  position: absolute;
  top: 0;
  left: 100px;
  width: 500%;
  height: 1000%;
  background: rgba(37, 182, 189, 0.6) !important;
  z-index: -1;
  transform-origin: 0% 0%;
  transform: translateX(calc(20% - 25px)) translateY(10%) rotate(-45deg);
  transform: translateY(10%) translateX(16%) rotate(-45deg);
  transition: transform 0.3s;
}

.generalSearchCard:hover::after {
  transform: translateY(10%) translateX(-200px) rotate(-45deg) !important;
}

.SearchBtn,
.esri-distance-measurement-2d__clear-button,
.esri-area-measurement-2d__clear-button {
  margin: 1em;
  /*  */
  letter-spacing: 1px;
  height: 3em !important;
  width: 18em !important;
  background: #284587;
  color: #fff;
  font-size: 1.05em;
  border: 1px solid #284587;
  border-radius: 13px;
  transition: all 1s ease-out;
  box-shadow: inset 0 0 #284587;
  margin-top: 21px !important;
}

.seeMoreBtn,
.printBtn {
  margin: 1em;

  letter-spacing: 1px;

  background: transparent;
  color: #284587 !important;
  font-size: 1.05em;
  border: none;
}

.seeMoreBtn:hover,
.seeMoreBtn:focus,
.printBtn:hover,
.printBtn:focus {
  background: transparent;
  color: #284587 !important;
}

.seeMoreBtn {
  margin: 0 !important;
  margin-bottom: 2px !important;
}

.tableStatBtn {
  background-color: #0a8eb9 !important;
  color: #fff !important;
  float: left !important;
}

.tableStatClose {
  /* background-color: #ff4d4f !important; */
  background: transparent !important;
  color: #284587 !important;
  float: left !important;
  /*  */
  border: none !important;
}

#openedToolsMenuLi:hover img,
.openedservicesMenu li:hover img {
  filter: unset !important;
}

/* #openedToolsMenuLi:hover,
.openedservicesMenu li:hover {
  transform: scale(1.2);
} */
.SearchBtn:hover,
.esri-distance-measurement-2d__clear-button:hover,
.esri-area-measurement-2d__clear-button:hover,
#openedToolsMenuLi:hover,
.openedservicesMenu li:hover {
  /* box-shadow: inset 12em 0 #284587; */
  cursor: pointer;
  color: #fff !important;
}

.esri-distance-measurement-2d__clear-buttonو.esri-area-measurement-2d__clear-button {
  margin: 10px auto;
}

.toc {
  /* overflow-y: scroll !important; */
  width: 267px;
  /* height: 340px; */
}

.toc li {
  list-style: none;
}

.toc-map {
  margin-top: 20px;
  padding: 2%;
  background: white;
  overflow-x: hidden;
  direction: rtl;
  background: transparent;
}

.toc-result {
  padding: 2%;
  display: grid;
  grid-template-rows: 50px 65vh;
  background: white;
  /* overflow-x: hidden; */
  direction: rtl;
  overflow-x: hidden;
  background: transparent;
  overflow-y: scroll;
}

.disableLabel {
  /* color: #707070; */
  /* border-radius: 15px; */
  /* padding: 5px; */
  /* margin-bottom: 5px; */
  margin-bottom: 8px;
}

.enableLabel {
  /* color: #fff; */
  /* background: #00619b; */
  /* color: black; */
  /* border-radius: 15px; */
  /* padding: 5px; */
  margin-bottom: 8px;
}

/* .toc-gallery {
  display: grid;
  padding: 0%;
  grid-template-columns: 10px 15px auto 25px;
  grid-gap: 10px;
} */

.toc-gallery {
  margin-bottom: 10px;
}

.toc-gallery,
.toc-gallery-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ant-spin-dot-item {
  background-color: #099655 !important;
}

.galleryHead {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0;
  padding: 5px;
}

.heading_inquiryTool {
  color: #284587;
}

.closeServMenu {
  color: #284587 !important;
}

.identifyScreen {
  overflow-y: auto;
  height: auto;
  max-height: 490px;
  direction: rtl;
}

.identifyTableStyle {
  margin-top: 10px;
  direction: rtl;
  /* border-bottom: 7px solid #25b6bd; */
}

.identifyTR {
  border-bottom: 1px solid #80808057 !important;
  border-top: none !important;
  height: 44px;
}

.infoTableTd {
  /* font-weight: bold; */
  white-space: normal;
}

.infoTableData {
  white-space: normal;
}

.print-box {
  background: rgba(0, 0, 0, 0.16);
  position: fixed;
  z-index: 1;
  border: 2px dashed #757575;
  fill-opacity: 0;
  pointer-events: none;
  cursor: pointer;
}

.printStyle {
  /* padding: 10px; */
  margin-top: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.spinStyle {
  position: fixed !important;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  background-color: rgb(16 16 16 / 20%);
  align-content: center;
  z-index: 100;
}

.loadingPortalStyle {
  margin-top: 20px;
  width: 100% !important;
  height: 100% !important;
  text-align: center;
  z-index: 100;
}

.spinStyleConatiner {
  text-align: center;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
}

.selectLabelStyle {
  margin: 3px 11px 0;
  color: #284587a3;
  float: right;
  font-size: 12px;
}

.coordinateData {
  font-weight: bold;
  text-align: center;
  margin-top: 10px;
}

.resultTitle {
  font-family: "NeoSansArabic";
  font-size: 15px;
  /* padding: 10px; */
  display: flex;
  align-items: center;
  /* background: #edf1f5; */
  justify-content: space-between;
  direction: rtl;
  color: #284587;
}

.searchInfoStyle {
  margin-top: 20px;
  text-align: center;
  color: red;
  font-family: "NeoSansArabic";
  font-size: 16px;
  white-space: break-spaces;
}

.ant-pagination-options {
  display: none !important;
}

.noDataStyle {
  text-align: center;
  margin-top: 50px;
  font-size: 17px;
}

.searchLocationInfo {
  margin-right: 5px;
  color: lightslategray;
  white-space: pre;
}

.filterModal .ant-modal-title,
.filterModal button {
  /* text-align: center !important;
   */
  color: #284587;
  font-weight: bold;
}

.filterModal .ant-col {
  padding: 5px !important;
}

.tableActionsUl {
  display: flex !important;
  list-style-type: none !important;
}

/*LOADER*/
.spinner-bk {
  background: rgba(240, 243, 244, 0.4);
  position: fixed;
  z-index: 9999999 !important;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
}

.sk-cube-grid {
  width: 40px;
  height: 40px;
  margin: 20% auto;
}

.sk-cube-grid .sk-cube {
  width: 33%;
  height: 33%;
  background-color: #0a8eb9;
  float: left;
  -webkit-animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
  animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
}

.sk-cube-grid .sk-cube1 {
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

.sk-cube-grid .sk-cube2 {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}

.sk-cube-grid .sk-cube3 {
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
}

.sk-cube-grid .sk-cube4 {
  -webkit-animation-delay: 0.1s;
  animation-delay: 0.1s;
}

.sk-cube-grid .sk-cube5 {
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

.sk-cube-grid .sk-cube6 {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}

.sk-cube-grid .sk-cube7 {
  -webkit-animation-delay: 0s;
  animation-delay: 0s;
}

.sk-cube-grid .sk-cube8 {
  -webkit-animation-delay: 0.1s;
  animation-delay: 0.1s;
}

.sk-cube-grid .sk-cube9 {
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

@-webkit-keyframes sk-cubeGridScaleDelay {
  0%,
  70%,
  100% {
    -webkit-transform: scale3D(1, 1, 1);
    transform: scale3D(1, 1, 1);
  }

  35% {
    -webkit-transform: scale3D(0, 0, 1);
    transform: scale3D(0, 0, 1);
  }
}

@keyframes sk-cubeGridScaleDelay {
  0%,
  70%,
  100% {
    -webkit-transform: scale3D(1, 1, 1);
    transform: scale3D(1, 1, 1);
  }

  35% {
    -webkit-transform: scale3D(0, 0, 1);
    transform: scale3D(0, 0, 1);
  }
}

.ant-message {
}

.canvas-line-zoom {
  border: 1px solid red;
  position: fixed;
  top: 0;
  z-index: 100000;
  pointer-events: none;
  border: none;
}

.ant-select-dropdown {
  direction: ltr !important;
}

.esri-select {
  font-size: 16px !important;
  padding-right: 25px !important;

  background-position: left;
}

.esri-select option {
  font-size: 15px !important;
  /*  */
  font-family: "Droid Arabic Kufi", sans-serif !important;
}

.esri-distance-measurement-2d__measurement {
  background: none;
}

.esri-area-measurement-2d__measurement-item,
.esri-distance-measurement-2d__measurement-item {
  margin-bottom: 25px !important;
  border-bottom: 1px solid #28458740 !important;
  direction: rtl;
  justify-content: space-between;
  flex-direction: row;
}

.esri-area-measurement-2d__measurement-item:last-child {
  border-bottom: none !important;
  margin-bottom: 0 !important;
}

.esri-distance-measurement-2d__measurement-item {
  margin-bottom: 0 !important;
  border-bottom: none !important;
  padding: 0 !important;
}

.esri-area-measurement-2d__measurement-item-title,
.esri-area-measurement-2d__measurement-item-value,
.esri-distance-measurement-2d__measurement-item-title,
.esri-distance-measurement-2d__measurement-item-value {
  color: #284587;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
}

.esri-area-measurement-2d__units-label,
.esri-distance-measurement-2d__units-label {
  font-family: Droid Arabic Kufi !important;
  font-weight: 400;
  font-size: 11px;
  color: #28458799;
}

.ant-notification.ant-notification-topLeft.ant-notification-rtl {
  text-align: right;
  color: white;
}

.ant-notification-notice-description {
  font-size: 18px !important;
  font-weight: bold;
}

.ant-notification-notice.ant-notification-notice-closable {
  background-color: #0a8eb9;
}

.no-chosen-layer {
  height: 200px;
  width: 100%;
  flex-direction: column;
  display: flex;
  text-align: center;
  justify-content: center;
  margin-right: 25%;
  border: dotted #0a8eb9;
}

.exportMenu span {
  /*  */
}

.exportPdful {
  list-style-type: none;
  display: flex;
  text-align: right;
  direction: rtl;
  padding-top: 15px;
}

.exportPdfRightLi {
  text-align: center;
  /* border-left: 3px solid #000; */
  padding-left: 5px;
  margin-left: 7px;
  height: fit-content;
}

.exportPdful h6 {
  font-weight: bold;
}

.exportPdfPage table {
  padding: 10px;
  direction: ltr;
  print-color-adjust: exact;
}

.exportPdfPage {
  overflow-y: scroll;
  height: 100vh !important;
  /* border: 1px solid #000; */
  margin: 10px;
  padding-top: 20px;
}

@media only print {
  .sunburst-viz {
    zoom: 0.5;
  }

  .printPdf {
    display: none;
  }

  .exportPdfPage {
    overflow-y: visible !important;
  }

  .exportPdfPage table {
    line-height: 2.5;
  }

  .reportStyle2 td {
    font-size: 17px !important;
  }

  .printBtn,
  .printBtnDisplay {
    display: none !important;
  }

  html {
    /* overflow: hidden; */
  }

  .exportPdfPage {
    display: block;
    width: auto;
    height: auto;
    overflow: visible;
  }

  .mapDiv {
    padding: 10px !important;
  }

  body {
    box-sizing: border-box;
    border: 1px solid black !important;
  }

  .one-page {
    /* padding: 10px;
    margin: 10px; */
    /* margin: 0;
    padding: 10px;
    border: initial;
    border-radius: initial;
    width: initial;
    min-height: initial;
    box-shadow: initial;
    background: initial; */
    page-break-before: always !important;
    zoom: 0.95;
  }

  .print-button {
    display: none;
  }

  .underlineStyle {
    margin-top: 20px;
    box-shadow: 0 0 2px 1px black;
    text-align: center;
    padding: 5px;
  }
}

.chartInfo:hover {
  -ms-transform: scale(2);
  -webkit-transform: scale(2);
  transform: scale(2);
}

.barChartTootltip {
  margin: 0px;
  padding: 10px;
  font-size: 11px;
  background-color: rgb(255, 255, 255);
  border: 1px solid rgb(204, 204, 204);
  white-space: nowrap;
  position: absolute;
  margin-right: -100px;
}

.generalDataTableMin {
  right: 510px;
  z-index: 10000 !important;
  direction: rtl;
  border-radius: 10px;
  margin-inline-end: 60px;
  margin-bottom: 10px;
}

.generalDataTableMax {
  right: 365px;
  z-index: 10000 !important;
  direction: rtl;
}

.centerPrintChart {
  width: 200px;
  display: block;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}

.centerPrintChart label {
  white-space: pre-wrap;
  margin-top: 30px;
  font-size: 23px;
}

.tableTitle {
  text-align: right;
  width: 50%;
  margin-right: 10px;
  font-size: 16px;
  margin-bottom: 10px;
}

.fullscreen {
  display: contents !important;
}

/*Meta Statistics*/
.metaStatModal {
  z-index: 99999;
  width: 100% !important;
  background-color: rgba(0, 0, 0, 0.52);
  top: 0 !important;
  right: 0 !important;
  left: 0 !important;
  bottom: 0 !important;
  position: fixed !important;
  height: 100% !important;
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden !important;
  font-family: "arabicNums";
}

.metaStatModal .ant-modal-content,
.metaStatModal .ant-modal-header {
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
  background-color: transparent !important;
}

.metaStatModal .statTitle,
.metaStatModal th,
.metaStatModal td {
  font-family: "NeoSansArabic";
  color: #fff;
  text-align: center;
}

.metaStatModal .colorBall {
  width: 20px;
  height: 20px;
  padding: 10px;
  border-radius: 50%;
  margin: auto;
}

.metaStatModal .ant-modal-footer {
  display: none !important;
}

.marsedStyle button:disabled {
  cursor: not-allowed;
  color: #00000040 !important;
  border-color: #d9d9d9;
  background: #f5f5f5;
  text-shadow: none;
  box-shadow: none;
}

.identifyBuildTitle {
  text-align: center;
  width: 100%;
  font-family: "NeoSansArabic";
  font-size: 18px;
  padding: 10px;
  background: #edf1f5;
  font-weight: bold;
}

.metaStatModal .ant-modal-close-icon svg {
  color: #fff !important;
  font-size: 45px;
  border: 2px solid #fff;
  padding: 5px;
}

.tableFiltersButtons {
  float: left;
}

.tableFiltersButtons svg {
  color: #284587;
}

.tableFiltersButtons svg:hover {
  color: #0a8eb9;
}

/*land Data*/

.landDataIconsModal {
  border-radius: 15px;
  background-color: #fff;
  box-shadow: 0px 0px 4px 0px rgba(4, 4, 4, 0.26);
  position: absolute;
  z-index: 999;
}

.landmodalTitle {
  text-align: right;
  width: 100%;
  font-family: "NeoSansArabic";
  font-size: 18px;
  padding: 5px;
  padding-right: 10px;
  color: #edf1f5;
  background-color: #0a8eb9;
  /* font-weight: bold; */
}

.landIconsUl {
  list-style-type: none;
  display: flex;
  padding: 5px;
  justify-content: space-between;
}

.landIconsLi {
  margin: 0px 5px;
  width: 45px;
  height: 45px;
  border-radius: 50px;
  box-shadow: 0 0 6px 0 rgb(0 0 0 / 35%);
  background-color: #0a8eb9;
  padding: 8px;
  padding-top: 4px;
}

.closeIconsIcon {
  float: left;
  margin-top: 5px;
}

.arrow-popup-modal {
  position: absolute;
  left: 49%;
  top: 37%;
  font-size: 54px;
  color: #fffdfd;
  z-index: 101;
}

.serviceIcon {
  text-shadow: 0px 1px 0px #000;
}

.landDetails {
  color: #000;
  font-family: "NeoSansArabic";
  text-align: right;
  padding: 5px 10px;
  font-size: 16px;
  white-space: break-spaces;
}

.landDetailClose {
  position: absolute;
  left: 10px;
  top: 10px;
  color: #fff;
  font-size: 20px;
}

.landDetailsModal li {
  text-align: right;
  font-family: "NeoSansArabic";
  padding: 5px;
  color: #000;
}

.landDetailsModal ul {
  direction: rtl;
}

.metaStatModal .ant-modal-body {
  height: 100vh;
}

.metaStatModal .ant-modal-close-x {
  display: none;
}

.metaTableIcons .tableHeaderBtn svg {
  color: #284587 !important;
  font-size: 20px;
}

.metaTableIcons table {
  border-collapse: separate;
  border-spacing: 0 10px !important;
}

.metaTableIcons thead {
  background-color: #338c9a33 !important;
  color: #284587 !important;
}

.metaTableIcons thead th {
  border-left: 2px solid #ddd !important;
}

.ant-table-thead > tr > th {
  background-color: inherit !important;
}

.metaTableIcons tr:hover td {
  background-color: #00000013 !important;
}

.metaTableIcons tr td button {
  padding: 0 !important;
  margin: 4px !important;
  width: fit-content !important;
}

.metaTableIcons td {
  padding: 5px;
  text-align: center;
  border-left: 2px solid #284587;
  border-bottom: 0px;
  vertical-align: middle;
}

.metaTableIcons .tableHeaderBtn svg:hover {
  color: #0a8eb9 !important;
}

.searchTableHidden th svg,
.searchTableShown th svg,
.searchTableShown .ant-table-column-sorter-up svg,
.searchTableHidden .ant-table-column-sorter-down svg {
  /* color: dimgrey; */
  color: #284587;
}

.marsadDeleteBtn {
  width: 100px !important;
}

.englishFont div {
  /* font-family: sans-serif !important; */
}

.englishFont span {
  /* font-family: sans-serif !important; */
}

.ant-table-column-sorter-down.active svg,
.ant-table-column-sorter-up.active svg,
.ant-table-filter-trigger.active svg {
  color: #0a8eb9 !important;
}

.searchTableShown .ant-table-row {
  position: relative;
}

.pagination-container {
  /* position: absolute; */
  bottom: 0%;
  right: 50%;
  position: -webkit-sticky;
  /* Safari */
  position: sticky;
}

#outerFormWidthFit {
  width: 380px;
}

#outerFormWidth {
  /* width: 300px; */
}

.metastatTable {
  background-color: rgba(0, 0, 0, 0.3);
  width: 80%;
}

.metastatTable2 {
  background-color: rgba(0, 0, 0, 0.3);
}

.metastatTable th {
  border: none !important;
  text-align: right !important;
}

.metastatTable2 th,
.metastatTable2 td {
  border-top: none !important;
  border-right: none !important;
  border-left: none !important;
}

.metastatTable td {
  border-left: none !important;
  border-right: none !important;
  border-bottom: none !important;
  text-align: right !important;
}

.metaStatBtns {
  position: absolute;
  left: 15px;
  bottom: 40px;
}

.plan-lands-statistics-tbl td,
.plan-lands-statistics-tbl th {
  padding: 0.1rem 0.5rem;
  text-align: center !important;
}

/*Translate*/

.translateIcon {
  color: #00619b;
  font-weight: bold;
  font-size: 17px;
}

.translateIcon:hover {
  color: #fff;
}

.metaHeaderSpacePadding {
  margin-right: 40px;
}

.breadcrumbs__item p {
  margin: 0 !important;
}

/*Header Styles*/
.Dashboard-Header {
  /* background: #0a8eb9 !important; */
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  height: 7vh;
  position: absolute;
  top: 11vh;
  width: 100vw;
}

.Dashboard-Header .navLink {
  width: 200px;
  font-style: normal;
  font-weight: 500 !important;
  font-size: 20px !important;
  line-height: 20px;
  padding: 10px;
  text-decoration: none !important;
  color: #d4d6de !important;

  margin: 2px 0px 2px 20px;
  letter-spacing: normal;
  text-align: center;
  border-radius: 0 !important;
  margin: 0 !important;
  -webkit-border-radius: 0 !important;
  -moz-border-radius: 0 !important;
  -ms-border-radius: 0 !important;
  -o-border-radius: 0 !important;
}

.Dashboard-Header .nav-link.active {
  color: #fff !important;
  font-weight: bold !important;
  border-bottom: 1px solid #fff !important;
}

.Header.navbar.fixed-top {
  z-index: 10 !important;
}

@media (max-width: 992px) {
  .Dashboard-Header {
    height: 100px !important;
  }

  .Dashboard-Header .userDropDown {
    margin-bottom: 37px !important;
  }

  .Dashboard-Header .navbar-nav {
    padding-top: 60px !important;
  }

  .Dashboard-Header .container-fluid {
    padding: 0 !important;
  }

  .Dashboard-Header .navLink {
    width: fit-content !important;
  }

  .Dashboard-Header .iconLink {
    margin-top: 20px;
  }
}

@media (max-width: 500px) {
  .Dashboard-Header {
    height: 145px !important;
  }

  .Dashboard-Header .navbar-nav {
    padding-top: 80px !important;
  }

  .Dashboard-Header .iconLink {
    margin-bottom: 50px !important;
  }
}

.pTextAlign {
  text-align: right;
}

/* .css-11o2879-MuiDrawer-docked .MuiListItemText-root {display: none;} */
.css-11o2879-MuiDrawer-docked .sideLinkDiv {
  height: 56px;
  padding-top: 0;
}

.css-11o2879-MuiDrawer-docked .changeLangIcon {
  width: 35px !important;
}

.css-11o2879-MuiDrawer-docked img {
  width: 20px !important;
  padding-top: 10px;
}

/***********Dashboard*********************/
.dashboardPage {
  overflow-y: scroll;
  overflow-x: hidden;
  width: 100vw;
  font-family: "arabicNums" !important;
}

.ant-modal-body {
  /* font-family: "arabicNums" !important; */
}

.statSquare {
  background-color: #ffebb0;
  margin: 0 3px 3px 0;
  height: 29.5vh;
}

.statSquare h6,
.mapSquaresGrid div h6 {
  /* font-family: "NeoSansArabic"; */
  font-size: 0.7rem;
  line-height: 1.93;
  text-align: center;
  font-weight: bold;
  color: #000;
  padding-bottom: 3px;
  margin: auto;
  padding-top: 10px;
}

.charts-below-map div h6 {
  /* font-family: "NeoSansArabic"; */
  font-size: 1.2rem;
  line-height: 1.93;
  text-align: center;
  font-weight: bold;
  color: #000;
  padding-bottom: 3px;
  margin: auto;
  /* padding-top: 10px; */
}

.statSquare h2,
.mapSquaresGrid div h2,
.charts-below-map div h2 {
  font-size: 40px;
  line-height: 2.1;
  text-align: center;
  font-weight: bold;
  color: #000;
}

.dashAfterHead {
  margin-top: 70px;
}

.select-wrap .ant-input {
  padding: 10px 12px 4px 11px !important;
}

.select-wrap .ant-select .ant-select-selector {
  padding: 10px 10px 4px 11px !important;
}

.select-wrap
  .ant-select-single:not(.ant-select-customize-input)
  .ant-select-selector {
  padding: 10px 10px 4px 11px !important;
  height: 7vh !important;
}

.select-wrap
  .ant-select-single
  .ant-select-selector
  .ant-select-selection-search {
  top: 16px !important;
}

#dashMapHeightDefault {
  height: 89vh;
}

#dashMapHeight,
.dashMap {
  height: 55vh;
}

.mapSquaresGrid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
}

.mapSquaresGrid .mapSquare,
.charts-below-map .mapSquare,
.normal-chart-item.mapSquare,
.single-chart-item.mapSquare,
.last-chart-item.mapSquare {
  background-color: #fff;
  color: #000;
  border: 1px solid #d9d9d9;
  height: 35vh;
  flex-grow: 0.5;
}

.dashboardMapTable1 td {
  width: 50%;
  /* font-family: "NeoSansArabic"; */
  text-align: center;
}

.dashTableTitle {
  margin: auto;
  text-align: center;
  /* font-family: "NeoSansArabic"; */
  font-weight: bold;
  padding: 10px;
}

.dashTable1 tbody {
  max-height: 30vh;
  display: block;
  overflow: auto;
}

.dashTable2 table,
.dashTable1 table {
  display: block;
  overflow: auto;
}

.dashTable2 tbody {
  display: block;
  max-height: 84.5vh;
  overflow: auto;
}

.dashTable2 tr,
.dashTable1 tr {
  display: table;
  width: 100%;
  table-layout: fixed;
}

.dashTable1,
.dashTable2 {
  margin-left: 5px;
}

.dashTable2 tr:nth-child(even) {
  background-color: #f2f2f2;
}

.dashDataTable th {
  text-align: center;
  color: #000;
  font-weight: bold;
  /*  */
}

.dashDataTable td {
  text-align: center;
  color: #00619b;
  font-size: 15px;
  /*  */
}

.dashDataPage h4 {
  /*  */
  float: right;
  text-align: right;
  margin: 20px;
  font-weight: bold;
  width: fit-content;
  border-bottom: 1px solid #000;
  padding-bottom: 4px;
}

.dashDataTable {
  width: 95%;
  margin: auto;
}

.dashHeaderSelectDiv {
  /* width: 190px; */
  margin: 0 10px;
  text-align: right;
}

.dashHeaderSelectDiv > div,
.select-year-month > div {
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.dashLabel {
  color: #000;
  padding-left: 2px;
  /* padding-right: 5px; */
  margin-top: 13px;
  width: fit-content;
}

.dashHeaderDates {
  display: flex;
  list-style-type: none !important;
  padding-top: 1px;
  padding-left: 5px;
}

.dashHeaderDates li {
  padding-top: 10px;
  color: #fff;
  /*  */
}

.dashHeaderDates .rmdp-container {
  height: 7vh !important;
}

.dashHeaderDates .rmdp-container .rmdp-input {
  height: 99% !important;
  width: 100px;
}

.apexcharts-toolbar {
  z-index: 1 !important;
}

.languageSideDiv .MuiButtonBase-root {
  padding: 0 !important;
}

.closeMenuIcon {
  margin-bottom: 20px !important;
}

.esri-distance-measurement-2d__clear-button,
.esri-area-measurement-2d__clear-button {
  margin: 10px auto !important;
}

/**************************************************/
/*Mobile media*/
.appStoreScreen {
  background-color: #fbfcfe;
  width: 100vw !important;
  padding-top: 100px;
  position: relative;
  height: 100vh !important;
  /* overflow: hidden; */
}

.appStoreUL h5 {
  font-weight: bold;
}

.Appfooter {
  background-image: url("./footerBG.jpg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
}

.smallfooter .smallfooterYear {
  font-family: "NeoSansArabic";
  font-size: 13px;
  text-align: center;
  color: #abafbe;
}

.Appfooter ul {
  list-style: none;
  text-align: right;
  padding-right: 0 !important;
}

.Appfooter h4 {
  text-align: right;
  font-family: "NeoSansArabic";
  font-size: 18px;
  font-weight: bold;
  line-height: 2.28;
  color: #364464;
}

.Appfooter li,
.Appfooter a {
  font-family: "NeoSansArabic";
  font-size: 16px;
  line-height: 2.5;
  text-align: right;
  color: #83899f;
  text-decoration: none !important;
}

.Appfooter a:hover {
  color: #fff;
  background-color: #00726f;
  padding: 0 20px;
  text-shadow: none;
  transition: 1s;
}

.footerTopI i {
  color: #83899f;
}

.footerTopI {
  text-align: left;
}

.Appfooter .conditions {
  background-color: #e7e9f1;
  padding: 10px;
  text-align: right;
}

.conditions h6 {
  font-size: 14px;
  font-weight: bold;
  line-height: 2.92;
  text-align: right;
  color: #5c6581;
  font-family: "NeoSansArabic";
}

.conditions p {
  font-size: 13px;
  line-height: 1.92;
  text-align: right;
  color: #83899f;
  font-family: "NeoSansArabic";
}

.conditions i,
.footIcon {
  color: #5c6581;
  padding: 0 5px;
  font-size: 30px !important;
}

.footerYear {
  font-family: "NeoSansArabic";
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  color: #364464;
}

.portalNavbar1 {
  box-shadow: 0 1px 0 0 #e7e9f1;
  background-color: #fbfcfe;
  height: 60px;
  padding-top: 0 !important;
  z-index: 120 !important;
}

.portalNavbar1 ul {
  list-style-type: none;
}

.portalNavbar1 i {
  font-size: 20px !important;
}

.leftUl {
  padding-left: 0 !important;
  padding-right: 5px !important;
  direction: ltr;
}

.portalNavbar1 li {
  display: inline-table;
  /* font-weight: 600; */
  font-family: "NeoSansArabic";
  font-size: 15px;
  /* padding-right: 20px; */
  line-height: 1.17;
  text-align: center;
  /* margin: 0 10px; */
  color: #364464 !important;
  letter-spacing: 1px;
  cursor: pointer;
  padding-top: 5px;
  padding-bottom: 5px;
}

.portalNavbar1 a {
  /* font-weight: 600; */
  font-family: "NeoSansArabic";
  font-size: 14px;
  line-height: 1.17;
  text-align: center;
  color: #364464 !important;
  letter-spacing: 1px;
  cursor: pointer;
}

.twitterIcon:hover {
  color: #1da1f2 !important;
}

.youtubeIcon:hover {
  color: #ff0000 !important;
}

.portalNavbar1 .navbar-nav {
  width: 100%;
  direction: rtl;
  display: block;
}

.appStoreUL li {
  display: inline;
}

.mobileLogo {
  float: right;
}

.AppfooterColLogo {
  text-align: right;
}

.footerYearContainer {
  background-image: url("./footerBG.jpg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
}

.leftUl li {
  padding-top: 15px;
}

.closeMenuIcon {
  position: absolute !important;
  left: 6px;
  top: 10px;
}

.drawerHeder {
  display: block !important;
}

/* .backBtn {
  display: grid !important;
  /* grid-template-columns: 1fr 2fr 1fr !important; */

.editionNo {
  color: #e60000;
}

.dashboard-page-layout {
  /* width: 99vw; */
  display: flex;
  flex-direction: row;
  /* position: relative;
  height: 91vh; */
  direction: rtl;
  margin-top: 7vh;
  margin-bottom: 10vh;
}

#layotHiddenDrops {
  margin-top: 1vh;
}

.dashboard-page-layout .apexcharts-menu-item {
  direction: rtl;
}

.dashboard-page-layout
  .left-side-chart-container
  .apexcharts-menu-item.exportCSV,
.dashboard-page-layout .Columnchart1 .apexcharts-menu-item.exportCSV,
.dashModalZoom .apexcharts-menu-item.exportCSV,
#Columnchart1 .apexcharts-menu-item.exportCSV {
  display: none;
}

.dashboard-page-layout .tbl-beside-map {
  width: 24vw;
  display: flex;
  flex-direction: column;
  border: 2px solid rgb(183, 181, 181);
  overflow-y: auto;
}

.dashboard-page-layout .map-wrapper {
  display: flex;
  flex-direction: column;
}

.dashboard-page-layout .charts-layout-row {
  display: flex;
  flex-direction: row;
  width: 100%;
  /* height: 65%; */
  flex-wrap: wrap;
  align-self: stretch;
  text-align: center;
  justify-content: center;
}

.dashboard-page-layout .charts-layout-col {
  display: flex;
  flex-direction: column;
  width: 100%;
  flex-wrap: wrap;
  align-self: stretch;
  text-align: center;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.dashboard-page-layout .charts-layout-row .normal-chart-item {
  width: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* justify-content: space-evenly; */
  height: auto;
  padding: 5px;
  min-height: 30%;
}

.dashboard-page-layout .charts-layout-col .normal-chart-item {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-evenly;
  height: auto;
}

.dashboard-page-layout .charts-layout-col .single-chart-item {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-evenly;
  height: auto;
}

.dashboard-page-layout .charts-layout-col .normal-chart-item .ape-chart {
  width: 60%;
}

.last-chart-item.mapSquare .ape-chart,
.single-chart-item .ape-chart {
  width: 100%;
}

.last-chart-item.mapSquare .ape-chart {
  /* width: 85%; */
}

.last-chart-item.mapSquare .ape-chart.bar {
  /* width: 350px; */
}

.last-chart-item.mapSquare {
  overflow: scroll;
  height: 35%;
}

.dashboard-page-layout .last-chart-item {
  align-items: center;
  display: flex;
  /* justify-content: center; */
  flex-direction: column;
  height: auto;
  padding: 10px;
}

.dashboard-page-layout .last-chart-item.text-count-info {
  justify-content: center;
}

.charts-below-map {
  display: flex;
  align-content: center;
  flex-direction: row;
  justify-content: space-evenly;
  flex-wrap: wrap;
  flex-grow: 1;
  border: 2px solid rgb(183, 181, 181);
}

.left-side-chart-container {
  border: 2px solid rgb(183, 181, 181);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 26vw;
}

.apexcharts-tooltip.apexcharts-theme-light {
  direction: ltr;
}

#Columnchart1 .apexcharts-tooltip.apexcharts-theme-light {
  direction: rtl;
  text-align: center;
}

#Columnchart1 .apexcharts-tooltip-series-group.apexcharts-active {
  justify-content: space-around;
}

/* clear calendar icon*/
.clear-calendar-icon {
  cursor: no-drop;
  font-size: large;
  height: min-content;
  margin: auto;
  display: flex;
  position: absolute;
  top: 40%;
  color: cadetblue;
  width: 34px;
  flex-direction: row;
  justify-content: center;
}

.generalResultDetails .react-tabs__tab-list {
  overflow: auto !important;
  direction: rtl;
}

.tableFiltersButtons .splitKrokyMetaSVG:hover {
  filter: invert(35%) sepia(84%) saturate(1302%) hue-rotate(166deg)
    brightness(94%) contrast(92%) !important;
}

.updaeContractImgClass {
  width: 25px !important;
  /* padding-top: 10px !important; */
  position: relative;
  top: 5px;
}

.select-year-month {
  /* width: 120px; */
  margin: 0 3px;
}

.identifyScreen div div .activeBtn {
  /* border: none !important;
  background-color: #0a8eb9 !important; */
}

.identifyScreen div div .activeBtn img {
  filter: brightness(0) saturate(100%) invert(52%) sepia(10%) saturate(2334%)
    hue-rotate(140deg) brightness(90%) contrast(91%) !important;
}

.tooltipButton.InqueryTool {
  margin: 6px 0;
  /* padding: 0 10px !important; */
}

/* .tooltipButton.archiveIcon{
  padding-top: 1rem !important;
} */
.identifyScreen div div .activeBtn svg {
  color: #fff !important;
}

.identifyScreen div div .activeBtn:hover {
  /* color: #0a8eb9;
  background-color: #0a8eb9 !important; */
}

.inqBTN,
.inqBTN:focus,
.inqBTN:hover {
  padding: 5px 10px !important;
}

.inqBTN svg {
  font-size: 17px !important;
}

/*map logo pointer*/
.map-pointer {
  cursor: pointer;
  width: 35px;
  margin: 4px;
}

.active-title {
  /* border: 2px dashed #f60101; */
  margin: 0.03em 0.25em;
}

/**********Navbar**************/
.portalNavbar1 {
  box-shadow: 0 1px 0 0 #e7e9f1;
  background-color: #fbfcfe;
  height: 60px;
}

.ant-dropdown-menu.ant-dropdown-menu-rtl {
  padding: 20px !important;
  border-radius: 10px !important;
}

.portalNavbar a:hover,
.portalNavbar1 a:hover {
  color: #00726f;
}

.ant-dropdown-menu-item:hover {
  background-color: #284587 !important;
  color: #fff;
}

.portalNavbar button,
.portalNavbar button:hover,
.portalNavbar button:active,
.portalNavbar button:focus {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  font-family: kufiBold;
  color: #0b2548 !important;
  text-align: right !important;
  outline: none !important;
}

.portalNavbar1 .btn,
.portalNavbar .btn {
  padding: 0 !important;
}

.portalNavbar1 button,
.portalNavbar1 button:hover,
.portalNavbar1 button:active,
.portalNavbar1 button:focus {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  font-size: 12px !important;
  /* padding: 5px !important; */
  /* margin: 0 10px; */
  font-family: kufiBold;
  color: #0b2548 !important;
  text-align: right !important;
  outline: none !important;
}

.portalNavbar {
  box-shadow: 0 2px 0 0 #e8e8ed;
  background-color: #f7fafd;
  margin-top: 60px;
  padding-top: 30px;
  z-index: 999 !important;
  padding-bottom: 30px;
}

.portalNavbar1 ul {
  list-style-type: none;
}

.portalNavbar1 i {
  font-size: 20px !important;
}

.portalNavbar1 li {
  display: inline-table;
  /* font-weight: 600; */
  font-family: "NeoSansArabic";
  font-size: 15px;
  line-height: 1.17;
  text-align: center;
  /* margin: 0 10px; */
  color: #364464 !important;
  letter-spacing: 1px;
  cursor: pointer;
  padding-top: 5px;
  padding-bottom: 5px;
}

.portalNavbar1 a {
  /* font-weight: 600; */
  font-family: "NeoSansArabic";
  font-size: 14px;
  line-height: 1.17;
  text-align: center;
  color: #364464 !important;
  letter-spacing: 1px;
  cursor: pointer;
}

.twitterIcon:hover {
  color: #1da1f2 !important;
}

.youtubeIcon:hover {
  color: #ff0000 !important;
}

.rightUl {
  position: absolute;
  right: 0;
  top: 10px !important;
}

.navitemBorder {
  content: "";
  width: 2px;
  height: 10px;
  background-color: #e8e8ed;
  padding: 4px 0.7px;
  margin: 0 15px;
  border-radius: 3px;
}

.LogoCenter {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 45%;
  text-align: center;
  font-size: 18px;
}

.dropdown-toggle::after {
  display: none !important;
}

.dropdown-toggle::before {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
  margin-right: 5px;
}

.ant-select-dropdown {
  text-align: right !important;
}

.mynavitem {
  padding: 0 20px !important;
}

.nav-link,
.navitem,
.navitem a {
  color: #0b2548 !important;
  text-decoration: none;
  font-size: 16px;
  font-family: "NeoSansArabic";
}

.navitem:hover {
  color: #025358 !important;
  font-weight: bold;
}

.userDropDown {
  color: #025358 !important;
  text-decoration: none;
}

.navitem-active {
  /* border-bottom: 3px solid #00726f; */
  font-weight: bold !important;
  color: #00726f !important;
}

.navbar-brand {
  position: absolute;
  top: 0;
  right: 0;
}

.navbar-brand img {
  width: auto;
  height: 70px;
}

.searchHeader {
  background-color: #223973;
  height: 60px;
  top: 60px;
  position: absolute;
  width: 100%;
}

.searchHeader .headTitle {
  color: #fff;
  text-align: right;
  padding-top: 20px;
  padding-right: 25px;
  font-weight: bold;
}

.headCenter {
  background-color: #fff;
  top: 8px;
  left: 40%;
  position: absolute;
  border-radius: 5px !important;
}

.headCenter .ant-input {
  width: 400px;
  border: none !important;
}

.headCenter .ant-radio-checked .ant-radio-inner {
  border-color: #223973 !important;
}

.headCenter .ant-radio-checked .ant-radio-inner::after {
  background-color: #223973 !important;
}

.headBtnSearch {
  position: absolute !important;
  top: 8px !important;
  height: 45px !important;
  background: transparent !important;
  color: #fff !important;
  width: 100px !important;
  left: 33% !important;
  border-radius: 6px !important;
}

/*Archive page*/
.archivePage {
  overflow: hidden !important;
  height: 100vh;
}

.archiveData {
  margin-top: 120px;
}

.ant-tree {
  background: #f7fafd !important;
  /* height: 100vh; */
  border-left: 1px solid #e0e0e0 !important;
  overflow-y: scroll;
  max-height: 80vh;
}

.archiveData .ant-table {
  background-color: #f7fafd !important;
}

.archiveData .ant-tree input {
  height: 0 !important;
}

.ant-tree {
  height: 100%;
}

.ant-tree.ant-tree-directory .ant-tree-treenode {
  text-align: right !important;
  /* height: 32px !important; */
  margin-bottom: 8px;
}

.ant-tree-title {
  word-break: break-word;
}

.ant-tree-list-holder-inner {
  padding-top: 10px;
}

.ant-tree.ant-tree-directory .ant-tree-treenode-selected::before {
  background: #d8e0f3 !important;
}

.ant-tree-title {
  color: #707070 !important;
  margin-right: 15px !important;
  font-size: 16px;
}

.anticon-folder-open,
.anticon-folder {
  font-size: 25px !important;
}

.anticon-folder-open svg,
.anticon-folder svg {
  color: #ffca28 !important;
}

.ant-tree-switcher-icon svg {
  color: #707070 !important;
  font-size: 20px !important;
}

.ant-table-wrapper.ant-table-wrapper-rtl .ant-table-pagination-left {
  justify-content: center !important;
  margin-top: 20px;
}

.ant-table-column-sorters {
  display: block !important;
  color: #707070 !important;
}

.ant-table-column-title {
  font-style: normal;
  font-weight: 700;
  font-size: 15px;
  color: #284587;
  vertical-align: middle !important;
  direction: ltr;
}

.ant-table-column-sorter {
  color: #707070 !important;
}

.ant-table-column-sorter-up,
.ant-table-column-sorter-down {
  font-size: 15px !important;
}

.ant-table-tbody > tr > td {
  color: #223973 !important;
  font-style: normal;
  font-weight: 500 !important;
  font-size: 16px;
  /* line-height: 30px; */
  text-align: right;
}

.tableBtn {
  font-style: normal;
  font-weight: bold !important;
  font-size: 14px;
  line-height: 27px;
  text-align: right;
  width: 80px !important;
  border: 1px solid #223973 !important;
  border-radius: 5px !important;
  color: #223973 !important;
  background-color: transparent !important;
  -webkit-border-radius: 5px !important;
  -moz-border-radius: 5px !important;
  -ms-border-radius: 5px !important;
  -o-border-radius: 5px !important;
}

.tableBtn:hover {
  background-color: #223973 !important;
  color: #fff !important;
}

.archiveData .ant-pagination-item-active a {
  color: #223973 !important;
}

.archiveData .ant-pagination-item-active {
  border-color: #223973 !important;
  border-radius: 4px !important;
}

.archiveFooter {
  background: #f7fafd;
  width: 100vw;
  position: absolute;
  bottom: 0;
}

.archiveFooter h6 {
  text-align: center;
  font-weight: 700;
  font-style: normal;
  padding: 15px;
  font-size: 16px;
  margin-bottom: 0 !important;
}

.archivePage .ant-table-content {
  padding-right: 22px;
}

.ant-table-wrapper::-webkit-scrollbar-thumb {
  background-color: #025358 !important;
}

.ant-table-wrapper {
  overflow: scroll !important;
  height: 70vh !important;
}

.ant-select-item-option-content {
  direction: rtl !important;
}

td {
  /* font-family: "arabicNums" !important; */
  color: #284587;
}

.rtl-direction {
  direction: rtl !important;
}

.ltr-direction {
  direction: ltr !important;
}

.exportPdfPage .ant-table-wrapper {
  height: auto !important;
  overflow: auto !important;
}

.sunburst-chart.planLandsStat {
  margin-top: 4rem;
}

.dashCalendarWithLabel .rmdp-container {
  margin-top: 30px;
}

.apexcharts-text tspan {
  font-size: 11px !important;
}

.printOnly {
  display: none;
}

@media print {
  .reportStyle2 {
    overflow: visible !important;
    padding: 1%;
    margin: 1%;
  }

  .dashDataTable table {
    page-break-inside: auto;
  }

  .dashDataTable table tr {
    page-break-inside: avoid;
    page-break-after: auto;
  }

  .dashDataTable table thead {
    display: table-header-group;
  }

  .dashDataTable table tfoot {
    display: table-footer-group;
  }

  .printOnly {
    display: block;
  }

  .no-print {
    display: none;
  }

  td {
    color: #000 !important;
  }

  .dashDataPage h4 {
    float: unset;
    margin: 10px auto;
  }

  .reportRow {
    /* height: 90px;
          font-size: 30px; */
    display: grid;
    /* width: 100%; */
    direction: rtl;
    border: 1px solid #e4e4e4 !important;
    /* height: 30px; */
    white-space: nowrap;
    color: black;
    grid-gap: 5px;
    padding: 2px;
    text-align: center;
    grid-template-columns: 1fr 1fr;
    font-size: 17px;
    border-top: 1px solid;
    border-bottom: 1px solid;
  }

  .reportRow {
    border: 1px solid;
  }

  /* .dashDataTable td{
    font-size: 18px ;
  } */
}

.printNav {
  width: 100vw;
  height: 120px !important;
  background-color: #025358;
  position: relative;
}

.routesData {
  position: absolute;
  width: 99vw;
}

.faplusChart {
  margin-left: auto;
  cursor: pointer;
  color: #00619b;
}

.dashModalZoom .Columnchart1,
.dashModalZoom .donut-chart {
  max-width: unset !important;
}

#headerHidden {
  position: absolute;
  top: -10vh;
  width: 100%;
  background-color: transparent;
  transition: top 1s;
}

#headerShown {
  width: 100%;
  position: absolute;
  /* top: 55px; */
  z-index: 99 !important;
  transition: top 1s;
  padding: 0;
}

.dashLogosNav {
  position: relative;
  height: 11vh;
}

.dashLogosNav .openCloseArrow {
  position: absolute;
  top: 85%;
  left: 50%;
  color: #00619b;
  font-size: 20px;
  cursor: pointer;
  z-index: 999;
}

.dashLogosNav #closeArrow {
  top: 75%;
}

.Columnchart1 {
  margin-top: 2px !important;
}

body {
  overflow: hidden;
}

.ant-select-dropdown {
  width: fit-content !important;
}

.white-color {
  color: white;
}

.text-center {
  text-align: center !important;
}

/*archive gallery modal*/
.archiveGalleryModal .ant-modal-content .ant-btn-primary {
  display: none;
}

.appsModal .ant-modal-content {
  width: 170%;
}

/*archive gallery modal*/

.dash-bottom-border {
  border-bottom: dashed;
}

/* 
it is style of akar report
*/

.reportStyle2,
.reportStyle-Suggestion {
  /* border: solid; */
  direction: rtl;
  color: black;
  overflow: auto;
  height: 92vh;
}

.reportStyle2,
.reportStyle-Suggestion {
  background: url("./reportBG.png");
}

.investment_report_header {
  justify-self: center;
  align-self: center;
  text-align: center;
  width: 60%;
}

.investment_report_header h4 {
  font-size: 18px;
}

.underlineStyle {
  margin-top: 20px;
  box-shadow: 0 0 3px 1px black !important;
  text-align: center;
  padding: 5px;
  font-size: 20px;
}

.divBorder {
  display: grid;
  grid-template-rows: 5vh auto;
  align-items: center;
  padding: 5px;
  /* border: 1px solid; */
  box-shadow: 0 0 0.5px -0.5px black;
}

.reportRow {
  display: grid;
  /* width: 100%; */
  direction: rtl;
  box-shadow: 1px solid #dbdbdb !important;
  /* height: 30px; */
  white-space: nowrap;
  color: black;
  grid-gap: 5px;
  text-align: center;
  grid-template-columns: 1fr 1fr;
  font-size: 17px;
}

.reportRow div {
  direction: ltr;
}

.labelReport {
  width: 100%;
  border: 1px solid;
  font-size: 20px;
  text-align: center;
  background-color: bisque;
  background-color: #e8e2c6;
  box-shadow: 0 0 6px black;
  margin-top: 2%;
}

.footer-report-print {
  position: fixed;
  display: flex;
  justify-content: space-between;
  direction: ltr;
  padding: 5px;
  bottom: 0;
  width: 100%;
}

.searchFormInput {
  border: none !important;
}

.searchFormInput input {
  background: none !important;
  border: none !important;
  width: 300px !important;
}

.SearchInputContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.SearchInputContainer img {
  /* margin-right: 5px !important; */
}

.searchInputWrapper {
  /* max-width: 200px; */
  opacity: 1;
  transition: all 0.3s ease;
  overflow: hidden;
}

.searchInputWrapper.hide {
  max-width: 0;
  opacity: 0;
  pointer-events: none;
}

.ant-form-item {
  margin-bottom: 0 !important;
}

.outerFormWidthHide {
  /* width: 300px; */
  padding: 0 !important;
  width: 50px;
  height: 50px;
  line-height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.searchToggleIcon {
  cursor: pointer;
}

.css-11o2879-MuiDrawer-docked .MuiDrawer-paper {
  border-radius: 30px 0px 0px 30px;
}

.SideMenu {
  direction: ltr !important;
}

.css-18sg6k4-MuiPaper-root-MuiDrawer-paper {
  border-radius: 24px 0px 0px 24px;
  /* width: 300px !important; */
}

#SearchSidemenu .css-18sg6k4-MuiPaper-root-MuiDrawer-paper {
  overflow: visible !important;
  width: 350px !important;
}

.css-18sg6k4-MuiPaper-root-MuiDrawer-paper {
  overflow: visible !important;
  background-color: #ffffffd9 !important;
  backdrop-filter: blur(8px) !important;
}

.openSideHelpCopy {
  /* top: 16px !important; */
  top: 19px !important;
  left: -20px !important;
  margin: 0 !important;
  padding: 0 !important;
  z-index: 9999 !important;
}

.MdKeyboardDoubleArrowLeft {
  border-radius: 24px;
  width: 32px;
  height: 32px;
  line-height: 32px;
  background: rgba(255, 255, 255, 0.85);
  color: rgba(40, 69, 135, 1);
}

.hashmap {
  display: flex !important;
  align-items: center;
}

.side_ListItemText {
  font-family: Droid Arabic Kufi !important;
  font-weight: 400;
  font-size: 16px;
  line-height: 16px;
  letter-spacing: 0%;
  text-align: right;
  vertical-align: middle;
  flex: auto !important;
}

.footerIcons {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  margin-top: 10px;
  padding: 0px 15px;
}

.footerIcons svg {
  color: #284587 !important;
  font-size: 20px;
}

.measurePageCopy_switch_btn_container {
  display: flex;
  align-items: center;
  /* justify-content: center; */
  margin: 5px 0px 8px;
}

.measurePageCopy {
  margin-top: 10px;
  padding-right: 20px;
}

.measurePageCopy button {
  background-color: #284587;
  /* width: 30px; */
  border-radius: 19px;
  /* width: 30px; */
  /* height: 30px; */
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0px 5px;
  padding: 8px !important;
  color: #fff;
}

.esri-widget {
  border-radius: 19px;
  background: none;
}

.esri-area-measurement-2d__container,
.esri-distance-measurement-2d__container {
  padding: 0;
}

.esri-area-measurement-2d__hint,
.esri-distance-measurement-2d__hint {
  padding: 0;
}

.esri-area-measurement-2d__hint-text,
.esri-distance-measurement-2d__hint-text {
  /* font-family: Droid Arabic Kufi; */
  font-weight: 400 !important;
  font-size: 12px !important;
  color: #284587 !important;
  padding: 0 !important;
}

.esri-area-measurement-2d__measurement,
.esri-distance-measurement-2d__container {
  background-color: transparent;
}

.esri-select {
  border-radius: 19px;
  height: 56px;
  padding: 0px 20px;
}

.custom-table {
  display: flex;
  flex-direction: column;
  width: 100%;
  color: #284587;
}

.custom-table .row {
  display: flex;
}

.custom-table .cell {
  flex: 1;
  padding: 8px;
  font-weight: 400;
  font-size: 12px;
}

.custom-table .center {
  text-align: center;
}

.custom-table .with-border {
  border: 1px solid #ccc;
  border-left: none;
  border-right: none;
}

.cell {
  text-align: right;
}

.mouseGreenPointContainer {
  background: #fff;
  padding: 10px;
  border-radius: 16px;
  margin: 20px 0px;
}

.mouseGreenPointConntent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #284587;
  border-bottom: 1px solid #d9d9d9;
  font-weight: 400;
  font-size: 12px;
  padding: 10px 5px;
}

.mouseGreenPointConntent:last-of-type {
  border-bottom: none;
}

.select-cust {
  display: grid !important;
  border: 1px solid #28458740;
  /* padding: 4px !important; */
  border-radius: 15px !important;
  /* height: 56px; */
  /* padding-block: 0 !important; */
  padding: 6px;
  padding-bottom: 3px;
  /* margin-top: 15px; */
}

.select-cust .ant-select-selector {
  background-color: transparent !important;
}

.select-cust .ant-form-item-label {
  padding: 0 !important;
}

.select-cust .ant-form-item-label label,
.select-cust label {
  color: #28458799 !important;
  font-size: 10px !important;
}

.select-cust input {
  padding: 0 11px !important;
  background: transparent !important;
  margin-top: -5px !important;
  border: none !important;
}

.select-cust input::placeholder,
.ant-select-selection-placeholder {
  color: #284587 !important;
}

.select-cust .ant-form-item-control-input {
  min-height: auto !important;
}

.ant-select-selector {
  border: none !important;
}

.generalSearchCard:hover {
  /* background: #eee; */
  box-shadow: inset -1px -1px 0px 0 #0a8eb9, 0px 4px 0px 0 #0a8eb9;
  /* border: none; */
  /* color: #0a8eb9; */
}

.infoTableData {
  /* text-align: right !important; */
}

.outerSearchDetailTrStyle tr {
  border-bottom: 1px solid#eee;
}

.importFile button {
  padding: 0 !important;
}

.importFilesTabs {
  border: 1px solid #28458740;
  border-radius: 13px;
  padding: 3px 13px 12px;
}

.importFilesTabs .pTextAlign {
  font-weight: 400;
  font-size: 14px;
  color: #28458799 !important;
}

.importFilesTabs .searchInput {
  padding: 5px;
}

.mainBaseMap,
.add_token {
  font-weight: 400;
  font-size: 16px;
  color: #284587;
}

.SearchBtnImportFiles {
  width: 100% !important;
  margin: auto !important;
  border-radius: 16px;
  background-color: #284587;
  color: #fff !important;
}

.ant-upload.ant-upload-select {
  display: block !important;
  margin-top: 5px !important;
}

.imgContainer {
  width: 88px;
  height: 55px;
  display: flex;
  border-radius: 8px;
  gap: 8px;
  background: #2845870d;
  align-items: center;
  justify-content: center;
}

.import_file input {
  background: transparent !important;
}

.ant-form label {
  margin-right: 10px;
}

.coordinate_search {
  box-shadow: 0.5px 0.5px 0.5px 0.5px;
  border-radius: 20px;
  padding: 5px;
  border: 1px solid #d9d4d4d4;
}

.ant-steps-rtl {
  zoom: 0.8;
}

.backBtn .ant-steps-rtl {
  margin-top: 3vh;
  zoom: 0.8;
}

.paintingIcons {
  font-size: 24px;
  /* margin-left: 5px; */
  color: #284587 !important;
}

.paintingText {
  font-weight: 400;
  font-size: 16px;
  color: #284587;
  margin-right: 6px;
}

.paintingTextInput {
  height: fit-content !important;
  /* padding: 8px 17px 25px !important; */
  border: 1px solid #28458740 !important;
  border-radius: 16px !important;
  background: transparent !important;
}

.measurmentToolCopy p {
  padding: 0 !important;
  direction: rtl;
}

.esri-select {
  text-align: right;
  padding-right: 10px !important;
  background-color: transparent;
  height: 28px;
  border: none;

  padding: 0 !important;
  padding-right: 3px !important;
  position: relative;
  top: -5px;
}

.esri-area-measurement-2d__units,
.esri-distance-measurement-2d__units {
  background: transparent;
  /* border: 1px solid #f1f1f1; */
  padding: 12px 15px 3px 10px;
  border-radius: 20px;
  border: 1px solid #28458740;
}

.esri-area-measurement-2d__units-select,
.esri-distance-measurement-2d__units-select {
  font-family: Droid Arabic Kufi !important;
  font-weight: 400;
  font-size: 16px;
  color: #284587;
}

.esri-distance-measurement-2d__settings,
.esri-area-measurement-2d__settings {
  padding: 6px 0px 0 11px;
}

.esri-area-measurement-2d__measurement {
  padding-right: 0;
}

.HeadingCutomTable {
  font-family: Droid Arabic Kufi;
  font-weight: 400;
  font-size: 12px;
  color: #284587;
  text-wrap: initial;
  margin-bottom: 12px;
}

.paintingTextInput::placeholder {
  font-weight: 400 !important;
  font-size: 11px !important;
  color: #284587 !important;
  /* Ensure it's not semi-transparent */
}

.Side-menu-mainContent {
  height: 90%;
}

.Side-menu-mainContent ul {
  height: 90%;
}

.Side-menu-icons {
  height: 10%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 10px;
}

.select-cust .ant-select:not(.ant-select-customize-input) .ant-select-selector {
  background-color: transparent !important;
}

.ant-select-selection-placeholder {
  color: #223973 !important;
}

#searchMeta .ant-table {
  background: transparent !important;
}

#searchMeta .ant-table-thead > tr > th {
  /* background: #d1e0de !important; */
  padding: 13px 10px;
  /* background: #d1e0de !important; */
  padding: 13px 10px;
  zoom: 0.8;
  /* text-align: center; */
}

#searchMeta thead {
  border-radius: 12px;
}

#searchMeta .ant-table-thead > tr > th {
  /* background: #2d98ba47 !important; */
}

.openMetaHelp .ant-input-affix-wrapper-rtl {
  direction: rtl;
  border-radius: 15px;
  margin: 10px -4px;
}

.activeService .hashTest {
  color: #fff !important;
}

.close_icon {
  color: #000 !important;
}

.Heading_tocComponent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e3e3e3;
  padding-bottom: 10px;
}

.Heading_tocComponent label {
  font-family: Droid Arabic Kufi;
  font-weight: 400;
  font-size: 16px;
  color: #284587;
}

.Heading_tocComponent img {
  cursor: pointer;
}

.opacityLayers {
  display: flex;
  flex-direction: row-reverse;
  margin: 10px 0px;
  flex-direction: column;
  justify-content: start;
}

.opacityLayers span {
  text-align: right;
}

.sliderLayers {
  /* width: 100%; */
  margin: 5px 0px 5px 6px !important;
}

.tocComponent_Ul {
  padding: 0 !important;
  max-height: 450px;
  overflow: scroll;
}

.tocComponent_Ul li {
  background: transparent;
  margin-right: 5px;
}

.toc_arrow {
  color: #284587 !important;
  width: 24px;
  height: 24px;
}

.gallery {
  padding-top: 10px;
  padding-right: 16px;
  width: 258px;
}

.gallery-img {
  float: right;
  width: 29%;
  margin-left: 10px;
  border-radius: 5px;
  text-align: center;
  margin-bottom: 10px;
  background: white;
  box-shadow: 0px 0px 4px 0px rgba(4, 4, 4, 0.26);
  font-size: 13px;
  overflow-wrap: break-word;
}

.gallery-img img {
  width: 100%;
}

.toc-gallery-content_checkbox {
  appearance: none;
  -webkit-appearance: none;
  width: 18px !important;
  height: 18px !important;
  background-color: white;
  border: 1px solid #284587;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  margin: 0px 10px !important;
}

.toc-gallery-content_checkbox:checked {
  background-color: #284587 !important;
}

.toc-gallery-content_checkbox:checked::after {
  content: "";
  position: absolute;
  left: 6px;
  top: 3px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.toc-gallery-content_Label {
  font-family: Droid Arabic Kufi !important;
  font-weight: 400;
  font-size: 16px;
  color: #284587;
}

.ant-steps-label-vertical .ant-steps-item-content {
  text-align: right !important;
}

.ant-steps-label-vertical .ant-steps-item-tail {
  margin-left: 58px;
  padding: 7.5px 0 !important;
  /* width: auto!important; */
}

.ant-steps-label-vertical .ant-steps-item-content {
  width: auto !important;
}

.ant-steps-item-title {
  color: #338c9a !important;
  font-weight: bold;
}

.ant-form-item-label > label::after {
  display: none;
}

.checkBox_print {
  font-family: Droid Arabic Kufi;
  font-weight: 400 !important;
  font-size: 16px !important;
  color: #284587 !important;
}

.printCheckbox {
  margin-top: 15px;
  margin-bottom: 15px;
}

.printButtons-container {
  display: flex;
  align-items: center;
  flex-direction: column;
}

.SearchBtn {
  background-color: #284587;
  width: 100% !important;
  font-family: Droid Arabic Kufi;
  font-weight: 700;
  font-size: 16px;
  color: #ffffff;
  border-radius: 16px;
  margin: 0 !important;
  margin-bottom: 15px !important;
}

.compareLayers-select .ant-select-arrow {
  left: 22px !important;
  margin-top: -18px !important;
}

.interActive-span {
  font-weight: 400;
  font-size: 11px;
}

.logo-span {
  font-family: Droid Arabic Kufi;
  font-weight: 400;
  font-size: 16px;
  color: #284587;
}

.MdKeyboardArrowDown_icon {
  color: #284587;
}

.delete_all_toolsbox {
  border-radius: 16px !important;
  font-family: Frutiger LT Arabic !important;
  font-weight: 400 !important;
  font-size: 12px !important;
  color: #284587 !important;
}

/* tbody,
td,
tfoot,
th,
thead,
tr {
  border-radius: 10px;
} */

.coordinateForm label.ant-checkbox-wrapper.ant-checkbox-rtl {
  font-weight: bold;
  /* background: #338c9a; */
  padding: 13px;
  color: #284587;
  border-radius: 10px;
  align-items: center;
  font-size: 12px;
  direction: ltr;
  margin: 0;
}

.coordinateForm .ant-checkbox-checked .ant-checkbox-inner {
  background: transparent !important;
  border: none !important;
}

.coordinateForm .ant-checkbox-inner {
  background: transparent !important;
  border: none;
}

.coordinates .select-cust_2 {
  display: grid !important;
  border: 1px solid #28458740 !important;
  /* padding: 4px !important; */
  border-radius: 15px !important;
  /* height: 56px; */
  /* padding-block: 0 !important; */
  padding: 6px;
  padding-bottom: 3px;
}

.select-cust_2 .ant-select-selector {
  background-color: transparent !important;
}

.select-cust_2 .ant-form-item-label {
  padding: 0 !important;
}

.select-cust_2 .ant-form-item-label label,
.select-cust_2 label {
  color: #28458799 !important;
  font-size: 10px !important;
}

.select-cust_2 input {
  padding: 0 11px !important;
  background: transparent !important;
  margin-top: -5px !important;
  border: none !important;
}

.select-cust_2 input::placeholder,
.ant-select-selection-placeholder {
  color: #284587 !important;
}

.select-cust_2 .ant-form-item-control-input {
  min-height: auto !important;
}

.coordResult_table {
  color: #284587;
  background: #fff;
  padding: 10px;
  border-radius: 16px;
  margin: 5px 0px;
  font-size: 12px;
}

.coordResult_table p {
  text-align: right;
}

label.ant-checkbox-wrapper.ant-checkbox-rtl.ant-checkbox-wrapper-checked {
  background: #338c9a;
  color: #fff;
}

.selectedRegion {
  font-weight: 400;
  font-size: 12px;
}

.interactive_map_span {
  font-family: Droid Arabic Kufi;
  font-weight: 400;
  font-size: 16px;
  color: #284587;
}

.interactive_drawing_label {
  font-family: Droid Arabic Kufi;
  font-weight: 400;
  font-size: 14px;
  color: #284587;
}

.interactive_drawing_label_2 {
  font-family: Droid Arabic Kufi;
  font-weight: 400;
  font-size: 12px;
  color: #284587;
}

.interActive_studyArea {
  display: flex;
  align-items: center;
  flex-direction: column;
  margin-top: 10px;
}

.interActive_studyArea .searchInput {
  height: 55px !important;
  padding-top: 6px !important;
  padding-right: 16px !important;
  padding-bottom: 25px !important;
  border-radius: 16px !important;
  border: 1px solid #28458740 !important;
  background: transparent !important;
}

.interActive_studyArea .searchInput::placeholder {
  font-weight: 400;
  font-size: 11px;
  color: #284587;
}

.interActive_studyArea .SearchBtn {
  margin-top: 10px !important;
  margin-bottom: 10px !important;
}

.map_area .ant-form-item-label {
  margin-bottom: -5px;
}

.select-cust_mapArea {
  margin-top: 0 !important;
  margin-bottom: 15px !important;
}

/* Remove default padding from header */
.shareModal .ant-modal-header {
  padding: 0;
}

.shareModal .ant-modal-body {
  max-height: 500px;
  overflow: auto;
}

.shareModal .ant-modal-body {
  padding: 0 24px 24px 24px !important;
}

/* Customize the title */
.shareModal .custom-modal-title {
  background-color: #ffffff;
  padding: 12px 16px;
  border-radius: 6px;
  color: #284587;
  font-size: 16px;
  font-weight: 400;
}

.label_Bookmark {
  font-family: Droid Arabic Kufi;
  font-weight: 400;
  font-size: 11px;
  color: #28458799;
}

.shareModal .ant-modal-content {
  border-radius: 24px !important;
  overflow: hidden !important;
  background: #ffffffd9;
}

.shareModal .ant-modal-body {
  padding: 0 24px 50px;
}

.sharmodalBookmark .ant-modal-body {
  padding: 0 15px 0px;
}

.shareModal .anticon {
  color: #284587;
  position: relative;
  top: -3px;
  width: 14px;
  height: 14px;
}

.checkbox_container {
  display: grid;
  border-radius: 10px;
  text-align: right;
  margin-top: 20px;
  border: 1px solid #bfbfbf;
  grid-template-columns: 1fr 1fr;
  justify-items: self-end;
}

.map_area .ant-form-item-label {
  margin-bottom: -5px;
}

.select-cust_mapArea {
  margin-top: 0 !important;
  margin-bottom: 15px !important;
}

.shareModal .toc-gallery-content_checkbox {
  margin: 0 !important;
  margin-left: 5px !important;
}

.shardContent {
  border-radius: 16px;
  border-width: 1px;
  padding: 8px;
  border: 1px solid #28458740;
  margin: 10px 0px 0;
}

.shareModal .SearchBtn {
  margin: 0 !important;
  margin-top: 10px !important;
}

/* Fix dropdown positioning in share modal to prevent covering search input */
.shareModal .ant-select-dropdown {
  position: absolute !important;
  z-index: 10000 !important;
  margin-top: 4px !important;
}

.shareModal .select-cust_mapArea .ant-select-dropdown {
  position: absolute !important;
  top: 110% !important;
  left: 0 !important;
  right: 0 !important;
  transform: none !important;
}

/* Ensure search input is not covered by dropdown */
.shareModal .ant-select-selection-search {
  z-index: 10001 !important;
}

.shareModal .ant-select-selector {
  z-index: 10001 !important;
}

.shared_selectedUsers {
  border-radius: 16px;
  padding: 8px;
  font-weight: 400;
  font-size: 12px;
  color: #ffffff;
}

.shared_selectedUsers_deleteIcon {
  color: #d9d9d9;
  width: 12px;
  height: 12px;
  cursor: pointer;
}

.rc-virtual-list-scrollbar-thumb {
  background-color: rgb(40, 69, 135) !important;
}

.label_size label.ant-form-item-label {
  font-size: 14px !important;
  margin: 9px;
  color: #284587 !important;
}

.filterModal {
  width: 55% !important;
}

.filterModal .ant-form-inline {
  display: block !important;
}

.filterModal .ant-modal-footer {
  display: flex;
  direction: ltr;
  grid-gap: 10px;
}

.filterModal .ant-btn-primary {
  background: #284587;
  border: none;
  color: #fff;
  transition: all 0.3s ease-in-out;
  border-radius: 10px;
}

.filterModal .ant-btn {
  border-radius: 6px;
}

.ant-steps-item-finish
  > .ant-steps-item-container
  > .ant-steps-item-tail::after {
  background-color: #338c9a !important;
}

.ant-steps:not(.ant-steps-vertical)
  .ant-steps-item-custom
  .ant-steps-item-icon {
  cursor: pointer;
}

.importFile_img {
  /* margin-left: 4px; */
}

.css-ja5taz-MuiTooltip-tooltip {
  border-radius: 16px !important;
  padding: 8px !important;
  background: #338c9a !important;
  font-family: Droid Arabic Kufi !important;
  font-weight: 400 !important;
  font-size: 12px !important;
  color: #fff !important;
}

.ant-table-filter-trigger {
  font-size: 17px !important;
}

.footer_modal {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 0 10px;
}

.footer_buttonEdit_modal,
.footer_buttonCancel_modal {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: #284587 !important;
  border-color: #284587 !important;
  border-radius: 16px !important;
  padding: 16px !important;
  /* height: fit-content !important; */
  height: 48px !important;
  width: 100% !important;
  font-family: Droid Arabic Kufi !important;
  font-weight: 700 !important;
  font-size: 16px !important;
  color: #fff !important;
}

.footer_buttonCancel_modal {
  background-color: #a8a8a8 !important;
  border-color: #a8a8a8 !important;
}

.coordinateForm label {
  color: #284587;
}

.rc-virtual-list {
  direction: rtl;
}

.ant-table-filter-dropdown button {
  font-size: 10px;
  min-width: 100px !important;
  height: 40px !important;
  border-radius: 70px;
  margin-bottom: 0 !important;
}

.ant-table-filter-dropdown .ant-select,
.ant-table-filter-dropdown .ant-input {
  padding: 5px !important;
  border-radius: 30px !important;
  border: 1px solid #284587 !important;
}

.ant-table-filter-dropdown .ant-input {
  height: 42px !important;
  padding: 10px !important;
}

.ant-table-filter-dropdown input {
  font-size: 10px;
}

.ant-table-filter-dropdown .ant-select-selection-placeholder {
  font-size: 10px;
}

.ant-table-filter-dropdown {
  /* width: 13vw; */
  text-align: center;
  border-radius: 20px !important;
}

.printPdf {
  width: 7% !important;
  background: #284587;
  color: #fff;
  padding: 10px;
  border-radius: 10px;
  border: 1px;
}

.thead_border thead {
  border: 1px solid#eee;
}

/* esri-legend__service */

#legendContainer {
  direction: rtl;
  width: 100%;
}

.esri-legend__service {
  padding: 0px 10px;
}

.esri-legend__layer {
  margin-left: 0;
}

#legendContainer .esri-legend__layer-row {
  flex-direction: row !important;
}

.nearbyIconIcon {
  font-size: 22px;
  color: #223973;
}

.nearbyIconIcon:hover {
  color: #284587;
}

.nestedLayerExpand {
  max-height: 200px;
  overflow-y: scroll;
  background: #f1f1f1;
  padding: 4px;
  border-radius: 20px;
  margin-bottom: 10px;
}

.ant-table-wrapper-rtl .ant-table-column-sorter {
  margin-left: 6px !important;
}

.filterModal .ant-btn-primary:hover,
.filterModal .ant-btn-primary:focus {
  box-shadow: inset 12em 0 #0a8eb9 !important;
  cursor: pointer;
  background: #0a8eb9 !important;
  color: #fff !important;
  transform: scale(1.1);
}

.noData_outerSearchResult {
  padding: 20px 0px;
  margin: 10px 5px;
  border-radius: 21px;
  /* box-shadow: rgba(0, 0, 0, 0.32) 0px 1px 4px 0px,
    rgba(0, 0, 0, 0.2) 0px 0px 2px 0px; */
  background: #fff;
}

.esri-distance-measurement-2d__measurement {
  padding: 20px;
  margin: 10px 2px 10px 10px;
  border-radius: 21px;
  /* box-shadow: rgba(0, 0, 0, 0.32) 0px 1px 4px 0px,
    rgba(0, 0, 0, 0.2) 0px 0px 2px 0px; */
  background: #fff;
}

.esri-area-measurement-2d__measurement {
  padding: 20px;
  margin: 10px 2px 10px 10px;
  border-radius: 21px;
  /* box-shadow: rgba(0, 0, 0, 0.32) 0px 1px 4px 0px,
    rgba(0, 0, 0, 0.2) 0px 0px 2px 0px; */
  background: #fff;
}

#leftSwipeLabel {
  position: absolute;
  z-index: 1000;
  color: #fff;
  background: #338c9a;
  width: 155px;
  left: 62vh;
  top: 48vh !important;
  border-radius: 25px;
  /* padding: 10px; */
  white-space: nowrap;
  text-align: center;
}

#rightSwipeLabel {
  position: absolute;
  z-index: 1000;
  color: #fff;
  background: #338c9a;
  width: 155px;
  left: 84vh;
  top: 48vh !important;
  border-radius: 25px;
  /* padding: 10px; */
  white-space: nowrap;
  text-align: center;
}
