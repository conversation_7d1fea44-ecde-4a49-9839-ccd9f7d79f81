import React, { useEffect, useRef, useState } from "react";
import { Container } from "react-bootstrap";
import OuterSearchResultDetails from "./OuterSearchResultDetails";
import OuterSearchResultsMenu from "./OuterSearchResultsMenu";
import debounce from "lodash.debounce";
export default function OuterSearchMainPage(props) {
  // const [landBaseParcelData, setLandBaseParcelData] = React.useState();
  const containerRef = useRef(null);
  const [visibleItems, setVisibleItems] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const outerOpenResultdetailsCallback = (item) => {
    props.map.__selectedItem = item;
    props.outerOpenResultdetails(item);
  };

  useEffect(() => {
    window.DisableActiveTool();
  }, []);

  useEffect(() => {
    if (
      Array.isArray(props.outerSearchResult) &&
      props.outerSearchResult?.length > 0
    ) {
      setVisibleItems(props.outerSearchResult.slice(0, 10)); // Show first 5 items
      setCurrentIndex(10);
    }
  }, [props.outerSearchResult]);

  const loadMoreItems = () => {
    if (currentIndex < props.outerSearchResult.length) {
      const nextItems = props.outerSearchResult.slice(
        currentIndex,
        currentIndex + 10
      );
      setVisibleItems((prevItems) => [...prevItems, ...nextItems]);
      setCurrentIndex(currentIndex + 10);
    }
  };

  // Scroll event handling
  const handleScroll = () => {
    const container = containerRef.current;
    if (container) {
      if (
        Math.abs(
          container.scrollHeight - container.scrollTop - container.clientHeight
        ) < 10
      ) {
        loadMoreItems();
      }
    }
  };

  const debouncedHandleScroll = debounce(handleScroll, 200);

  useEffect(() => {
    const container = containerRef.current;

    if (container) {
      container.addEventListener("scroll", debouncedHandleScroll);
    }

    return () => {
      if (container) {
        container.removeEventListener("scroll", debouncedHandleScroll);
      }
      debouncedHandleScroll.cancel();
    };
  }, [debouncedHandleScroll]);

  return (
    (!Array.isArray(props.outerSearchResult) &&
      props.outerSearchResult?.list &&
      props.outerSearchResult.statisticsInfo && (
        <div className="coordinates mb-4 mt-4">
          <Container>
            {props.outerResultMenuShown ? (
              <OuterSearchResultsMenu
                mainData={props.mainData}
                map={props.map}
                outerResultMenuShown={props.outerResultMenuShown}
                outerSearchResult={props.outerSearchResult}
                outerOpenSearchInputs={props.outerOpenSearchInputs}
                outerOpenResultdetails={outerOpenResultdetailsCallback}
                landBaseParcelData={
                  props.resultDetailsDataRef.current?.landBaseParcelData
                }
                // setLandBaseParcelData={props.setLandBaseParcelData}
              />
            ) : props.outerResultDetailsShown ? (
              <OuterSearchResultDetails
                mainData={props.mainData}
                outerOpenSearchInputs={props.outerOpenSearchInputs}
                outerOpenResultMenu={props.outerOpenResultMenu}
                data={
                  props.outerSearchResult.length > 1
                    ? props.map.__selectedItem
                    : props.outerSearchResult[0]
                }
                map={props.map}
                isPrivateLandOrRoyalLand={[
                  "PARCEL_PRIVACY",
                  "LGR_ROYAL",
                  "SALES_LANDS",
                ].includes(props.outerSearchResult?.layerName)}
                resultDetailsDataRef={props.resultDetailsDataRef}
                // setLandBaseParcelData={props.setLandBaseParcelData}
              />
            ) : null}
          </Container>
        </div>
      )) || (
      <div className="coordinates mb-4 mt-4" ref={containerRef}>
        <Container>
          {props.outerResultMenuShown ? (
            <OuterSearchResultsMenu
              mainData={props.mainData}
              map={props.map}
              outerResultMenuShown={props.outerResultMenuShown}
              outerSearchResult={visibleItems}
              outerOpenSearchInputs={props.outerOpenSearchInputs}
              outerOpenResultdetails={outerOpenResultdetailsCallback}
              landBaseParcelData={
                props.resultDetailsDataRef.current?.landBaseParcelData
              }
              // setLandBaseParcelData={props.setLandBaseParcelData}
            />
          ) : props.outerResultDetailsShown ? (
            <OuterSearchResultDetails
              mainData={props.mainData}
              outerOpenSearchInputs={props.outerOpenSearchInputs}
              outerOpenResultMenu={props.outerOpenResultMenu}
              data={
                props.outerSearchResult.length > 1
                  ? props.map.__selectedItem
                  : props.outerSearchResult[0]
              }
              map={props.map}
              isPrivateLandOrRoyalLand={[
                "PARCEL_PRIVACY",
                "LGR_ROYAL",
                "SALES_LANDS",
              ].includes(props.outerSearchResult?.layerName)}
              resultDetailsDataRef={props.resultDetailsDataRef}
              // setLandBaseParcelData={props.setLandBaseParcelData}
            />
          ) : null}
        </Container>
      </div>
    )
  );
}
