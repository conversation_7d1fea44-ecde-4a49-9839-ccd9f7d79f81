import React, { useEffect } from "react";
import Fade from "react-reveal/Fade";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes } from "@fortawesome/free-solid-svg-icons";
import TocComponent from "./TocComponent";

export default function LayersMenu(props) {
  // useEffect(() => {
  //   window.handles.remove("map-events");
  // }, []);
  return (
    <Fade left collapse>
      <div
        className="layersMenu"
        style={{ top: "0%", position: "absolute", left: "112%" }}
      >
        {/* <span
          style={{
            width: "100%",
            float: "left",
            textAlign: "left",
          }}
        >
          {" "}
          <FontAwesomeIcon
            className="close_icon"
            icon={faTimes}
            onClick={props.closeToolsData}
          />
        </span> */}
        <TocComponent
          languageState={props.languageState}
          mainData={props.mainData}
          map={props.map}
          closeToolsData={props.closeToolsData}
        />
      </div>
    </Fade>
  );
}
