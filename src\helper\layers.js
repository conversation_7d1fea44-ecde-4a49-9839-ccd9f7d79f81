import {
  faBuilding,
  faCartPlus,
  faChartPie,
  faFilePdf,
  faSitemap,
  faUser,
  faFileInvoice,
} from "@fortawesome/free-solid-svg-icons";
import kroky from "../assets/images/kroky.svg";
import mshare3Icon from "../assets/images/mshare3.svg";
import storeLicense from "../assets/icons/inquiry-tool/رخص المحلات.svg";
import sa7efa from "../assets/icons/inquiry-tool/الصحيفة العقارية.svg";
import archieve from "../assets/icons/inquiry-tool/رخص البناء.svg";
import buildingConditions from "../assets/icons/inquiry-tool/إشتراطات البناء.svg";
import splitIcon from "../assets/images/splitIcon.svg";
import updateContract from "../assets/images/updateContracts.svg";
import zwa2dTnzemya from "../assets/images/zwa2dIcon.svg";
import privateLandIcon from "../assets/images/privateLandIcon2.svg";

import results_icon from "../assets/icons/inquiry-tool/النتائج.svg";

export const loginLayersSetting = {
  incidents940: {
    name: "البلاغات",
    isPublicSearchable: true, // it is for general search
    displayField: "LOCATION_ID",
    isSearchable: true, // it is for search within buffer
    // the outfields of layer --> OBJECTID is mandatory
    outFields: [
      "OBJECTID",
      "MUNICIPALITY_NAME",
      "SUB_MUNICIPALITY_NAME",
      "LOCATION_NAME",
      "LOCATION_ID",
      "DISTRICT_NAME",
      "STREET_FULLNAME",
      "INCIDENT_STATUS",
      "MAINCLASSIFICATIONNAME",
      "SUBCLASSIFICATIONNAME",
      "SUBSUBCLASSIFICATIONNAME",
      "TICKET_NUMBER",
      "CREATED_DATE",
      "CLOSED_DATE",
      "LATITUDE",
      "LONGITUDE",
      "PRIORITYZONENAME",
    ],
    // this is the key of translations of out fields --> see locales/layers.json files [ar, en]
    aliasOutFields: [
      "munName",
      "subMunName",
      "locationName",
      "reportNum",
      "districtName",
      "streetName",
      "reportStatus",
      "MAINCLASSIFICATIONID",
      "SUBCLASSIFICATIONID",
      "SUBSUBCLASSIFICATIONID",
      "orderNum",
      "dateCreated",
      "closeDate",
      "latitude",
      "longitude",
      "priorityzZomeName",
    ],
    searchFields: [
      // it is for search form in general search
      {
        field: "MUNICIPALITY_NAME",
        alias: "munName",
        zoomLayer: {
          name: "Municipality_Boundary",
          filterField: "MUNICIPALITY_NAME",
        },
      },
      {
        field: "SUB_MUNICIPALITY_NAME",
        alias: "subMunName",
        zoomLayer: {
          // this is for zoom to layer
          name: "Sub_Municipality_Boundary",
          filterField: "SUB_MUNICIPALITY_NAME",
        },
      },
      {
        field: "DISTRICT_NAME",
        alias: "districtName",
        zoomLayer: { name: "District_Boundary", filterField: "DISTRICT_NAME" },
      },
      {
        field: "PRIORITYZONENAME",
        alias: "priorityzZomeName",
      },
      {
        field: "STREET_FULLNAME",
        alias: "streetName",
        zoomLayer: { name: "Street_Naming", filterField: "STREET_FULLNAME" },
      },
      { field: "INCIDENT_STATUS", alias: "reportStatus" },
      { field: "MAINCLASSIFICATIONNAME", alias: "MAINCLASSIFICATIONID" },
      { field: "SUBCLASSIFICATIONNAME", alias: "SUBCLASSIFICATIONID" },
      { field: "SUBSUBCLASSIFICATIONNAME", alias: "SUBSUBCLASSIFICATIONID" },
      { field: "LOCATION_ID", alias: "reportNum", isServerSideSearch: true },
      { field: "CREATED_DATE", alias: "from", isDate: true, operator: ">=" },
      {
        field: "CLOSED_DATE",
        alias: "to",
        isDate: true,
        operator: "<=",
        isEndDate: true,
      },
    ],
    restrictionWhereClause: " INCIDENT_STATUS <> 1",
    // this is a dashboard configration to render chart data
    dashboardCharts: [
      {
        name: "MAINCLASSIFICATIONNAME",
        alias: "mostIncidentClassificationType",
        chartType: "bar",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
        maxShownItems: 3,
        restrictionWhereClause: " INCIDENT_STATUS <> 1",
      },

      {
        name: "LICENSETYPENAME",
        dependentLayer: "construction_license",
        alias: "countsructionLic",
        chartType: "pie",
        position: "top",
        filterDateField: "LICENSEISSUEDATEH",
        shownData: ["count"],
        maxShownItems: 3,
      },
      {
        name: "MUNICIPALITY_NAME",
        alias: "incidentNuPermuniciplityName",
        chartType: "col",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
        maxShownItems: 3,
        restrictionWhereClause: " INCIDENT_STATUS <> 1",
        relatedChildChart: {
          name: "MAINCLASSIFICATIONNAME",
          alias: "moreWidelyIncidentsPerAdmin",
          chartType: "col",
          position: "top",
          filterDateField: "CREATED_DATE",
          shownData: ["count"],
          maxShownItems: 3,
          restrictionWhereClause: " INCIDENT_STATUS <> 1",
        },
      },
      {
        name: "MUNICIPALITY_NAME",
        dependentLayer: "digging_license",
        alias: "diggingLicense",
        chartType: "text",
        position: "bottom",
        filterDateField: "START_LICENSE_DATE",
        shownData: ["count"],
        restrictionDateWhereClause: "CLOSED_DATE> Date ",
      },
    ],
  },
  Deformation_Sites: {
    isSearchable: true,
    name: "التشوه البصري",
    isHidden: true, // this is for hiding this layer from map service into map
    outFields: [
      "OBJECTID",
      "MUNICIPALITY_NAME",
      "SUB_MUNICIPALITY_NAME",
      "CITY_NAME",
      "DISTRICT_NAME",
      "REMARKS",
      "DEFORMATION_TYPE",
      "STREET_NAME",
    ],
    aliasOutFields: [
      "munName",
      "subMunName",
      "cityName",
      "districtName",
      "notes",
      "opticalDistortionType",
      "streetName",
    ],
    dashboardCharts: [
      {
        name: "MUNICIPALITY_NAME",
        alias: "deformationNuPermuniciplityName",
        chartType: "donut",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },

      {
        name: "DEFORMATION_TYPE",
        alias: "deformationType",
        chartType: "pie",
        position: "bottom",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
    ],
  },
  gis_eppms_section_vw: {
    name: "حالة الطرق",
    isHidden: true,
    isSearchable: true,
    outFields: [
      "OBJECTID",
      "AREA_NO",
      "AREA_NAME",
      "ROAD_NO",
      "CITY_NO",
      "ZONE_NO",
      "ROAD_NAME",
      "SECTION_NO",
      "SECTION_RATING",
      "MAINTENANCE_COST",
      "MAINTENANCE_PRIORITY",
      "SECTION_PCI",
      "DISTRESS_CONDITION_AFTER",
      "PREDICTED_DISTRESS_TYPE",
      "PREDICTED_PCI",
    ],
    aliasOutFields: [
      "districtNum",
      "districtName",
      "streetNum",
      "cityNum",
      "bandNum",
      "streetName",
      "sectionNum",
      "sectionevl",
      "CostOfMaintenance",
      "maintenancePriority",
      "pavementConditionIndicator",
      "damageTypeCode",
      "suggestedRepairType",
      "proposedPavementConditionIndicator",
    ],
    dashboardCharts: [],
  },
  transport_track: {
    isSearchable: true,
    name: "مسارات النقل",
    outFields: ["OBJECTID", "NAME"],
    aliasOutFields: ["pathName"],
    isHidden: true,
    dashboardCharts: [],
  },

  camera_locations: {
    name: "الكاميرات",
    outFields: ["OBJECTID", "LOCATION"],
    aliasOutFields: ["cameraLocation"],
    isHidden: true,
    dashboardCharts: [],
  },
  transport_stations: {
    name: "محطات النقل",
    outFields: [
      "OBJECTID",
      "MUN_NAME",
      "DISTRICT_NAME",
      "STATION_NO",
      "STREET_NAME",
      "STATION_AREA",
      "STATION_PATH",
    ],
    aliasOutFields: [
      "munName",
      "districtName",
      "stationNum",
      "streetName",
      "stationArea",
      "path",
    ],
    isHidden: true,
    isSearchable: true,
    dashboardCharts: [],
  },

  Tarfeeh_Data: {
    name: "ترفيه",
    outFields: [
      "OBJECTID",
      "MUNICIPALITY_NAME",
      "LOCATION_NAME",
      "EVENT_EXECUTIVE_NAME",
      "EVENT_NAME",
      "PROGRAM_TYPE",
      "PROGRAM_CATOGRIES",
      "EVENT_NO_DAY",
      "EVENT_FROM_DATE",
      "EVENT_TO_DATE",
      "SUGGESTED_LOCATION",
    ],
    aliasOutFields: [
      "munName",
      "locationName",
      "TheExecutingAgency",
      "events",
      "natureOfProgram",
      "categoryOfProgram",
      "daysNum",
      "eventStartDate",
      "eventFinishDate",
      "sggestedPlace",
    ],
    isHidden: true,
    isSearchable: true,
    dashboardCharts: [
      {
        name: "MUNICIPALITY_NAME",
        alias: "eventsNoPermuniciplityName",
        chartType: "pie",
        position: "bottom",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },

      {
        name: "STATUS_OF_FUNDING",
        alias: "eventsStatusFund",
        chartType: "bar",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "CONTRACT_STATUS",
        alias: "contractStatus",
        chartType: "col",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
    ],
  },
  digging_license: {
    name: "رخص الحفريات",
    isPublicSearchable: true,
    displayField: "CO_NAME_AR",
    isSearchable: true,
    isHidden: false,
    permission: function () {
      if (localStorage.user) {
        return localStorage.user.groups.find(function (x) {
          return x.id == 3031;
        });
      } else {
        return true;
      }
    },
    outFields: [
      "OBJECTID",
      "MUNICIPALITY_NAME",
      "SUB_MUNICIPALITY_NAME",
      "DISTRICT_NAME",
      "STREET_NAME",
      "BILLNU",
      "BILLCOAST",
      "START_LICENSE_DATE",
      "CR_NUMBER",
      "CO_NAME_AR",
      "EXPR1",
      "REQUEST_DATE",
      "TOTEXCLENGH",
      "PERIOD",
    ],
    aliasOutFields: [
      "munName",
      "subMunName",
      "districtName",
      "streetName",
      "billNum",
      "billValue",
      "drillingliceStartDate",
      "contractorNum",
      "contractorName",
      "superAuthName",
      "orderDate",
      "drillingLength",
      "drillingTime",
    ],
    searchFields: [
      {
        field: "MUNICIPALITY_NAME",
        alias: "munName",

        zoomLayer: {
          name: "Municipality_Boundary",
          filterField: "MUNICIPALITY_NAME",
        },
      },
      {
        field: "SUB_MUNICIPALITY_NAME",
        alias: "subMunName",
        zoomLayer: {
          name: "Sub_Municipality_Boundary",
          filterField: "SUB_MUNICIPALITY_NAME",
        },
      },
      {
        field: "DISTRICT_NAME",
        alias: "districtName",
        zoomLayer: { name: "District_Boundary", filterField: "DISTRICT_NAME" },
      },
      { field: "CO_NAME_AR", alias: "contractorName" },
      { field: "EXPR1", alias: "superAuthName" },
      { field: "CREATED_DATE", alias: "from", isDate: true, operator: ">=" },
      {
        field: "CLOSED_DATE",
        alias: "to",
        isDate: true,
        operator: "<=",
        isEndDate: true,
      },
    ],
    dashboardCharts: [],
  },
  PavementSections: {
    name: "مضلعات الطرق - الأسفلت",
    isSearchable: true,
    isHidden: true,
    outFields: [
      "OBJECTID",
      "AREA_NAME",
      "AREA_NO",
      "SECTION_CO",
      "SECTION_ID",
      "ROADNO",
      "SECTIONID",
      "REMARKS",
      "SHAPE_LENG",
      "SECTION_NO",
      "SECTION_PC",
      "MAINTENANC",
      "MAINTENA_1",
      "DISTRESS_S",
      "DISTRESS_C",
      "DISTRESS_1",
      "SPECIAL_TR",
      "SPECIAL__1",
      "SPECIAL__2",
      "SECTION_RA",
      "RIDING_QUA",
      "SAFETY_CON",
      "DISTRESS_O",
      "CONSTRUCTI",
      "SURVEY_DAT",
      "ROAD_CLASS",
    ],
    aliasOutFields: [
      "areaName",
      "areaNum",
      "sectorsNum",
      "SectorIdNum",
      "roadNum",
      "SectorIdNum",
      "digramNotes",
      "Perimeter",
      "sectorNumer",
      "sectorAccounts",
      "maintenance",
      "maintenance1",
      "S_strait",
      "C_strait",
      "strait1",
      "specialTR",
      "special1",
      "special2",
      "sectorAR",
      "installQUA",
      "protectionNum",
      "O_strait",
      "dateCreated",
      "scanDate",
      "roadClassification",
    ],
    dashboardCharts: [],
  },

  Sidewalk: {
    isHidden: true,
    isSearchable: true,
    name: "أرصفة و جزر وسطية",
    outFields: [
      "OBJECTID",
      "AREA_NAME",
      "AREA_NO",
      "SECTION_CO",
      "SECTION_ID",
      "ROADNO",
      "SECTIONID",
      "REMARKS",
      "SHAPE_LENG",
      "SECTION_NO",
      "SECTION_PC",
      "MAINTENANC",
      "MAINTENA_1",
      "DISTRESS_S",
      "DISTRESS_C",
      "DISTRESS_1",
      "SPECIAL_TR",
      "SPECIAL__1",
      "SPECIAL__2",
      "SECTION_RA",
      "RIDING_QUA",
      "SAFETY_CON",
      "DISTRESS_O",
      "CONSTRUCTI",
      "SURVEY_DAT",
      "ROAD_CLASS",
    ],
    aliasOutFields: [
      "areaName",
      "areaNum",
      "sectorsNum",
      "SectorIdNum",
      "roadNum",
      "SectorIdNum",
      "digramNotes",
      "Perimeter",
      "sectorNumer",
      "sectorNumer",
      "sectorAccounts",
      "maintenance",
      "maintenance1",
      "S_strait",
      "C_strait",
      "strait1",
      "specialTR",
      "special1",
      "special2",
      "sectorAR",
      "installQUA",
      "protectionNum",
      "O_strait",
      "dateCreated",
      "scanDate",
      "roadClassification",
    ],
    dashboardCharts: [],
  },

  District_UrbanElements: {
    name: "أولويات التنمية - العناصر العمرانية علي مستوي الأحياء",
    isHidden: true,
    outFields: [
      "OBJECTID",
      "SUB_MUNICIPALITY_NAME",
      "DISTRICT_NAME",
      "REMARKS",
      "MUNICIPALITY_NAME",
      "POPULATION1437H",
      "BUILDING1437H",
      "PLANNED",
      "SPACE_UR",
      "CROPS",
      "RUGGED",
      "SERVICES",
      "INDUSTRY",
      "PARCEL_WA",
      "SPECIAL",
      "TOTAL_SPACE",
      "CURRENT_DENSITY",
      "DENSE_TYPE",
      "BUILDING_RATE",
      "STATE_BUILDINGS",
      "STATUS_PLANNING",
      "QUALITY_DEVE",
      "DIMENSION_CITY_CENTER",
      "TOPOGRAPHY",
      "URBAN_DEV_PRIO",
      "PRIM_CENTERNUM",
      "BENEF_NUM",
      "NOTIC",
      "PARCELBUILT_AREA",
      "PARCELBUILT_COVERED",
      "PARCELBUILT_NOTCOVERED",
      "PARCELPLAN_AREA",
      "PARCELPLAN_COVERED",
      "PARCELPLAN_NOTCOVERED",
      "PARCELSPACE_AREA",
      "PARCELSPACE_COVERED",
      "PARCELSPACE_NOTCOVERED",
      "PARCELBUILTR_AREA",
      "PARCELBUILTR_COVERED",
      "PARCELBUILTR_NOTCOVERED",
      "PARCELPLANR_AREA",
      "PARCELPLANR_COVERED",
      "PARCELPLANR_NOTCOVERED",
      "PARCELSPACER_AREA",
      "PARCELSPACER_COVERED",
      "PARCELSPACER_NOTCOVERED",
      "PARCELBUILTELEC_AREA",
      "PARCELBUILTELEC_COVERED",
      "PARCELBUILTELEC_NOTCOVERED",
      "PARCELPLANELEC_AREA",
      "PARCELPLANELEC_COVERED",
      "PARCELPLANELEC_NOTCOVERED",
      "PARCELSPACEELEC_AREA",
      "PARCELSPACEELEC_COVERED",
    ],
    aliasOutFields: [
      "whiteLndsUncElec",
      "elecresUse2015",
      "elecresUse2016",
      "buildUpLandArea",
      "buildUpLandCover",
      "buildUpLandNotCover",
      "planLandArea",
      "planLandCovered",
      "planLandUnCover",
      "whiteLandArea",
      "whiteLandCover",
      "whiteLandUnCover",
      "servNameGirlTutorial",
      "servLevelGirlEduc",
      "classNumGirls",
      "studentNumGirls",
      "buildOwnerShipGirls",
      "servNameTutorialBoys",
      "servLevelEducBoys",
      "classNumBoys",
      "studentsNumBoys",
      "buildOwnerShipBoys",
    ],
    dashboardCharts: [],
  },

  Building_Gov_Loc: {
    name: "مواقع صيانة المباني الحكومية",
    isSearchable: false,
    outFields: [
      "OBJECTID",
      "BUILD_NAME",
      "TYPE_BUILD",
      "TYPE_PRO",
      "OWNER_NAME",
      "No_Floors",
      "MUNICIPALITY_NAME",
    ],
    aliasOutFields: [
      "buildName",
      "buildType",
      "ownerShipType",
      "ownerName",
      "floorsNum",
      "munName",
    ],
    dashboardCharts: [],
  },

  CriticalSites: {
    name: "المواقع الحرجة -ادارة الكوارث",
    isSearchable: false,
    outFields: [
      "OBJECTID",
      "MUNICIPALITY_NAME",
      "DISTRICT_NAME",
      "STREET_NAME",
      "NAME_SUPER_AMANA",
      "MOBILE_NO",
      "CONTR_TEAM",
      "EQUIPMENT",
      "EMP_NO",
      "DESC_SITE",
    ],
    aliasOutFields: [
      "buildName",
      "Region",
      "street",
      "amanaSuperVName",
      "mobileNum",
      "constTeamSuper",
      "neccEquip",
      "laborNeedNum",
      "critSiteDesc",
    ],
    dashboardCharts: [],
  },

  Bridges_tunnels_Sites: {
    name: "مواقع الجسور والانفاق - الادارة العامة للاشراف",
    isSearchable: false,
    outFields: [
      "OBJECTID",
      "PROJECT_NAME",
      "COMP_RAT",
      "PRIM_DATENEW",
      "PRIM_DATE_MUN",
      "PRIM_DATE_EXC",
      "FINAL_DATENEW",
      "FINAL_DATE_MUN",
      "FINAL_DATE_EXC",
      "DEV_WEBSITE_DATE_MUN",
      "DEV_WEBSITE_DATE_EXC",
    ],
    aliasOutFields: [
      "projectName",
      "completeRate",
      "primaryHistory",
      "primaryHistryReform",
      "initExecDate",
      "constTeamSuper",
      "finalDate",
      "finalDateRepar",
      "execDeadline",
      "repairSiteDelDate",
      "SiteExecDate",
    ],
    dashboardCharts: [],
  },

  Tbl_SHOP_LIC_MATCHED: {
    isSearchable: false,
    name: "رخص المحلات",
    outFields: [
      "OBJECTID",
      "PARCEL_SPATIAL_ID",
      "S_PLAN_NO1",
      "S_LAND_NO1",
      "S_SHOP_LIC_NO",
      "S_SHOP_YEAR",
      "SHOP_NAME",
    ],
    aliasOutFields: [
      "planNum",
      "landNum",
      "storeLicenseNum",
      "storeLicenseDate",
      "storeName",
    ],
    dependecies: [
      {
        name: "Landbase_Parcel",
        icon: faSitemap,
        filter: "PLAN_SPATIAL_ID",
        filterDataType: "esriFieldTypeDouble",
        tooltip: "SurveyingPlotsData",
      },
    ],
    filter: "PARCEL_SPATIAL_ID",
    dashboardCharts: [],
  },

  Landbase_ParcelLic: {
    name: "رخص الأراضي",

    outFields: [
      "OBJECTID",
      "MUNICIPALITY_NAME",
      "SUB_MUNICIPALITY_NAME",
      "DISTRICT_NAME",
      "CITY_NAME",
      "LIC_NO",
      "LIC_YEAR",
      "PARCEL_PLAN_NO",
      "PLAN_NO",
    ],
    aliasOutFields: [
      "munName",
      "subMunName",
      "districtName",
      "cityName",
      "licenseNum",
      "licenseYear",
      "landNumONPlan",
      "planNum",
    ],
    dashboardCharts: [
      {
        name: "MUNICIPALITY_NAME",
        alias: "licenseNuPermuniciplityName",
        chartType: "donut",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "ACTUAL_MAINLANDUSE",
        alias: "actualMainLUse",
        chartType: "donut",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "PARCEL_INVESTED",
        alias: "parcelIsInvested",
        chartType: "col",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count", "area"],
      },
      {
        name: "USING_SYMBOL",
        alias: "usingSymbol",
        chartType: "pie",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "PARCEL_PROPERTY",
        alias: "parcelProperty",
        chartType: "bar",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "PARCEL_DVELOPED",
        alias: "parcelIsDeveloped",
        chartType: "bar",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "PARCEL_MAIN_LUSE",
        alias: "parcelMainLuse",
        chartType: "bar",
        position: "bottom",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
    ],
  },
  Gas_StationsEP: {
    name: "محطات البنزين المنطقة الشرقية  للطرق الرئيسية",
    isSearchable: false,
    outFields: [
      "OBJECTID",
      "STATION_NAME",
      "AMANA",
      "GOV_NAME",
      "MUN_NAME",
      "CLASS_STATION",
      "ROAD_NAME",
      "ROAD_NO",
      "X_COORDINATE",
      "Y_COORDINATE",
      "CONS_BENZ95",
      "PUMPS_BENZ95",
      "CONS_BENZ91",
      "PUMPS_BENZ91",
      "DIESEL_CONS",
      "PUMPS_DIESEL",
      "CONS_KEROSENE",
      "PUMPS_KEROSENE",
    ],
    aliasOutFields: [
      "stationName",
      "amana",
      "govName",
      "munName",
      "stationClassification",
      "roadName",
      "roadNum",
      "northCoord",
      "eastCoord",
      "95Petrolconsumption",
      "95PetrolPumps",
      "91Petrolconsumption",
      "91PetrolPumps",
      "dieselConsumption",
      "dieselPumps",
      "keroseneConsumption",
      "kerosenePumps",
    ],
    dashboardCharts: [
      {
        name: "GOV_NAME",
        alias: "gasStationNoPerGovName",
        chartType: "donut",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },

      {
        name: "MUN_NAME",
        alias: "gasStationNoPerMunName",
        chartType: "pie",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },

      {
        name: "CLASS_STATION",
        alias: "stationClasses",
        chartType: "col",
        position: "bottom",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
    ],
  },
  Gas_Stations_INT: {
    isSearchable: false,
    name: "محطات بنزين داخل الحاضرة للطرق الرئيسية",
    isHidden: true,
    outFields: [
      "OBJECTID",
      "STATION_NAME",
      "AMANA",
      "GOV_NAME",
      "MUN_NAME",
      "CLASS_STATION",
      "ROAD_NAME",
      "ROAD_NO",
      "X_COORDINATE",
      "Y_COORDINATE",
      "CONS_BENZ95",
      "PUMPS_BENZ95",
      "CONS_BENZ91",
      "PUMPS_BENZ91",
      "DIESEL_CONS",
      "PUMPS_DIESEL",
      "CONS_KEROSENE",
      "PUMPS_KEROSENE",
    ],
    aliasOutFields: [
      "stationName",
      "amana",
      "govName",
      "munName",
      "stationClassification",
      "roadName",
      "roadNum",
      "northCoord",
      "eastCoord",
      "95Petrolconsumption",
      "95PetrolPumps",
      "91Petrolconsumption",
      "91PetrolPumps",
      "dieselConsumption",
      "dieselPumps",
      "keroseneConsumption",
      "kerosenePumps",
    ],
    dashboardCharts: [
      {
        name: "GOV_NAME",
        alias: "gasStationNoPerGovName",
        chartType: "donut",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },

      {
        name: "MUN_NAME",
        alias: "gasStationNoPerMunName",
        chartType: "pie",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },

      {
        name: "CLASS_STATION",
        alias: "stationClasses",
        chartType: "col",
        position: "bottom",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
    ],
  },
  Gas_Stations_OutEP: {
    isSearchable: true,

    name: "محطات بنزين خارج الحاضرة للطرق الرئيسية",
    isHidden: true,
    outFields: [
      "OBJECTID",
      "STATION_NAME",
      "AMANA",
      "GOV_NAME",
      "MUN_NAME",
      "CLASS_STATION",
      "ROAD_NAME",
      "ROAD_NO",
      "X_COORDINATE",
      "Y_COORDINATE",
      "CONS_BENZ95",
      "PUMPS_BENZ95",
      "CONS_BENZ91",
      "PUMPS_BENZ91",
      "DIESEL_CONS",
      "PUMPS_DIESEL",
      "CONS_KEROSENE",
      "PUMPS_KEROSENE",
    ],
    aliasOutFields: [
      "stationName",
      "amana",
      "govName",
      "munName",
      "stationClassification",

      "roadName",

      "roadNum",

      "northCoord",
      "eastCoord",
      "95Petrolconsumption",
      "95PetrolPumps",
      "91Petrolconsumption",
      "91PetrolPumps",
      "dieselConsumption",
      "dieselPumps",
      "keroseneConsumption",
      "kerosenePumps",
    ],
    dashboardCharts: [
      {
        name: "GOV_NAME",
        alias: "gasStationNoPerGovName",
        chartType: "donut",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },

      {
        name: "MUN_NAME",
        alias: "gasStationNoPerMunName",
        chartType: "pie",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },

      {
        name: "CLASS_STATION",
        alias: "stationClasses",
        chartType: "col",
        position: "bottom",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
    ],
  },
  Gas_Stations_Satellites: {
    name: "محطات بنزين مضافة من جوجل المنطقة الشرقية للطرق الرئيسية",
    isSearchable: false,

    outFields: [
      "OBJECTID",
      "STATION_NAME",
      "AMANA",
      "GOV_NAME",
      "MUN_NAME",
      "CLASS_STATION",
      "ROAD_NAME",
      "ROAD_NO",
      "X_COORDINATE",
      "Y_COORDINATE",
      "CONS_BENZ95",
      "PUMPS_BENZ95",
      "CONS_BENZ91",
      "PUMPS_BENZ91",
      "DIESEL_CONS",
      "PUMPS_DIESEL",
      "CONS_KEROSENE",
      "PUMPS_KEROSENE",
    ],
    aliasOutFields: [
      "stationName",
      "amana",
      "govName",
      "munName",
      "stationClassification",

      "roadName",

      "roadNum",
      "northCoord",
      "eastCoord",
      "95Petrolconsumption",
      "95PetrolPumps",
      "91Petrolconsumption",
      "91PetrolPumps",
      "dieselConsumption",
      "dieselPumps",
      "keroseneConsumption",
      "kerosenePumps",
    ],
    dashboardCharts: [
      {
        name: "GOV_NAME",
        alias: "gasStationNoPerGovName",
        chartType: "donut",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },

      {
        name: "MUN_NAME",
        alias: "gasStationNoPerMunName",
        chartType: "pie",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },

      {
        name: "CLASS_STATION",
        alias: "stationClasses",
        chartType: "col",
        position: "bottom",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
    ],
  },
  Province_Boundary: {
    name: "حدود الأمانة",
    isSearchable: true,
    isPublicSearchable: true,
    outFields: ["OBJECTID", "PROVINCE_NAME", "PROVINCE_ENGNAME"],
    aliasOutFields: ["amanaName", "engAmanaName"],
    dashboardCharts: [],
  },

  PolesGisView: {
    name: "أعمدة الإنارة",

    outFields: [
      "OBJECTID",
      "POLE_NO",
      "PANEL_NO",
      "CITY_NAME",
      "NEIGHBORHO",
      "STREET",
      "ADDRESS",
    ],
    aliasOutFields: [
      "lightPoleNum",
      "plateNum",
      "cityName",
      "districtName",
      "streetName",
      "address",
    ],
    dashboardCharts: [],
  },
  STATION_SITE: {
    name: "التعداد المروري",
    isSearchable: true,
    outFields: [
      "OBJECTID",
      "STATION_NO",
      "STREET_NAM",
      "CITY_SUBRE",
      "SUBMUNICIB",
    ],
    aliasOutFields: ["counterNum", "streetName", "cityName", "subMunName"],
    dashboardCharts: [],
  },
  PanelsGisView: {
    isSearchable: true,
    name: "لوحات الإنارة",
    outFields: [
      "OBJECTID",
      "PANELNO",
      "CITY_NAME",
      "NEIGHBORHO",
      "STREET",
      "TRANSFORME",
      "CONTRACTOR",
      "POLES",
    ],
    aliasOutFields: [
      "plateNum",
      "cityName",
      "districtName",
      "streetName",
      "transformer",
      "contractorName",
      "ColumnNum",
    ],
    dashboardCharts: [],
  },
  Project_Data: {
    isSearchable: true,
    name: "بيانات المشاريع",
    outFields: [
      "OBJECTID",
      "PROJECT_NAME",
      "DEPARTMENT",
      "CONSULTANT",
      "CONTRACTOR",
      "STATUS",
      "CITY_NAME",
      "SUB_MUNICIPALITY_NAME",
    ],
    aliasOutFields: [
      "projectName",
      "Administration",
      "Consultant",
      "contractor",
      "projectStatus",
      "cityName",
      "subMunName",
    ],
    dashboardCharts: [],
  },
  AnnualFivYear_Plan: {
    isSearchable: true,
    name: "الخطة السنويه الخمسية",
    isUpdate: true,
    outFields: [
      "OBJECTID",
      "MAIN_SIDE_NAME",
      "PROJECT_NAME",
      "CONTRACT_NAME",
      "CONTRACT_START_DATE",
      "CONTRACT_END_DATE",
      "MUNICIPALITY_NAME",
      "NEIGHBORHOOD_NAME",
    ],
    aliasOutFields: [
      "mainSideName",
      "projectName",
      "contractName",
      "suggStartDate",
      "suggFinishDate",
      "munName",
      "districtName",
    ],
    dashboardCharts: [],
  },

  Invest_Site_Polygon: {
    isSearchable: true,
    name: "الأراضي الإستثمارية",
    outFields: [
      "OBJECTID",
      "MUNICIPALITY_NAME",
      "DISTRICT_NAME",
      "PLAN_NO",
      "PARCEL_PLAN_NO",
      "STREET_NAME",
      "SITE_AREA",
      "SITE_ACTIVITY",
      "SITE_STATUS",
      "SITE_SUBTYPE",
      "CONTRACT_NUMBER",
      "PROJECT_NO",
      "SITE_LAT_COORD",
      "SITE_LONG_COORD",
      "USING_SYMBOL",
      "BLDG_CONDATIONS",
    ],
    aliasOutFields: [
      "munName",
      "districtName",
      "planNum",
      "landNumONPlan",
      "streetName",
      "areaM2",
      "propInvctivity",
      "locationstaus",
      "invSiteType",
      "contractNum",
      "bidNum",
      "latitudeEngCener",
      "longitudeEngCenter",
      "useCode",
      "buildConditionDesc",
    ],
    dashboardCharts: [
      {
        name: "SITE_STATUS",
        alias: "siteStatus",
        chartType: "bar",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "SITE_SUBTYPE",
        alias: "siteSubtype",
        chartType: "line",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "SITE_ACTIVITY",
        alias: "siteActivity",
        chartType: "pie",
        position: "bottom",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
    ],
  },

  Municipality_Boundary: {
    isSearchable: true,
    order: 2,
    name: "حدود البلدية",
    outFields: [
      "OBJECTID",
      "PROVINCE_NAME",
      "MUNICIPALITY_NAME",
      "MUNICIPLAITY_TYPE",
    ],
    aliasOutFields: ["amanaName", "munName", "munType"],
    chartInfo: { text: "", displayField: "MUNICIPALITY_NAME" },
    filter: "MUNICIPALITY_NAME",
    filerType: "subType",
    statistics: [
      {
        type: "count",
        field: "OBJECTID",
        name: "count",
      },
    ],
    //statistic: chartStaticsSettigns.landBaseParcel,
    dashboardCharts: [
      {
        name: "MUNICIPLAITY_TYPE",
        alias: "municiplityType",
        chartType: "bar",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "MUN_CLASS",
        alias: "munClass",
        chartType: "pie",
        position: "bottom",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
    ],
  },
  Sub_Municipality_Boundary: {
    isSearchable: true,
    order: 3,
    chartInfo: { text: "", displayField: "SUB_MUNICIPALITY_NAME" },
    name: "حدود البلديات الفرعية",
    outFields: ["OBJECTID", "MUNICIPALITY_NAME", "SUB_MUNICIPALITY_NAME"],
    filter: "SUB_MUNICIPALITY_NAME",
    filerType: "subType",
    aliasOutFields: ["munName", "subMunName"],
    //statistic: chartStaticsSettigns.landBaseParcel,
    dashboardCharts: [],
  },
  UrbanAreaBoundary: {
    isHiddenOnDashboard: true,
    isSeachable: true,
    order: 1,
    chartInfo: { text: "", displayField: "URBAN_BOUNDARY_TYPE" },
    name: "حدود النطاق العمراني",
    outFields: ["OBJECTID", "PROVINCE_NAME", "URBAN_BOUNDARY_TYPE"],
    aliasOutFields: ["amanaName", "UrbanScaleType"],
    dashboardCharts: [],
  },
  District_Boundary: {
    displayField: "DISTRICT_NAME",
    isPublicSearchable: true,
    order: 4,
    chartInfo: { text: "District", displayField: "DISTRICT_NAME" },
    isSearchable: true,
    name: "حدود الأحياء",
    outFields: ["OBJECTID", "MUNICIPALITY_NAME", "DISTRICT_NAME"],
    aliasOutFields: ["munName", "districtName"],
    filerType: "domain",
    filter: "DISTRICT_NAME",
    searchFields: [
      {
        field: "MUNICIPALITY_NAME",
        alias: "munName",
        zoomLayer: {
          name: "Municipality_Boundary",
          filterField: "MUNICIPALITY_NAME",
        },
      },
      {
        field: "DISTRICT_NAME",
        alias: "districtName",
        zoomLayer: { name: "District_Boundary", filterField: "DISTRICT_NAME" },
      },
    ],
    statistics: [
      {
        type: "count",
        field: "OBJECTID",
        name: "count",
      },
    ],
    //statistic: chartStaticsSettigns.landBaseParcel,
    dashboardCharts: [],
    dependecies: [
      {
        name: "Landbase_Parcel",
        icon: faChartPie,
        filter: "DISTRICT_NAME",
        filterDataType: "esriFieldTypeInteger",
        tooltip: "districtLandStatistics",
        depName: "districtLandStatistics",
      },
    ],
  },
  Plan_Data: {
    isPublicSearchable: true,
    order: 5,
    displayField: "PLAN_NO",
    chartInfo: { text: "planNum", displayField: "PLAN_NO" },
    isSearchable: true,
    name: "حدود المخططات",
    outFields: [
      "OBJECTID",
      "PLAN_SPATIAL_ID",
      "MUNICIPALITY_NAME",
      "SUB_MUNICIPALITY_NAME",
      "PLAN_NO",
      "PLAN_NAME",
      "PLAN_SAK_NO",
      "PLAN_AREA",
      "PLAN_CLASS",
      "PLAN_TYPE",
      "PLAN_STATUS",
    ],
    statistics: [
      {
        type: "sum",
        field: "PLAN_AREA",
        name: "area",
      },
      {
        type: "count",
        field: "OBJECTID",
        name: "count",
      },
    ],
    searchFields: [
      {
        field: "MUNICIPALITY_NAME",
        alias: "munName",

        zoomLayer: {
          name: "Municipality_Boundary",
          filterField: "MUNICIPALITY_NAME",
        },
      },
      {
        field: "PLAN_NO",
        alias: "planNum",

        zoomLayer: { name: "Plan_Data", filterField: "PLAN_SPATIAL_ID" },
      },
    ],
    dependecies: [
      {
        name: "Landbase_Parcel",
        icon: faSitemap,
        filter: "PLAN_SPATIAL_ID",
        filterDataType: "esriFieldTypeDouble",
        tooltip: "SurveyingPlotsData",
      },
      {
        name: "Landbase_Parcel",
        icon: faChartPie,
        filter: "PLAN_SPATIAL_ID",
        filterDataType: "esriFieldTypeDouble",
        tooltip: "planNoLandsStatistics",
        depName: "LandStatistics",
      },
    ],
    filter: "PLAN_SPATIAL_ID",
    aliasOutFields: [
      "munName",
      "subMunName",
      "planNum",

      "planName",
      "titleDeedNum",
      "planArea",
      "planclassif",
      "planType",
      "planStatus",
    ],
    area: [
      {
        type: "sum",
        field: "PLAN_AREA",
        name: "area",
      },
    ],
    //statistic: chartStaticsSettigns.landBaseParcel,
    dashboardCharts: [
      {
        name: "PLAN_CLASS",
        alias: "planClass",
        chartType: "polarArea",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },

      {
        name: "PLAN_STATUS",
        alias: "planStatus",
        chartType: "donut",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "PLAN_TYPE",
        alias: "planType",
        chartType: "pie",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "APPROVAL_STATUS",
        alias: "approvalStatus",
        chartType: "bar",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "PLAN_LANDUSE",
        alias: "planLanduse",
        chartType: "line",
        position: "bottom",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
    ],
  },
  Subdivision: {
    isHiddenOnDashboard: true,
    isSearchable: true,
    order: 6,
    chartInfo: { text: "", displayField: "SUBDIVISION_DESCRIPTION" },
    name: "حدود التقسيم",
    outFields: [
      "OBJECTID",
      "SUBDIVISION_SPATIAL_ID",
      "MUNICIPALITY_NAME",
      "SUB_MUNICIPALITY_NAME",
      "SUBDIVISION_NO",
      "PLAN_NO",
      "SUBDIVISION_AREA",
      "SUBDIVISION_DESCRIPTION",
      "SUBDIVISION_TYPE",
    ],
    filter: "SUBDIVISION_SPATIAL_ID",
    aliasOutFields: [
      "munName",
      "subMunName",
      "partitionNum",
      "planNum",

      "partitionArea",
      "partitionDesc",
      "partitionType",
    ],
    area: [
      {
        type: "sum",
        field: "SUBDIVISION_AREA",
        name: "area",
      },
    ],
    //statistic: chartStaticsSettigns.landBaseParcel,
  },
  Landbase_Parcel: {
    isPublicSearchable: true,
    displayField: "PARCEL_PLAN_NO",
    order: 8,
    searchFields: [
      {
        field: "MUNICIPALITY_NAME",
        alias: "munName",

        zoomLayer: {
          name: "Municipality_Boundary",
          filterField: "MUNICIPALITY_NAME",
        },
      },
      {
        field: "DISTRICT_NAME",
        alias: "districtName",
        zoomLayer: { name: "District_Boundary", filterField: "DISTRICT_NAME" },
      },
      {
        field: "PLAN_NO",
        alias: "planNum",

        zoomLayer: { name: "Plan_Data", filterField: "PLAN_SPATIAL_ID" },
      },
      {
        field: "SUBDIVISION_TYPE",
        alias: "subdivisionType",
      },
      {
        field: "SUBDIVISION_DESCRIPTION",
        alias: "subdivisionDesc",
      },
      {
        field: "PARCEL_BLOCK_NO",
        alias: "blockNum",
        zoomLayer: { name: "Survey_Block", filterField: "BLOCK_SPATIAL_ID" },
      },
      { field: "PARCEL_PLAN_NO", alias: "landNum", isServerSideSearch: true },
    ],
    fromAnotherLayer: [
      {
        fieldName: "SUBDIVISION_TYPE",
        layerName: "Subdivision",
        outFields: ["SUBDIVISION_DESCRIPTION"],
      },
    ],
    statistics: [
      {
        type: "sum",
        field: "PARCEL_AREA",
        name: "area",
      },
      {
        type: "count",
        field: "OBJECTID",
        name: "count",
      },
    ],
    depenedFilter: [
      {
        fieldName: "DISTRICT_NAME",
        depenedField: "MUNICIPALITY_NAME",
        outFields: ["DISTRICT_NAME"],
        layer: "District_Boundary",
      },
      {
        fieldName: "DISTRICT_NAME",
        depenedField: "SUB_MUNICIPALITY_NAME",
        outFields: ["DISTRICT_NAME"],
        layer: "District_Boundary",
      },
      {
        fieldName: "SUB_MUNICIPALITY_NAME",
        depenedField: "MUNICIPALITY_NAME",
        outFields: ["SUB_MUNICIPALITY_NAME"],
        layer: "Sub_Municipality_Boundary",
      },
    ],

    isSearchable: true,
    name: "بيانات قطع الأراضي",
    outFields: [
      "OBJECTID",
      "PARCEL_SPATIAL_ID",
      "MUNICIPALITY_NAME",
      "SUB_MUNICIPALITY_NAME",
      "PLAN_NO",
      "PARCEL_PLAN_NO",
      "DISTRICT_NAME",
      "PARCEL_AREA",
      "DETAILED_LANDUSE",
      "USING_SYMBOL",
      "PARCEL_BLOCK_NO",
      "SUBDIVISION_TYPE",
      "SUBDIVISION_DESCRIPTION",
      "PARCEL_MAIN_LUSE",
      "PARCEL_SUB_LUSE",
      "LANDMARK_NAME",
      "BLDG_CONDITIONS",
      // "LIC_NO",
      // "LIC_YEAR",
      "PARCEL_LICENSED",
      "REQ_TASK_TEXT",
      "ISBUILD",
      "UNITS_NUMBER",
      "OWNER_TYPE",
      "PARCEL_LAT_COORD",
      "PARCEL_LONG_COORD",
    ],
    // these are the shown denpendent icons for layer that will be shown into general search, identify and attribute table as well
    dependecies: [
      {
        name: "Tbl_Parcel_Conditions",
        imgIconSrc: buildingConditions,
        filter: "USING_SYMBOL",
        filterDataType: "esriFieldTypeString",
        isTable: true,
        tooltip: "buildingReq",
        depName: "ParcelConditionsBtn",
      },
      {
        name: "TBL_Parcel_LIC",
        imgIconSrc: archieve,
        filter: "PARCEL_SPATIAL_ID",
        filterDataType: "esriFieldTypeDouble",
        isTable: true,
        tooltip: "buildLicenses",
        depName: "ParcelLicenseBtn",
      },
      {
        name: "LICENSE_INFO",
        imgIconSrc: archieve,
        filter: "PARCEL_SPATIAL_ID",
        filterDataType: "esriFieldTypeDouble",
        isFromApi: true,
        tooltip: "licenseInfo",
        url: window.webApiUrl + "LandLicence?spId=",
        depName: "buildingLandLicenseBtn",
      },
      {
        name: "Tbl_SHOP_LIC_MATCHED",
        // icon: storeLicense,
        imgIconSrc: storeLicense,
        filter: "PARCEL_SPATIAL_ID",
        filterDataType: "esriFieldTypeDouble",
        isTable: true,
        tooltip: "storeLicense",
        depName: "ShopLicenseBtn",
      },
      {
        name: "LGR_ROYAL",
        // icon: sa7efa,
        imgIconSrc: sa7efa,
        filter: "PARCEL_SPATIAL_ID",
        filterDataType: "esriFieldTypeDouble",
        isFromApi: true,
        tooltip: "propertyGrantData",
        // permission: function () {
        //   return localStorage.user.groups.find(function (x) {
        //     return x.id == 2528 || true;
        //   });
        // },
        showingField: "OWNER_TYPE",
        codeValue: 2,
        url: window.webApiUrl + "RoyalGrants?isBrief=false&spId=",
        depName: "royalDataAttrTbl",
      },
      {
        name: "SALES_LANDS",
        icon: faUser,
        filter: "PARCEL_SPATIAL_ID",
        filterDataType: "esriFieldTypeDouble",
        isFromApi: true,
        tooltip: "salesLandsData",
        showingField: "OWNER_TYPE",
        codeValue: 3,
        url: window.webApiUrl + "SalesLands/LandsInfo?isBrief=false&spId=",
        depName: "salesLandsAttrTbl",
      },
      {
        name: "PARCEL_PRIVACY",
        imgIconSrc: privateLandIcon,
        filter: "PARCEL_SPATIAL_ID",
        filterDataType: "esriFieldTypeDouble",
        isFromApi: true,
        tooltip: "landsAllotment",
        showingField: "OWNER_TYPE",
        codeValue: 1,
        // permission: function () {
        //   return localStorage.user.groups.find(function (x) {
        //     return x.id == 2689;
        //   });
        url: window.webApiUrl + "GetGovernmentLandsDataNew?isBrief=false&spId=",
        depName: "ownershipAttrTbl",
      },
      {
        name: "Landbase_Parcel",
        icon: faFileInvoice,
        isFromApi: true,
        tooltip: "akarReportBtn",
        redirectURL: "/akarReport",
        depName: "akarReportBtn",
        isLandsRelevant: true,
      },
      {
        name: "Landbase_Parcel",
        // icon: sa7efa,
        imgIconSrc: sa7efa,
        isFromApi: true,
        tooltip: "gisAkarReportBtn",
        redirectURL: "/gisAkarReport",
        depName: "gisAkarReportBtn",
        isLandsRelevant: true,
      },
      {
        name: "KROKY_SUBMISSIONS",
        icon: null,
        imgIconSrc: kroky,
        isFromApi: true,
        tooltip: "krokySubmission",
        showingField: "KROKY",
        url: window.webApiUrl + "submission/GetSubmissionsInfoByLandInfo/8",
        workflowUrl: window.workflowUrl + "survey_report/print_survay/",
        depName: "krokySubmissions",
      },
      {
        name: "FARZ_SUBMISSIONS",
        icon: null,
        imgIconSrc: splitIcon,
        isFromApi: true,
        tooltip: "farzSubmission",
        showingField: "FARZ",
        url: window.webApiUrl + "submission/GetSubmissionsInfoByLandInfo/1",
        workflowUrl: window.workflowUrl + "split_merge/print_duplixs/",
        depName: "farzSubmissions",
      },
      {
        name: "CONTRACT_UPDATE_SUBMISSIONS",
        icon: null,
        imgIconSrc: updateContract,
        isFromApi: true,
        tooltip: "contractUpdateSubmission",
        showingField: "UPDATE_CONTRACT",
        url: window.webApiUrl + "submission/GetSubmissionsInfoByLandInfo/14",
        workflowUrl: window.workflowUrl + "contract_update/print_sak/",
        depName: "updateContractSubmissions",
        className: "contzwa2edClass",
      },
      {
        name: "ZAWAYED_SUBMISSIONS",
        icon: null,
        imgIconSrc: zwa2dTnzemya,
        isFromApi: true,
        tooltip: "zwa2edSubmission",
        showingField: "ZAWAYED",
        url: window.webApiUrl + "submission/GetSubmissionsInfoByLandInfo/22",
        workflowUrl: window.workflowUrl + "addedparcel_temp2/",
        depName: "zawayedSubmissions",
        className: "contzwa2edClass",
      },
      {
        name: "SERVICE_PROJECTS_SUBMISSIONS",
        icon: null,
        imgIconSrc: mshare3Icon,
        isFromApi: true,
        tooltip: "mshare3Submission",
        showingField: "SERVICE_PROJECTS",
        url: window.webApiUrl + "submission/GetSubmissionsInfoByLandInfo/19",
        workflowUrl: window.workflowUrl + "print_technical/",
        depName: "serviceProjectsSubmission",
        className: "contzwa2edClass",
      },
    ],
    filter: "PARCEL_SPATIAL_ID",
    aliasOutFields: [
      "munName",
      "subMunName",
      "planNum",
      "landNumONPlan",
      "districtName",
      "ApproxArea",
      "DetailedUse",
      "useCode",
      "blockNum",
      "partitionType",
      "partitionDesc",
      "mainUseOfLand",
      "subUseOfLand",
      "existBuildName",
      "buildConditionDesc",
      // "licenseNum",
      // "licenseDate",
      "parcelLicensed",
      "isBuild",
      "currentStatus",
      "unitsNum",
      "ownerShipType",
      "latitudeEngCener",
      "longitudeEngCenter",
    ],
    area: [
      {
        type: "sum",
        field: "PARCEL_AREA",
        name: "area",
      },
    ],
    groupByFields: [
      "PARCEL_MAIN_LUSE",
      "PARCEL_SUB_LUSE",
      // "USING_SYMBOL","LANDUSE_SURVEY"
    ],
    //statistic: chartStaticsSettigns.landBaseParcel,
    dashboardCharts: [
      {
        name: "ACTUAL_MAINLANDUSE",
        alias: "actualMainLUse",
        chartType: "donut",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "OWNER_TYPE",
        alias: "ownerType",
        chartType: "line",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count", "area"],
      },
      {
        name: "USING_SYMBOL",
        alias: "usingSymbol",
        chartType: "pie",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "PARCEL_MAIN_LUSE",
        alias: "parcelMainLuse",
        chartType: "bar",
        position: "bottom",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
    ],
  },
  // GOV_boundary: {
  //   isPublicSearchable: true,
  //   displayField: "GOVERNORAT",
  //   order: 12,
  //   searchFields: [
  //     {
  //       field: "GOVERNORAT ",
  //       alias: "GOVERNORAT ",

  //       zoomLayer: {
  //         name: "Municipality_Boundary",
  //         filterField: "MUNICIPALITY_NAME",
  //       },
  //     },

  //   ],

  //   statistics: [
  //     {
  //       type: "sum",
  //       field: "POPULATION2022",
  //       name: "count",
  //     },

  //   ],
  //   depenedFilter: [
  //     {
  //       fieldName: "",
  //       depenedField: "MUNICIPALITY_NAME",
  //       outFields: ["DISTRICT_NAME"],
  //       layer: "District_Boundary",
  //     },
  //     {
  //       fieldName: "DISTRICT_NAME",
  //       depenedField: "SUB_MUNICIPALITY_NAME",
  //       outFields: ["DISTRICT_NAME"],
  //       layer: "District_Boundary",
  //     },
  //     {
  //       fieldName: "SUB_MUNICIPALITY_NAME",
  //       depenedField: "MUNICIPALITY_NAME",
  //       outFields: ["SUB_MUNICIPALITY_NAME"],
  //       layer: "Sub_Municipality_Boundary",
  //     },
  //   ],

  //   isSearchable: true,
  //   name: "بيانات قطع الأراضي",
  //   outFields: [
  //     "OBJECTID",
  //     "PARCEL_SPATIAL_ID",
  //     "MUNICIPALITY_NAME",
  //     "SUB_MUNICIPALITY_NAME",
  //     "PLAN_NO",
  //     "PARCEL_PLAN_NO",
  //     "DISTRICT_NAME",
  //     "PARCEL_AREA",
  //     "DETAILED_LANDUSE",
  //     "USING_SYMBOL",
  //     "PARCEL_BLOCK_NO",
  //     "SUBDIVISION_TYPE",
  //     "SUBDIVISION_DESCRIPTION",
  //     "PARCEL_MAIN_LUSE",
  //     "PARCEL_SUB_LUSE",
  //     "LANDMARK_NAME",
  //     "BLDG_CONDITIONS",
  //     // "LIC_NO",
  //     // "LIC_YEAR",
  //     "PARCEL_LICENSED",
  //     "REQ_TASK_TEXT",
  //     "ISBUILD",
  //     "UNITS_NUMBER",
  //     "OWNER_TYPE",
  //     "PARCEL_LAT_COORD",
  //     "PARCEL_LONG_COORD"
  //   ],
  //   dependecies: [
  //     {
  //       name: "Tbl_Parcel_Conditions",
  //       icon: faBuilding,
  //       filter: "USING_SYMBOL",
  //       filterDataType: "esriFieldTypeString",
  //       isTable: true,
  //       tooltip: "buildingReq",
  //       depName: "ParcelConditionsBtn"
  //     },
  //     {
  //       name: "TBL_Parcel_LIC",
  //       icon: faFilePdf,
  //       filter: "PARCEL_SPATIAL_ID",
  //       filterDataType: "esriFieldTypeDouble",
  //       isTable: true,
  //       tooltip: "buildLicenses",
  //       depName: 'ParcelLicenseBtn'
  //     },
  //     {
  //       name: "Tbl_SHOP_LIC_MATCHED",
  //       icon: faCartPlus,
  //       filter: "PARCEL_SPATIAL_ID",
  //       filterDataType: "esriFieldTypeDouble",
  //       isTable: true,
  //       tooltip: "storeLicense",
  //       depName: 'ShopLicenseBtn'
  //     },
  //     {
  //       name: "LGR_ROYAL",
  //       icon: faUser,
  //       filter: "PARCEL_SPATIAL_ID",
  //       filterDataType: "esriFieldTypeDouble",
  //       isFromApi: true,
  //       tooltip: "propertyGrantData",
  //       // permission: function () {
  //       //   return localStorage.user.groups.find(function (x) {
  //       //     return x.id == 2528 || true;
  //       //   });
  //       // },
  //       showingField: "OWNER_TYPE",
  //       codeValue: 2,
  //       url: window.webApiUrl + "RoyalGrants?isBrief=false&spId=",
  //       depName: 'royalDataAttrTbl'
  //     },
  //     {
  //       name: "SALES_LANDS",
  //       icon: faUser,
  //       filter: "PARCEL_SPATIAL_ID",
  //       filterDataType: "esriFieldTypeDouble",
  //       isFromApi: true,
  //       tooltip: "salesLandsData",
  //       showingField: "OWNER_TYPE",
  //       codeValue: 3,
  //       url: window.webApiUrl + "SalesLands/LandsInfo?isBrief=false&spId=",
  //       depName: 'salesLandsAttrTbl'
  //     },
  //     {
  //       name: "PARCEL_PRIVACY",
  //       imgIconSrc: privateLandIcon,
  //       filter: "PARCEL_SPATIAL_ID",
  //       filterDataType: "esriFieldTypeDouble",
  //       isFromApi: true,
  //       tooltip: "landsAllotment",
  //       showingField: "OWNER_TYPE",
  //       codeValue: 1,
  //       // permission: function () {
  //       //   return localStorage.user.groups.find(function (x) {
  //       //     return x.id == 2689;
  //       //   });
  //       url: window.webApiUrl + "GetGovernmentLandsDataNew?isBrief=false&spId=",
  //       depName: 'ownershipAttrTbl'
  //     },
  //     {
  //       name: "Landbase_Parcel",
  //       icon: faFileInvoice,
  //       isFromApi: true,
  //       tooltip: "akarReportBtn",
  //       redirectURL: "/akarReport",
  //       depName: 'akarReportBtn',
  //       isLandsRelevant: true
  //     },
  //     {
  //       name: "KROKY_SUBMISSIONS",
  //       icon: null,
  //       imgIconSrc: kroky,
  //       isFromApi: true,
  //       tooltip: "krokySubmission",
  //       showingField: "KROKY",
  //       url: window.webApiUrl + "submission/GetSubmissionsInfoByLandInfo/8",
  //       workflowUrl: window.workflowUrl + "survey_report/print_survay/",
  //       depName: 'krokySubmissions'
  //     }, {
  //       name: "FARZ_SUBMISSIONS",
  //       icon: null,
  //       imgIconSrc: splitIcon,
  //       isFromApi: true,
  //       tooltip: "farzSubmission",
  //       showingField: "FARZ",
  //       url: window.webApiUrl + "submission/GetSubmissionsInfoByLandInfo/1",
  //       workflowUrl: window.workflowUrl + "split_merge/print_duplixs/",
  //       depName: 'farzSubmissions'
  //     },
  //     {
  //       name: "CONTRACT_UPDATE_SUBMISSIONS",
  //       icon: null,
  //       imgIconSrc: updateContract,
  //       isFromApi: true,
  //       tooltip: "contractUpdateSubmission",
  //       showingField: "UPDATE_CONTRACT",
  //       url: window.webApiUrl + "submission/GetSubmissionsInfoByLandInfo/14",
  //       workflowUrl: window.workflowUrl + "contract_update/print_sak/",
  //       depName: 'updateContractSubmissions',
  //       className: "contzwa2edClass"
  //     },
  //     {
  //       name: "ZAWAYED_SUBMISSIONS",
  //       icon: null,
  //       imgIconSrc: zwa2dTnzemya,
  //       isFromApi: true,
  //       tooltip: "zwa2edSubmission",
  //       showingField: "ZAWAYED",
  //       url: window.webApiUrl + "submission/GetSubmissionsInfoByLandInfo/22",
  //       workflowUrl: window.workflowUrl + "addedparcel_temp2/",
  //       depName: 'zawayedSubmissions',
  //       className: "contzwa2edClass"
  //     },
  //     {
  //       name: "SERVICE_PROJECTS_SUBMISSIONS",
  //       icon: null,
  //       imgIconSrc: mshare3Icon,
  //       isFromApi: true,
  //       tooltip: "mshare3Submission",
  //       showingField: "SERVICE_PROJECTS",
  //       url: window.webApiUrl + "submission/GetSubmissionsInfoByLandInfo/19",
  //       workflowUrl: window.workflowUrl + "print_technical/",
  //       depName: 'serviceProjectsSubmission',
  //       className: "contzwa2edClass"
  //     },
  //   ],
  //   filter: "PARCEL_SPATIAL_ID",
  //   aliasOutFields: [
  //     "munName",
  //     "subMunName",
  //     "planNum",
  //     "landNumONPlan",
  //     "districtName",
  //     "ApproxArea",
  //     "DetailedUse",
  //     "useCode",
  //     "blockNum",
  //     "partitionType",
  //     "partitionDesc",
  //     "mainUseOfLand",
  //     "subUseOfLand",
  //     "existBuildName",
  //     "buildConditionDesc",
  //     // "licenseNum",
  //     // "licenseDate",
  //     "parcelLicensed",
  //     "isBuild",
  //     "currentStatus",
  //     "unitsNum",
  //     "ownerShipType",
  //     "latitudeEngCener",
  //     "longitudeEngCenter",
  //   ],
  //   area: [
  //     {
  //       type: "sum",
  //       field: "PARCEL_AREA",
  //       name: "area",
  //     },
  //   ],
  //   groupByFields: [
  //     "PARCEL_MAIN_LUSE",
  //     "PARCEL_SUB_LUSE",
  //     // "USING_SYMBOL","LANDUSE_SURVEY"
  //   ],
  //   //statistic: chartStaticsSettigns.landBaseParcel,
  //   dashboardCharts: [
  //   //   {
  //   //   name: 'ACTUAL_MAINLANDUSE',
  //   //   alias: "actualMainLUse",
  //   //   chartType: 'donut',
  //   //   position: 'top',
  //   //   filterDateField: "CREATED_DATE",
  //   //   shownData: ['count']
  //   // },
  //   {
  //     name: 'MUNICIPALITY_NAME',
  //     alias: "MUNICIPALITY_NAME",
  //     chartType: 'pie',
  //     position: 'top',
  //     filterDateField: "CREATED_DATE",
  //     shownData: ['count']
  //   },
  //   // {
  //   //   name: 'USING_SYMBOL',
  //   //   alias: "usingSymbol",
  //   //   chartType: 'pie',
  //   //   position: 'top',
  //   //   filterDateField: "CREATED_DATE",
  //   //   shownData: ['count']
  //   // },
  //    {
  //     name: 'PARCEL_MAIN_LUSE',
  //     alias: "parcelMainLuse",
  //     chartType: 'bar',
  //     position: 'bottom',
  //     filterDateField: "CREATED_DATE",
  //     shownData: ['count']
  //   }]
  // },
  KROKY_SUBMISSIONS: {
    name: "معاملات الكروكي",
    outFields: [
      { id: 1, name: "id", alias: "id" },
      { id: 2, name: "request_no", alias: "request_no", isAnchor: true },
      { id: 3, name: "create_date", alias: "create_date" },
    ],
  },
  FARZ_SUBMISSIONS: {
    name: "معاملات الفرز",
    outFields: [
      { id: 1, name: "id", alias: "id" },
      { id: 2, name: "request_no", alias: "request_no", isAnchor: true },
      { id: 3, name: "export_date", alias: "export_date" },
    ],
  },
  CONTRACT_UPDATE_SUBMISSIONS: {
    name: "معاملات تحديث الصكوك",
    outFields: [
      { id: 1, name: "id", alias: "id" },
      { id: 2, name: "request_no", alias: "request_no", isAnchor: true },
      { id: 3, name: "export_date", alias: "export_date" },
    ],
  },
  ZAWAYED_SUBMISSIONS: {
    name: "معاملات الزوائد الخدمية",
    outFields: [
      { id: 1, name: "id", alias: "id" },
      { id: 2, name: "request_no", alias: "request_no", isAnchor: true },
      { id: 3, name: "create_date", alias: "create_date" },
    ],
  },
  SERVICE_PROJECTS_SUBMISSIONS: {
    name: "معاملات المشاريع الخدمية",
    outFields: [
      { id: 1, name: "id", alias: "id" },
      { id: 2, name: "request_no", alias: "request_no", isAnchor: true },
      { id: 3, name: "create_date", alias: "create_date" },
    ],
  },
  LGR_ROYAL: {
    name: "بيانات المنح الملكية",
    isPublicSearchable: true,
    isSearchable: true,
    displayField: "land_no",
    outFields: [
      { id: 1, name: "citizen_name", alias: "citizenName" },
      { id: 2, name: "id_number", alias: "IdNum" },
      { id: 3, name: "card_no", alias: "cardNum" },
      { id: 4, name: "nor_ror_no", alias: "orderNum" },
      // { id: 5, name: "nor_ror_no_cat", alias: "orderNum" },
      { id: 6, name: "ral_letter_no", alias: "NotLettNum" },
    ],
    searchFields: [
      {
        field: "MUNICIPALITY_NAME",
        alias: "munName",
        required: true,
        apiParamFieldName: "cityId",
        zoomLayer: {
          name: "Municipality_Boundary",
          filterField: "MUNICIPALITY_NAME",
        },
        fetchURL: "/RoyalGrants/Cities?",
        reflectedFields: [
          "PLAN_NO",
          "districtNo",
          "centerNo",
          "categoryNo",
          "blockNo",
          "PARCEL_PLAN_NO",
        ],
      },
      {
        field: "PLAN_NO",
        alias: "planNum",
        apiParamFieldName: "planNo",
        required: true,
        zoomLayer: { name: "Plan_Data", filterField: "PLAN_NO" },
        fetchURL: "/RoyalGrants/PlanNums?",
        dependentFields: ["MUNICIPALITY_NAME"],
        reflectedFields: [
          "districtNo",
          "centerNo",
          "categoryNo",
          "blockNo",
          "PARCEL_PLAN_NO",
        ],
      },
      {
        field: "districtNo",
        alias: "megawrahDivision",
        apiParamFieldName: "districtNo",
        // required: true,
        // zoomLayer: { name: "Plan_Data", filterField: "PLAN_SPATIAL_ID" },
        fetchURL: "/RoyalGrants/Districts?",
        dependentFields: ["MUNICIPALITY_NAME", "PLAN_NO"],
        reflectedFields: ["PARCEL_PLAN_NO"],
      },
      {
        field: "centerNo",
        alias: "districtDivision",
        apiParamFieldName: "centerNo",
        // required: true,
        // zoomLayer: { name: "Plan_Data", filterField: "PLAN_SPATIAL_ID" },
        fetchURL: "/RoyalGrants/Centers?",
        dependentFields: ["MUNICIPALITY_NAME", "PLAN_NO"],
        reflectedFields: ["PARCEL_PLAN_NO"],
      },
      {
        field: "categoryNo",
        alias: "categoryDivision",
        apiParamFieldName: "categoryNo",
        // required: true,
        // zoomLayer: { name: "Plan_Data", filterField: "PLAN_SPATIAL_ID" },
        fetchURL: "/RoyalGrants/Categories?",
        dependentFields: ["MUNICIPALITY_NAME", "PLAN_NO"],
        reflectedFields: ["PARCEL_PLAN_NO"],
      },
      {
        field: "blockNo",
        alias: "blockNum",
        apiParamFieldName: "blockNo",
        // required: true,
        // zoomLayer: { name: "Plan_Data", filterField: "PLAN_SPATIAL_ID" },
        fetchURL: "/RoyalGrants/Blocks?",
        dependentFields: ["MUNICIPALITY_NAME", "PLAN_NO"],
        reflectedFields: ["PARCEL_PLAN_NO"],
      },
      {
        field: "fromDate",
        apiParamFieldName: "fromDate",
        alias: "fromDate",
        isSearch: true,
      },
      {
        field: "toDate",
        apiParamFieldName: "toDate",
        alias: "toDate",
        isSearch: true,
      },
      {
        field: "PARCEL_PLAN_NO",
        alias: "landNumONPlan",
        apiParamFieldName: "landNo",
        // required: true,
        zoomLayer: { name: "Landbase_Parcel", filterField: "PARCEL_PLAN_NO" },
        isServerSideSearch: true,
        fetchURL: "/RoyalGrants/LandsInfo?isBrief=true",
      },
    ],
  },
  //sales lands
  SALES_LANDS: {
    name: "بيانات البيوع",
    isPublicSearchable: true,
    isSearchable: true,
    displayField: "land_no",
    outFields: [
      { id: 1, name: "citizen_name", alias: "citizenName" },
      { id: 2, name: "city_name", alias: "munName" },
      { id: 3, name: "plan_no", alias: "planNum" },
      { id: 4, name: "block_no", alias: "blockNum" },
      { id: 6, name: "land_no", alias: "landNum" },
      { id: 7, name: "land_typen", alias: "landType" },
      { id: 8, name: "land_space", alias: "areaM2" },
      { id: 10, name: "subdivision_type", alias: "subdivisionType" },
      { id: 9, name: "subdivision_description", alias: "subdivisionDesc" },
    ],
    searchFields: [
      {
        field: "MUNICIPALITY_NAME",
        alias: "munName",
        required: true,
        apiParamFieldName: "cityId",
        zoomLayer: {
          name: "Municipality_Boundary",
          filterField: "MUNICIPALITY_NAME",
        },
        fetchURL: "/SalesLands/Cities?",
        reflectedFields: [
          "PLAN_NO",
          // "districtNo", "centerNo", "categoryNo",
          "blockNo",
          "PARCEL_PLAN_NO",
        ],
      },
      {
        field: "PLAN_NO",
        alias: "planNum",
        apiParamFieldName: "planNo",
        required: true,
        zoomLayer: { name: "Plan_Data", filterField: "PLAN_NO" },
        fetchURL: "/SalesLands/PlanNums?",
        dependentFields: ["MUNICIPALITY_NAME"],
        reflectedFields: ["blockNo", "PARCEL_PLAN_NO", "SUBDIVISION_TYPE"],
      },
      // {
      //   field: "districtNo",
      //   alias: "megawrahDivision",
      //   apiParamFieldName: "districtNo",
      //   // required: true,
      //   // zoomLayer: { name: "Plan_Data", filterField: "PLAN_SPATIAL_ID" },
      //   fetchURL: '/SalesLands/Districts?',
      //   dependentFields:["MUNICIPALITY_NAME","PLAN_NO"],
      //   reflectedFields:[ "PARCEL_PLAN_NO" ]
      // },
      // {
      //   field: "centerNo",
      //   alias: "districtDivision",
      //   apiParamFieldName: "centerNo",
      //   // required: true,
      //   // zoomLayer: { name: "Plan_Data", filterField: "PLAN_SPATIAL_ID" },
      //   fetchURL: '/SalesLands/Centers?',
      //   dependentFields:["MUNICIPALITY_NAME","PLAN_NO"],
      //   reflectedFields:[ "PARCEL_PLAN_NO" ]

      // },
      {
        field: "SUBDIVISION_TYPE",
        alias: "subdivisionType",
        apiParamFieldName: "subdivision_type",
        fetchURL: "/SalesLands/Subdivision?",
        reflectedFields: ["PARCEL_PLAN_NO"],
      },
      {
        field: "SUBDIVISION_DESCRIPTION",
        apiParamFieldName: "subdivision_description",
        alias: "subdivisionDesc",
        reflectedFields: ["PARCEL_PLAN_NO"],
      },
      // {
      //   field: "categoryNo",
      //   alias: "categoryDivision",
      //   apiParamFieldName: "categoryNo",
      //   // required: true,
      //   // zoomLayer: { name: "Plan_Data", filterField: "PLAN_SPATIAL_ID" },
      //   fetchURL: '/SalesLands/Categories?',
      //   dependentFields:["MUNICIPALITY_NAME","PLAN_NO"],
      //   reflectedFields:[ "PARCEL_PLAN_NO" ]

      // },
      {
        field: "blockNo",
        alias: "blockNum",
        apiParamFieldName: "blockNo",
        // required: true,
        // zoomLayer: { name: "Plan_Data", filterField: "PLAN_SPATIAL_ID" },
        fetchURL: "/SalesLands/Blocks?",
        dependentFields: ["MUNICIPALITY_NAME", "PLAN_NO"],
        reflectedFields: ["PARCEL_PLAN_NO"],
      },
      {
        field: "fromDate",
        apiParamFieldName: "fromDate",
        alias: "fromDate",
        isSearch: true,
      },
      {
        field: "toDate",
        apiParamFieldName: "toDate",
        alias: "toDate",
        isSearch: true,
      },
      {
        field: "PARCEL_PLAN_NO",
        alias: "landNumONPlan",
        apiParamFieldName: "landNo",
        // required: true,
        zoomLayer: { name: "Landbase_Parcel", filterField: "PARCEL_PLAN_NO" },
        isServerSideSearch: true,
        fetchURL: "/SalesLands/LandsInfo?isBrief=true",
      },
    ],
  },
  PARCEL_PRIVACY: {
    name: "أراضي التخصيص",
    isPublicSearchable: true,
    isSearchable: true,
    displayField: "part_no",

    outFields: [
      { id: 1, name: "request_no", alias: "reqNum" },
      { id: 2, name: "decision_no", alias: "decisionNum" },
      {
        id: 3,
        name: "decision_date_hijri",
        alias: "decisionDate",
        isHijriDateFormat: true,
      },
      { id: 4, name: "organization_name", alias: "organizationName" },
      { id: 5, name: "activity", alias: "activityName" },
      { id: 6, name: "baladia_doc_no", alias: "baladiaDocNo" },
    ],
    searchFields: [
      {
        field: "MUNICIPALITY_NAME",
        alias: "munName",
        apiParamFieldName: "munCode",
        required: true,
        zoomLayer: {
          name: "Municipality_Boundary",
          filterField: "MUNICIPALITY_NAME",
        },
        fetchURL: "/GetGovernmentLandsMunicipalities?",
        reflectedFields: [
          "DISTRICT_NAME",
          "PLAN_NO",
          "SUBDIVISION_TYPE",
          "PARCEL_PLAN_NO",
          "baladia_doc_no",
        ],
      },
      {
        field: "DISTRICT_NAME",
        apiParamFieldName: "districtCode",
        alias: "districtName",
        zoomLayer: { name: "District_Boundary", filterField: "DISTRICT_NAME" },
        fetchURL: "/GetGovernmentLandsDistricts?",
        reflectedFields: ["PARCEL_PLAN_NO"],
      },
      {
        field: "PLAN_NO",
        alias: "planNum",
        // required: true,
        apiParamFieldName: "planNo",
        zoomLayer: { name: "Plan_Data", filterField: "PLAN_NO" },
        fetchURL: "/GetGovernmentLandsPlanNums?",
        reflectedFields: [
          "PARCEL_PLAN_NO",
          "SUBDIVISION_TYPE",
          "baladia_doc_no",
        ],
      },
      {
        field: "baladia_doc_no",
        apiParamFieldName: "baladia_doc_no",
        alias: "baladiaDocNo",
        fetchURL: "/GetGovernmentLandsBaladiaDocNo?",
        reflectedFields: ["PARCEL_PLAN_NO"],
      },
      {
        field: "SUBDIVISION_TYPE",
        alias: "subdivisionType",
        apiParamFieldName: "subdivision_type",
        fetchURL: "/GetGovernmentLandsSubdivisionTypes?",
        reflectedFields: ["PARCEL_PLAN_NO"],
      },
      {
        field: "SUBDIVISION_DESCRIPTION",
        apiParamFieldName: "subdivision_description",
        alias: "subdivisionDesc",
        reflectedFields: ["PARCEL_PLAN_NO"],
      },

      {
        field: "fromDate",
        apiParamFieldName: "fromDate",
        alias: "fromDate",
        isSearch: true,
      },
      {
        field: "toDate",
        apiParamFieldName: "toDate",
        alias: "toDate",
        isSearch: true,
      },
      {
        field: "PARCEL_PLAN_NO",
        alias: "landNumONPlan",
        apiParamFieldName: "part_no",
        // required:true,
        isServerSideSearch: true,
        zoomLayer: {
          name: "Landbase_Parcel",
          filterField: "PARCEL_SPATIAL_ID",
        },
        fetchURL: "/GetGovernmentLandsDataNew?isBrief=false",
      },
    ],
  },
  LICENSE_INFO: {
    name: "بيانات الرخص",
    outFields: [
      { id: 1, name: "owner_name", alias: "ownerName" },
      { id: 2, name: "identifier_id", alias: "identifier_id" },
      {
        id: 3,
        name: "licence_no",
        alias: "licenseNum",
      },
      { id: 4, name: "licence_issue_date", alias: "licenseDate" },
      { id: 5, name: "licence_type_name", alias: "licenseType" },
      { id: 6, name: "deed_number", alias: "deed_number" },
      { id: 6, name: "deed_issue_date", alias: "deed_issue_date" },
    ],
    searchFields: [],
  },
  Serivces_Data: {
    isPublicSearchable: true,
    order: 8,
    displayField: "SRVC_NAME",
    isSearchable: true,
    searchFields: [
      {
        field: "MUNICIPALITY_NAME",
        alias: "munName",
        zoomLayer: {
          name: "Municipality_Boundary",
          filterField: "MUNICIPALITY_NAME",
        },
      },
      { field: "SRVC_TYPE", alias: "servType" },
      { field: "SRVC_SUBTYPE", alias: "servsubUse" },
      { field: "SRVC_NAME", alias: "servName", isServerSideSearch: true },
    ],
    statistics: [
      {
        type: "count",
        field: "OBJECTID",
        name: "count",
      },
    ],
    depenedFilter: [
      {
        fieldName: "DISTRICT_NAME",
        depenedField: "MUNICIPALITY_NAME",
        outFields: ["DISTRICT_NAME"],
        layer: "District_Boundary",
      },
      {
        fieldName: "SUB_MUNICIPALITY_NAME",
        depenedField: "MUNICIPALITY_NAME",
        outFields: ["SUB_MUNICIPALITY_NAME"],
        layer: "Sub_Municipality_Boundary",
      },
    ],
    name: "بيانات الخدمة",
    outFields: [
      "OBJECTID",
      "MUNICIPALITY_NAME",
      "DISTRICT_NAME",
      "SUB_MUNICIPALITY_NAME",
      "CITY_NAME",
      "PLAN_NO",
      "SRVC_NAME",
      "SRVC_TYPE",
      "SRVC_DESCRIPTION",
      "SRVC_SUBTYPE",
      //"SRVC_OWNER_TYPE"
    ],
    aliasOutFields: [
      "munName",
      "districtName",
      "subMunName",
      "cityName",
      "planNum",
      "servName",

      "servType",
      "servDesc",
      "servsubUse",

      // "ملكية الخدمة"
    ],
    dashboardCharts: [
      {
        name: "SRVC_TYPE",
        alias: "serviceType",
        chartType: "donut",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "SERVICE_DVELOPED",
        alias: "serviceIsDeveloped",
        chartType: "bar",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "SRVC_OWNER_TYPE",
        alias: "serviceOwners",
        chartType: "line",
        position: "bottom",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
    ],
  },

  Survey_Block: {
    isHiddenOnDashboard: true,
    isSearchable: true,
    order: 7,
    depenedFilter: [
      {
        fieldName: "DISTRICT_NAME",
        depenedField: "MUNICIPALITY_NAME",
        outFields: ["DISTRICT_NAME"],
        layer: "District_Boundary",
      },
      {
        fieldName: "SUB_MUNICIPALITY_NAME",
        depenedField: "MUNICIPALITY_NAME",
        outFields: ["SUB_MUNICIPALITY_NAME"],
        layer: "Sub_Municipality_Boundary",
      },
    ],
    chartInfo: { text: "blockNum", displayField: "BLOCK_NO" },
    name: "حدود البلوكات",
    outFields: [
      "OBJECTID",
      "BLOCK_SPATIAL_ID",
      "MUNICIPALITY_NAME",
      "SUB_MUNICIPALITY_NAME",
      "BLOCK_NO",
      "PLAN_NO",
      "BLOCK_LUSE",
      "BLOCK_AREA",
      "DISTRICT_NAME",
      "CITY_NAME",
    ],
    aliasOutFields: [
      "munName",
      "subMunName",
      "blockNum",
      "planNum",
      "blockUse",
      "blockArea",
      "districtName",
      "cityName",
    ],
    filter: "BLOCK_SPATIAL_ID",
    area: [
      {
        type: "sum",
        field: "BLOCK_AREA",
        name: "area",
      },
    ],
    //statistic: chartStaticsSettigns.landBaseParcel,
  },

  TBL_Parcel_LIC: {
    isSearchable: false,

    name: "رخص البناء",
    outFields: [
      "OBJECTID",
      "LIC_NO",
      "LIC_YEAR",
      "H_ISSUE_DATE",
      "NO_FLOORS",
      "REQ_TASK_TEXT",
      "ORD_TYPE",
      "BLD_TYPE",
      "RECORD_STATUS_TEXT",
      "REGION_USE_NO",
      "AREA_IN_ORD",
      "EL_LETTER_NO",
      "EL_LETTER_YEAR",
      "H_DATE",
      "RECORD_STATUS_TEXT ",
      "NO_IND_UNITS",
      "NO_LIV_UNITS",
      "NO_SRV_UNITS",
      "OWNER_ID_NO",
      "OWNER_ID_DATE",
      "OWNER_ID_SOURCE",
      "RANK",
      "RANK_DESCRIPTION",
    ],
    isHidden: true,
    aliasOutFields: [
      "licenseNum",
      "liceYearIssuance",
      "liceDateIssuance",
      "floorsNum", //عدد الادوار
      "currentStatus",
      "orderType",
      "buildType",
      "orderStatus",
      "regeqNum",
      "reaFromInstru",
      "letterNum",
      "speechHistory",
      "HijriDate",
      "orderStatus",
      "indusUnitsNum",
      "houseUnitsNum",
      "servNum",
      "IdNum",
      "IdHistory",
      "idSource",
      "accuracyStatLevel",
      "accuracyStat",
    ],
  },
  Parcel_Boundary: {
    name: "جدول أضلاع الأراضي",
    outFields: [
      "OBJECTID",
      "BOUNDARY_NO",
      "FROM_CORNER",
      "TO_CORNER",
      "BOUNDARY_LENGTH",
      "BOUNDARY_DIRECTION",
      "BOUNDARY_DESCRIPTION",
    ],
    isHidden: true,
    aliasOutFields: [
      "boundaryNum",
      "ribstartPoint",
      "ribfinishPoint",
      "boundaryLength",
      "Boundary_Direction",
      "boundaryDesc",
    ],
    dashboardCharts: [],
  },
  Parcel_Corner: {
    name: "جدول نقاط إحداثيات الأركان",
    outFields: [
      "OBJECTID",
      "CORNER_NO",
      "XUTM_COORD",
      "YUTM_COORD",
      "XGCS_COORD",
      "YGCS_COORD",
    ],
    isHidden: true,
    aliasOutFields: [
      "pointNumInLand",
      "xCoord",
      "yCoord",
      "longitudeCoord",
      "latitudeCoord",
    ],
    dashboardCharts: [],
  },

  Street_Naming: {
    order: 9,

    isPublicSearchable: true,
    isSearchable: true,
    depenedFilter: [
      {
        fieldName: "DISTRICT_NAME",
        depenedField: "MUNICIPALITY_NAME",
        outFields: ["DISTRICT_NAME"],
        layer: "District_Boundary",
      },
      {
        fieldName: "SUB_MUNICIPALITY_NAME",
        depenedField: "MUNICIPALITY_NAME",
        outFields: ["SUB_MUNICIPALITY_NAME"],
        layer: "Sub_Municipality_Boundary",
      },
    ],
    searchFields: [
      {
        field: "MUNICIPALITY_NAME",
        alias: "munName",
        zoomLayer: {
          name: "Municipality_Boundary",
          filterField: "MUNICIPALITY_NAME",
        },
      },
      {
        field: "STREET_FULLNAME",
        alias: "streetName",
        isServerSideSearch: true,
      },
    ],
    displayField: "STREET_FULLNAME",
    name: "الشوارع",
    outFields: [
      "OBJECTID",
      "MUNICIPALITY_NAME",
      "SUB_MUNICIPALITY_NAME",
      "DISTRICT_NAME",
      "STREET_FULLNAME",
      "STREET_NAME_CLS",
      "STREET_CLASS",
      //"STREET_LENGTH",
      "WIDTH",
      //"STREET_TYPE",
      //"STREET_SPEED_CLASS",
      //"STREET_LIFECYCLE_STATUS",
    ],
    aliasOutFields: [
      "munName",
      "subMunName",
      "districtName",
      "streetName",
      "streetClassification",
      "streetType",
      "streetWidth",
      //"طول الطريق",
      //"عرض الطريق",
      //"نوع الشارع",
      //"سرعة الطريق",
      //"الحالة العمرية للطريق",
    ],
    dashboardCharts: [
      {
        name: "STREET_NAME_CLS",
        alias: "streetNameClass",
        chartType: "donut",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "ONE_WAY",
        alias: "oneWay",
        chartType: "bar",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "ASPHALT_STATUS",
        alias: "asphaltStatus",
        chartType: "line",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "STREET_CLASS",
        alias: "streetClass",
        chartType: "pie",
        position: "bottom",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
    ],
  },

  Tbl_Parcel_Conditions: {
    name: "اشتراطات البناء",

    outFields: [
      "OBJECTID",
      "SLIDE_AREA",
      "MIN_FROT_OFFSET",
      "DESCRIPTION",
      "BUILDING_RATIO",
      "FRONT_OFFSET",
      "SIDE_OFFSET",
      "BACK_OFFSET",
      "FLOORS",
      "FLOOR_HEIGHT",
      "ADD_FLOOR",
      "USING_SYMBOL",
      "FAR",
    ],
    isHidden: true,
    aliasOutFields: [
      "couponspace",
      "minFacade",
      "description",
      "buildRation",
      "faceBounce",
      "revAsspects",
      "backback",

      "floors",
      "floorHeight",
      "canaddFloor",
      "useCode",
      "buildBlockCoeff",
    ],
  },

  Admin_Mun_VoteDistrict: {
    isSearchable: true,

    isPublicSearchable: true,
    order: 1,
    name: "الدوائر الانتحابية",
    outFields: [
      "OBJECTID",
      "REMARKS",
      "VOTEDISTRICT_CODE",
      "VOTEDISTRICT_NAME_A",
    ],
    aliasOutFields: ["munName", "constituencyNum", "constituency"],
    searchFields: [
      { field: "REMARKS", alias: "munName" },
      { field: "VOTEDISTRICT_CODE", alias: "constituencyNum" },
      {
        field: "VOTEDISTRICT_NAME_A",
        alias: "constituency",
        isServerSideSearch: true,
      },
    ],
    dashboardCharts: [],
  },
  GOV_boundary: {
    isHidden: true,
    isHiddenOnDashboard: true,
  },
  Eastern_Marsad: {
    isHidden: true,
    name: "المرصد الحضري",
    outFields: [
      "SECTOR",
      "SECTOR_TYBE",
      "SECTOR_SUBTYBE",
      "GOV_NAME",
      "INDICATOR_VALUE",
      "INDICATOR_NAME",
      "INDICATOR_SUBTYBE",
      "INDICATOR_DEFINATION",
      "INDICATOR_MEASURE",
      "RGB",
    ],
    aliasOutFields: [
      "sector",
      "sectorClassfication",
      "subSector",
      "govName",
      "inicatorValue",
      "indicatorName",
      "indicatorDetails",
      "indicatorIdentify",
      "indicatorMeasureUnit",
    ],
    searchFields: [
      { field: "SECTOR", alias: "sector" },
      { field: "SECTOR_TYBE", alias: "sectorClassfication" },
      { field: "SECTOR_SUBTYBE", alias: "subSector" },
      { field: "INDICATOR_NAME", alias: "indicatorName" },
      { field: "INDICATOR_SUBTYBE", alias: "indicatorClassfication" },
    ],
    isHiddenOnDashboard: true,
  },
  Amana_buildings: {
    isSearchable: true,
    isPublicSearchable: true,
    order: 1,
    searchFields: [
      { field: "BUILDING_TYPE", alias: "buildType" },
      { field: "MATRIAL_TYPE", alias: "buildingMaterialType" },
      { field: "BUILDING_NAME", alias: "buildName", isServerSideSearch: true },
    ],
    name: "حصر مباني الأمانة",
    outFields: [
      "OBJECTID",
      "BUILDING_NAME",
      "OWNER_NAME",
      "FLOOR_NO",
      "PRICE",
      "BUILDING_TYPE",
      "MATRIAL_TYPE",
      "PRICE_STATUS",
    ],
    aliasOutFields: [
      "buildName",
      "ownerName",
      "floorsNum",
      "rentValue",
      "buildType",
      "buildingMaterialType",
      "buildStatus",
    ],
    dashboardCharts: [
      {
        name: "BUILDING_TYPE",
        alias: "buildingType",
        chartType: "pie",
        position: "bottom",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "PRICE_STATUS",
        alias: "priceStatus",
        chartType: "bar",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
      {
        name: "MATRIAL_TYPE",
        alias: "materialType",
        chartType: "line",
        position: "top",
        filterDateField: "CREATED_DATE",
        shownData: ["count"],
      },
    ],
  },
};
export const royalPrivateLandsPrintFields = {
  PARCEL_PRIVACY: {
    columns: [
      "city_name",
      "plan_no",
      "part_no",
      "organization_name",
      "activity",
      "part_area",
      "allocation_typen",
      "decision_no",
      "decision_dateg",
      "decision_date_hijri",
    ],
    labels: [
      "cityName",
      "planNo",
      "landNo",
      "orgName",
      "licPurpose",
      "areaByM2",
      "typeLic",
      "decisionNoLic",
      "dateMilady",
      "dateHijri",
    ],
  },
  LGR_ROYAL: {
    columns: [
      "MUNICIPALITY_NAME",
      "plan_no",
      "land_no",
      "citizen_name",
      "id_number",
      "card_no",
      "nor_ror_no",
      "ral_letter_no",
    ],
    labels: [
      "cityName",
      "planNo",
      "landNo",
      "citizenName",
      "IdNum",
      "cardNum",
      "orderNum",
      "NotLettNum",
    ],
  },
  SALES_LANDS: {
    columns: [
      "MUNICIPALITY_NAME",
      "plan_no",
      "land_no",
      "citizen_name",
      "block_no",
      "land_typen",
      "land_space",
      "subdivision_type",
      "subdivision_description",
    ],
    labels: [
      "cityName",
      "planNo",
      "landNo",
      "citizenName",
      "blockNum",
      "landType",
      "areaM2",
      "subdivisionType",
      "subdivisionDesc",
    ],
  },
};
export const randomPlanNumbers = ["الارشادي", "9999"];
export const blockLandsLayerObj = {
  arabicName: "أراضي البلكات القديمة",
  arname: "أراضي البلكات القديمة",
  name: "OLD_Landbase_Parcel",
  englishName: "OLD_Landbase_Parcel",
  searchFields: [
    {
      field: "MUNICIPALITY_NAME",
      alias: "munName",

      zoomLayer: {
        name: "Municipality_Boundary",
        filterField: "MUNICIPALITY_NAME",
      },
    },
    {
      field: "DISTRICT_NAME",
      alias: "districtName",
      zoomLayer: { name: "District_Boundary", filterField: "DISTRICT_NAME" },
    },
    {
      field: "PARCEL_BLOCK_NO",
      alias: "blockNum",
      zoomLayer: { name: "Survey_Block", filterField: "BLOCK_SPATIAL_ID" },
    },
    { field: "PARCEL_PLAN_NO", alias: "landNum", isServerSideSearch: true },
  ],
};

export const layersSetting =
  /*localStorage.user ? */ loginLayersSetting; /* : publicUserlayersSetting*/

//for any new added layer on map service
export const TempobjectLayer = {
  searchFields: [
    {
      field: "MUNICIPALITY_NAME",
      alias: "munName",
      zoomLayer: {
        name: "Municipality_Boundary",
        filterField: "MUNICIPALITY_NAME",
      },
    },
    {
      field: "SUB_MUNICIPALITY_NAME",
      alias: "subMunName",
      zoomLayer: {
        name: "Sub_Municipality_Boundary",
        filterField: "SUB_MUNICIPALITY_NAME",
      },
    },
    {
      field: "DISTRICT_NAME",
      alias: "districtName",
      zoomLayer: { name: "District_Boundary", filterField: "DISTRICT_NAME" },
    },
  ],
  isPublicSearchable: true,
};

export const incidentLayerName = "incidents940";

export const OLD_BLOCK_LAYER_NAME = "OLD_BLOCK";
export const nonDisplayedLayers = ["UnusedParcels"];
export const akarReportFields = {
  outFields: [
    "PARCEL_SPATIAL_ID",
    "CITY_NAME",
    "DISTRICT_NAME",
    "PLAN_NO",
    "PARCEL_BLOCK_NO",
    "SUBDIVISION_TYPE",
    "SUBDIVISION_DESCRIPTION",
    "PARCEL_PLAN_NO",
    "PARCEL_AREA",
    "PARCEL_MAIN_LUSE",
    "LANDMARK_NAME",
    "PARCEL_LAT_COORD",
    "PARCEL_LONG_COORD",
  ],
  aliasOutFields: [
    "",
    "govMunName",
    "districtName",
    "planNum",
    "blockNum",
    "subdivisionType",
    "subdivisionDesc",
    "landNo",
    "ApproxArea",
    "mainUse",
    "locationName",
    "lngCentroid",
    "latCentroid",
  ],
};
export const cornersBoundariesFields = {
  corners: {
    outFields: ["CORNER_NO", "XUTM_COORD", "YUTM_COORD"],
    aliasOutFields: ["cornerNo", "xCoord", "yCoord"],
  },
  boundaries: {
    outFields: [
      "BOUNDARY_DIRECTION",
      "BOUNDARY_LENGTH",
      "FROM_CORNER",
      "TO_CORNER",
    ],
    aliasOutFields: [
      "boundaryDirection",
      "boundaryLength",
      "startBoundary",
      "endBoundary",
    ],
  },
};

//royals config (adminstration bound data)
export const municipilitiesForRoyal = [
  {
    name: "الدمام",
    GISCode: 10501,
  },
  {
    name: "الخبر",
    GISCode: 10506,
  },
  {
    name: "القطيف",
    GISCode: 10505,
  },
  {
    name: "الجبيل",
    GISCode: 10504,
  },
  {
    name: "راس تنورة",
    GISCode: 10508,
  },
  {
    name: "الخفجى",
    GISCode: 10507,
  },
  {
    name: "حفر الباطن",
    GISCode: 10503,
  },
  {
    name: "الظهران",
    GISCode: 10513,
  },
  {
    name: "تاروت",
    GISCode: 10514,
  },
  {
    name: "صفوى",
    GISCode: 10515,
  },
  {
    name: "سيهات",
    GISCode: 10516,
  },
  {
    name: "عنك",
    GISCode: 10517,
  },
  {
    name: "الرفيعة",
    GISCode: 10520,
  },
  {
    name: "قرية العليا",
    GISCode: 10511,
  },
  {
    name: "بقيق",
    GISCode: 10509,
  },
  {
    name: "النعيرية",
    GISCode: 10510,
  },
  {
    name: "مليجة",
    GISCode: 10523,
  },
  {
    name: "عين دار",
    GISCode: 10524,
  },
  {
    name: "اللهابة",
    GISCode: 10525,
  },
  {
    name: "الصرار",
    GISCode: 10526,
  },
  {
    name: "القليب",
    GISCode: 10527,
  },
  {
    name: "جوف بني هاجر",
    GISCode: 10528,
  },
  {
    name: "عريعرة",
    GISCode: 10530,
  },
  {
    name: "بلدية القديح",
    GISCode: 10531,
  },
  {
    name: "بلدية البيضاء",
    GISCode: 10512,
  },
];

export const alphbetNumbers = [
  {
    name: ["أ", "ا"],
    value: 1,
  },
  {
    name: "ب",
    value: 2,
  },
  {
    name: "ج",
    value: 3,
  },
  {
    name: "د",
    value: 4,
  },
  {
    name: "ه",
    value: 5,
  },
  {
    name: "و",
    value: 6,
  },
  {
    name: "ز",
    value: 7,
  },
  {
    name: "ح",
    value: 8,
  },
  {
    name: "ط",
    value: 9,
  },
  {
    name: "ي",
    value: 10,
  },
  {
    name: "ك",
    value: 11,
  },
  {
    name: "ل",
    value: 12,
  },
];

export const megawrahNumbersInString = [
  {
    value: "1",
    like: { OR: ["اول", "أول"] },
    NOT: "عشر",
  },
  {
    value: "2",
    like: "ثان",
    NOT: "عشر",
  },
  {
    value: "3",
    like: "ثالث",
    NOT: "عشر",
  },
  {
    value: "4",
    like: "رابع",
    NOT: "عشر",
  },
  {
    value: "5",
    like: "خامس",
    NOT: "عشر",
  },
  {
    value: "6",
    like: "سادس",
    NOT: "عشر",
  },
  {
    value: "7",
    like: "سابع",
    NOT: "عشر",
  },
  {
    value: "8",
    like: "ثامن",
    NOT: "عشر",
  },
  {
    value: "9",
    like: "تاسع",
    NOT: "عشر",
  },
  {
    value: "10",
    like: "عاشر",
    NOT: "عشر",
  },
  {
    value: "11",
    like: { OR: ["أحد", "حادي", "احد"], AND: "عشر" },
  },
  {
    value: "12",
    like: { OR: ["ثان"], AND: "عشر" },
  },
  {
    value: "13",
    like: { OR: ["ثالث", "ثلاث"], AND: "عشر" },
  },
  {
    value: "14",
    like: { OR: ["رابع", "أربع", "اربع"], AND: "عشر" },
  },
  {
    value: "15",
    like: { OR: ["خمس", "خامس"], AND: "عشر" },
  },
  {
    value: "16",
    like: { OR: ["سادس", "ستة"], AND: "عشر" },
  },
  {
    value: "17",
    like: { OR: ["سابع", "سبعة"], AND: "عشر" },
  },
];

export const subdivisionTypes = {
  megawrah: 2, // مجاورة
  district: 3, // منطقة
  category: 1, //فئة
  center: 4, //مركز الحي
  mar7la: 5, //مرحلة
};

export function getNumbersInStringsByValue(value) {
  let requValue = megawrahNumbersInString.find((i) => {
    let bool;
    if (typeof i.like === "string") {
      if (i.NOT) {
        bool = value.includes(i.like) && !value.includes(i.NOT);
      } else bool = value.includes(i.like);
    } else {
      if (i.like?.OR && i.like?.AND) {
        bool =
          i.like.OR.find((ii) => value.includes(ii)) &&
          value.includes(i.like.AND);
      } else if (i.like?.OR) {
        bool = i.like.OR.find((ii) => value.includes(ii));
      }
    }
    return bool;
  });
  return requValue;
}
