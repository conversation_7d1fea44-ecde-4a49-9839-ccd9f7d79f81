import React, { useEffect, useState, forwardRef } from "react";
import styles from "../sidemenu-Components/Deals.module.css";
import trackingStyles from "../sidemenu-Components/Tracking.module.css";
import FloatingLabelTextarea from "../mapComponents/tools/more/FloatingLabelTextarea";
import FloatingLabelInput from "../mapComponents/tools/more/FloatingLabelInput";
import Axios from "axios";
import closeImg from "../../src/assets/icons/close.svg";
import { Box, Grid, Dialog, Slide } from "@mui/material";
import { useTranslation } from "react-i18next";
import { ExpandLess, ExpandMore } from "@mui/icons-material";
import { getDistrictNameById } from "../helper/common_func";
import { isNaN } from "lodash";
import { Button, Form, Input, Tooltip } from "antd";
import Upload from "../components/Upload/Upload";
import ImageGallery from "react-image-gallery"; // Import react-image-gallery
import "react-image-gallery/styles/css/image-gallery.css";
import { convertToArabic } from "../helper/common_func";
import { IoArrowBackCircleOutline } from "react-icons/io5";

import { Tabs } from "antd";
const { TabPane } = Tabs;

const { TextArea } = Input;

const UpdateDataLandView = ({ setIsOpen, ticketData, features, map }) => {
  const { t, i18n } = useTranslation(["common"]);
  const [districtName, setDistrictName] = useState(
    features[0]?.attributes.DISTRICT_NAME
  );
  const [selectedAttachments, setSelectedAttachments] = useState(
    ticketData.attachments || []
  );
  const [formValues, setFormValues] = useState({
    landesNumbers: ticketData.description.split("--")?.[1]?.trim() || "",
    planNumbers: ticketData.description.split("--")?.[2]?.trim() || "",
    streetNumbers: ticketData.description.split("--")?.[3]?.trim() || "",
    description: ticketData.description.split("--")?.[0]?.trim(),
    ticketTypeId: ticketData.ticketTypeId,
    attachments: ticketData.attachments,
  });
  const [isVisible, setIsVisible] = useState(true);
  useState(() => {
    Axios.get(`${window.ApiUrl}/ticket-type`, {
      params: { isMadinaty: true },
    }).then(({ data }) => {
      if (data.length !== 0) {
        setFormValues({
          ...formValues,
          ticketTypeName: data.find((r) => r.id == formValues.ticketTypeId)
            .name,
        });
      }
    });
  }, [formValues.selectedLandes]);
  const [isOpenContent, setIsOpenContent] = useState(false);

  const toggleContent = () => {
    setIsOpenContent(!isOpenContent);
  };

  useEffect(() => {
    if (!isNaN(+districtName)) {
      getDistrictNameById(map, features[0]?.attributes.DISTRICT_NAME).then(
        (res) => setDistrictName(res)
      );
    }
  }, []);

  const handleClose = () => {
    setIsOpen(false);
    map.graphics?.clear();
    map?.findLayerById("highLightGraphicLayer")?.removeAll();
    map.findLayerById("SketchLayer")?.removeAll();
  };

  const getGalleryItems = (attachments) => {
    if (!attachments || !Array.isArray(attachments)) return [];
    return attachments
      .filter((file) => {
        const fileName = file.fileName?.toLowerCase();
        return (
          fileName?.endsWith(".jpg") ||
          fileName?.endsWith(".jpeg") ||
          fileName?.endsWith(".png")
        );
      })
      .map((file) => ({
        original: `${window.filesURL}${file.path}`,
        thumbnail: `${window.filesURL}${file.path}`,
      }));
  };

  // Get PDF links
  const getPdfLinks = (attachments) => {
    if (!attachments || !Array.isArray(attachments)) return [];
    return attachments.filter((file) =>
      file.fileName?.toLowerCase().endsWith(".pdf")
    );
  };

  return (
    <div style={{ padding: "10px", maxHeight: "87vh", overflowY: "auto" }}>
      <div
        className={styles.SearchLandTitle}
        style={{
          marginBottom: "10px",
          flexDirection: "column",
          alignItems: "start",
        }}
      >
        <div
          className={styles.title}
          style={{
            margin: "0",
            backgroundColor: "transparent",
          }}
        >
          {/* <img src={closeImg} alt="" onClick={handleClose} /> */}
          <Tooltip title={t("common:back")}>
            <IoArrowBackCircleOutline
              onClick={handleClose}
              size={25}
              color="#284587"
              style={{
                cursor: "pointer",
                transform: "rotate(180deg)",
              }}
            />
          </Tooltip>
          <span>{t("validation.applyTicket")}</span>
        </div>
        <div style={{ display: "flex", alignItems: "center", width: "100%" }}>
          <div
            style={{
              padding: "10px",
              backgroundColor: "transparent",
              width: "100%",
              display: "flex",
              gap: "10px",
            }}
          >
            <span
              class={
                ticketData.isClosed
                  ? trackingStyles.finished
                  : trackingStyles.running
              }
              style={{
                margin: "0",
                color: "#284587",
                fontSize: "14px",
                fontWeight: "400",
                display: "flex",
                alignItems: "center",
                gap: "10px",
                // width: "130px",
                justifyContent: "center",
                flex: 1,
              }}
            >
              {t("validation.ticket_no")} :{" "}
              {convertToArabic(ticketData.title.match(/(\d+\/?)+/g)[0])}
            </span>
            <span
              class={
                ticketData.isClosed
                  ? trackingStyles.finished
                  : trackingStyles.running
              }
              style={{
                margin: "0",
                color: "#284587",
                fontSize: "14px",
                fontWeight: "400",
                display: "flex",
                alignItems: "center",
                gap: "10px",
                // width: "130px",
                justifyContent: "center",
                flex: 1,
              }}
            >
              {/* {t("validation.ticket_no")} :{" "}
              {convertToArabic(ticketData.title.match(/(\d+\/?)+/g)[0])} */}
              {(ticketData.isClosed && "منتهية") || "قيد المعالجة"}
            </span>
          </div>
        </div>
      </div>

      <Grid
        container
        spacing={1}
        style={{
          // height: "73vh",
          overflow: "auto",
        }}
      >
        <Grid item xs={12}>
          <div className={`${styles.attributeContainer}`}>
            <div
              className={`${styles.attributeHeader} px-2 py-1`}
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                cursor: "pointer",
                margin: "auto",
              }}
              onClick={toggleContent}
            >
              {!isOpenContent ? <ExpandLess /> : <ExpandMore />}
            </div>

            {/* <div
              className={`${styles.attributeHeader} px-2 py-1`}
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                cursor: "pointer",
              }}
              onClick={toggleContent}
            >
              {!isOpenContent ? <ExpandLess /> : <ExpandMore />}
            </div> */}
            <div
              className={`${styles.attributeContent} ${
                !isOpenContent ? styles.contentOpen : styles.contentClosed
              } mb-2 px-2 py-1`}
              style={{
                textAlign: i18n.language === "en" ? "left" : "right",
              }}
            >
              <Grid
                container
                spacing={2}
                className="attribute-row"
                style={{
                  padding: "8px 0",
                  borderBottom: "1px solid #f1f1f1",
                }}
              >
                <Grid item xs={6}>
                  <div className={styles.attributeKey}>
                    {t("common:municipality")}
                  </div>
                </Grid>
                <Grid item xs={6} className="px-0">
                  <div className={styles.attributeValue}>
                    {features[0]?.attributes.CITY_NAME ||
                      features[0]?.attributes.MUNICIPALITY_NAME}
                  </div>
                </Grid>
              </Grid>
              {districtName && (
                <Grid
                  container
                  spacing={2}
                  className="attribute-row"
                  style={{
                    padding: "8px 0",
                    borderBottom: "1px solid #f1f1f1",
                  }}
                >
                  <>
                    <Grid item xs={6}>
                      <div className={styles.attributeKey}>
                        {t("generalSearch:district")}
                      </div>
                    </Grid>
                    <Grid item xs={6} className="px-0">
                      <div className={styles.attributeValue}>
                        {districtName}
                      </div>
                    </Grid>
                  </>
                </Grid>
              )}
              {formValues.planNumbers.length > 0 && (
                <Grid
                  container
                  spacing={2}
                  className="attribute-row"
                  style={{
                    padding: "8px 0",
                    borderBottom: "1px solid #f1f1f1",
                  }}
                >
                  <>
                    <Grid item xs={6}>
                      <div className={styles.attributeKey}>
                        {t("generalSearch:planNumber")}
                      </div>
                    </Grid>
                    <Grid item xs={6} className="px-0">
                      <div className={styles.attributeValue}>
                        {formValues.planNumbers
                          .split(",")
                          .map((r) => convertToArabic(r.trim()) || r.trim())
                          .join(", ")}
                      </div>
                    </Grid>
                  </>
                </Grid>
              )}
              {formValues.streetNumbers.length > 0 && (
                <Grid
                  container
                  spacing={2}
                  className="attribute-row"
                  style={{
                    padding: "8px 0",
                    borderBottom: "1px solid #f1f1f1",
                  }}
                >
                  <>
                    <Grid item xs={6}>
                      <div className={styles.attributeKey}>
                        {t("generalSearch:streetName")}
                      </div>
                    </Grid>
                    <Grid item xs={6} className="px-0">
                      <div className={styles.attributeValue}>
                        {formValues.streetNumbers
                          .split(",")
                          .map((r) => convertToArabic(r.trim()) || r.trim())
                          .join(", ")}
                      </div>
                    </Grid>
                  </>
                </Grid>
              )}
              {formValues.landesNumbers.length > 0 && (
                <Grid
                  container
                  spacing={2}
                  className="attribute-row"
                  style={{
                    padding: "8px 0",
                    borderBottom: "1px solid #f1f1f1",
                  }}
                >
                  <>
                    <Grid item xs={6}>
                      <div className={styles.attributeKey}>
                        {t("generalSearch:parcelNumber")}
                      </div>
                    </Grid>
                    <Grid item xs={6} className="px-0">
                      <div className={styles.attributeValue}>
                        {formValues.landesNumbers
                          .split(",")
                          .map((r) => convertToArabic(r.trim()) || r.trim())
                          .join(", ")}
                      </div>
                    </Grid>
                  </>
                </Grid>
              )}
            </div>
          </div>
        </Grid>

        <Grid
          item
          xs={12}
          style={{
            paddingTop: "10px",
            paddingBottom: "0",
            marginTop: "1vh",
            marginBottom: "1vh",
          }}
        >
          {/* <FloatingLabelInput
            label={t("layers:orderType")}
            placeholder={t("layers:selectOrderType")}
            value={formValues.ticketTypeName}
            disabled={true}
          /> */}

          <Form
            className="GeneralForm"
            layout="vertical"
            name="validate_other"
            style={{
              width: "100%",
              padding: "0 10px",
              display: "flex",
              flexDirection: "column",
              gap: "10px",
            }}
            // onFinish={addBookMark}
          >
            <Form.Item
              label={t("layers:orderType")}
              rules={[
                {
                  message: "ابحث برقم الطلب",
                  required: true,
                },
              ]}
              className="select-cust"
            >
              <Input
                type="search"
                name="bookmark"
                value={formValues.ticketTypeName}
                disabled={true}
                placeholder={t("layers:selectOrderType")}
              />
            </Form.Item>

            <Form.Item
              label={"وصف الطلب"}
              rules={[
                {
                  message: "ابحث برقم الطلب",
                  required: true,
                },
              ]}
              className="select-cust"
              style={{ height: "auto" }}
            >
              <TextArea
                placeholder={t("layers:selectOrderDesc")}
                value={formValues.description}
                disabled={true}
                readOnly
                style={{
                  backgroundColor: "transparent",
                  border: "none",
                  color: "#284587",
                }}
              />
            </Form.Item>
            <Form.Item>
              <Upload
                label={"إضافة صور/ملفات اخري"}
                name="attachment"
                value={selectedAttachments.map((file) => file.path)}
                isView={true}
              />
            </Form.Item>
            {/* <Form.Item>
              <Tabs defaultActiveKey="1">
                <TabPane tab="الصور" key="1">
                  {getGalleryItems(selectedAttachments).length > 0 ? (
                    <ImageGallery
                      items={getGalleryItems(selectedAttachments)}
                      showThumbnails={true}
                      showFullscreenButton={true}
                      showPlayButton={false}
                    />
                  ) : (
                    <p>لا توجد صور مرفقة</p>
                  )}
                </TabPane>
                <TabPane tab="PDF" key="2">
                  {getPdfLinks(selectedAttachments).length > 0 ? (
                    getPdfLinks(selectedAttachments).map((file, idx) => (
                      <a
                        key={idx}
                        href={`${window.filesURL}${file.path}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{ display: "block", marginBottom: "5px" }}
                      >
                        {file.fileName}
                      </a>
                    ))
                  ) : (
                    <p>لا توجد ملفات PDF</p>
                  )}
                </TabPane>
              </Tabs>
            </Form.Item> */}

            {/* <Button
              className="addMark mt-2 "
              size="large"
              htmlType="submit"
              // disabled={formValues.bookmark !== "" ? false : true}
            >
              إرسال
            </Button> */}
          </Form>
        </Grid>
        {/* <Grid item xs={12} style={{ paddingTop: "0", paddingBottom: "0" }}>
          <FloatingLabelTextarea
            label={t("layers:orderDesc")}
            placeholder={t("layers:selectOrderDesc")}
            // helperText={
            //   formTouched.description && formErrors.description[0]
            // }
            value={formValues.description}
            disabled={true}
          />
        </Grid> */}
      </Grid>
    </div>
  );
};

export default UpdateDataLandView;
