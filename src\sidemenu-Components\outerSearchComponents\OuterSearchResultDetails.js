import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Table } from "react-bootstrap";
import { Tab, Tabs, Tab<PERSON><PERSON>, TabPanel } from "react-tabs";
import "react-tabs/style/react-tabs.css";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import axios from "axios";
import {
  faArchive,
  faInfo,
  faSearchPlus,
} from "@fortawesome/free-solid-svg-icons";
import IconButton from "@mui/material/IconButton";
import archive from "../../assets/icons/archieve.svg";
import zoom from "../../assets/icons/zoom.svg";
import { Tooltip } from "@mui/material";
import moment from "moment-hijri";
import PlanDataModal from "../../tables/Modals/PlanLandsData/PlanDataModal";
// import { layersSetting } from "../../helper/layers";
import * as webMercatorUtils from "@arcgis/core/geometry/support/webMercatorUtils.js";
import Point from "@arcgis/core/geometry/Point";
import {
  clearGraphicLayer,
  getFeatureDomainName,
  getLayerId,
  navigateToGoogle,
  convertToArabic,
  queryTask,
  showLoading,
  zoomToFeatureByObjectId,
  zoomToFeatureDefault,
  zoomToFeatureBySpatialID,
  zoomToFeatureByFilter,
} from "../../helper/common_func";

import googleLocation from "../../assets/icons/google.svg";
// import zoom from "../../assets/icons/googleLocation.svg";
import info from "../../assets/icons/info.svg";
import GalleryArchive from "../../assets/images/gallery archive.svg";
import { useTranslation } from "react-i18next";
import {
  getDateFromConcatNumbers,
  isNumber,
  notificationMessage,
  showDataSplittedBySlash,
} from "../../helper/utilsFunc";
import { toArabic } from "arabic-digits";
import { PARCEL_LANDS_LAYER_NAME } from "../../helper/constants";
import { message } from "antd";
import {
  alphbetNumbers,
  getNumbersInStringsByValue,
  megawrahNumbersInString,
  municipilitiesForRoyal,
  randomPlanNumbers,
  subdivisionTypes,
} from "../../helper/layers";
import ArchiveModal from "../../tables/Modals/ArchiveModal/ArchiveModal";
import PdfViewer from "../../components/PdfViewer/PdfViewer";
import DistrictDataModal from "../../tables/Modals/DistrictLandsData/DistrictDataModal";

function OuterSearchResultDetails(props) {
  const [dependcyLayer, setDependcyLayer] = useState(null);
  const [planDataModal, setPlanDataModal] = React.useState();
  const [districtDataModal, setDistrictDataModal] = React.useState();
  const [archiveDataModal, setArchiveDataModal] = React.useState();
  const [loading, setLoading] = React.useState(false);
  const { t } = useTranslation("map", "common", "layers");
  const isPrivateRoyalSalesLands = [
    "PARCEL_PRIVACY",
    "LGR_ROYAL",
    "SALES_LANDS",
  ].includes(props.outerSearchResult?.layerName);
  const [iconsData] = useState(() => {
    let reqIcons = [
      {
        id: 1,
        icon: zoom,
        tooltip: "zoomIn",
        data: "Zoom Data",
      },
      ...(props?.mainData?.layers[
        isPrivateRoyalSalesLands
          ? PARCEL_LANDS_LAYER_NAME
          : props.data?.layerName
      ]?.dependecies?.filter((dep) => {
        let landParcelLayer = props.data.layerName === "Landbase_Parcel";
        let isOwnerTypeExist =
          dep.showingField === "OWNER_TYPE" &&
          props.data?.OWNER_TYPE_Code === dep?.codeValue;
        if (landParcelLayer && isOwnerTypeExist) return dep;
        else if (
          (dep.showingField !== "OWNER_TYPE" &&
            (!dep.showingField || !isPrivateRoyalSalesLands)) ||
          (isPrivateRoyalSalesLands &&
            (dep.name === props.data.layerName || dep.isLandsRelevant))
        )
          return dep;
        else return undefined;
      }) || []),
    ];

    if (
      [
        "District_Boundary",
        "Plan_Data",
        "Municipality_Boundary",
        "Landbase_Parcel",
      ].includes(props.data?.layerName) &&
      props?.mainData?.logged &&
      props?.mainData?.user
      //&&props?.mainData?.layers["construction_license"]
    ) {
      reqIcons.push({
        id: 10,
        icon: archive,
        tooltip: "archive",
        data: "Archive Data",
        name: "Archive Data",
      });
    } else if (isPrivateRoyalSalesLands) {
      //تخصيص ومنح
      reqIcons.push({
        id: 23,
        imgIconSrc: GalleryArchive,
        tooltip: "GalleryArchive",
        data: "GalleryArchive",
        name: "GalleryArchive",
      });
    } else if (props.data?.layerName === "UnplannedParcels") {
      reqIcons.push({
        id: 10,
        icon: archive,
        tooltip: "unplannedLandArchive",
        data: "UnplannedParcels",
        name: "UnplannedParcels",
      });
    }

    if (isPrivateRoyalSalesLands) {
      reqIcons = reqIcons.filter(
        (ic) =>
          ![
            "KROKY_SUBMISSIONS",
            "FARZ_SUBMISSIONS",
            "CONTRACT_UPDATE_SUBMISSIONS",
            "ZAWAYED_SUBMISSIONS",
            "SERVICE_PROJECTS_SUBMISSIONS",
          ].includes(ic.name)
      );
    }
    return reqIcons;
  });
  const [selectedTab, setSelectedTab] = useState(() => {
    if (!isPrivateRoyalSalesLands) return 0;
    else {
      let layerName = props.data.layerName;
      let reqIndex = iconsData.findIndex((i) => i.name === layerName) + 2;
      return reqIndex;
    }
  });

  const makeClickAction = (index, prevIndex) => {
    let layersSetting = props.mainData.layers;
    let isNotAPIFetchIcon = true;
    // 1,2 is google and zoom
    if ([1, 2].includes(index)) {
      if (!isPrivateRoyalSalesLands) {
        if (props.data?.geometry) {
          if (index === 1) {
            if (props.data.geometry.paths){
              const point  = new Point({
                x: props.data.geometry.paths[0][0][0],
                y: props.data.geometry.paths[0][0][1],
                spatialReference: {
                  wkid: 3857,
                }
              })

           const geographicPoint =  webMercatorUtils.webMercatorToGeographic(point);

          // Extract latitude and longitude
          const latitude = geographicPoint.latitude;
          const longitude = geographicPoint.longitude;

          // Use the coordinates in your Google Maps function
          navigateToGoogle(latitude, longitude);
            }else{
                navigateToGoogle(
                props.data.geometry.latitude ||
                  props.data.geometry.centroid.latitude ,
                props.data.geometry.longitude ||
                  props.data.geometry.centroid.longitude
              );
            }
          } else if (index === 2) {
            zoomToFeatureDefault(props.data, props.map);
          }
        } else
          zoomToFeatureByObjectId(
            props.data,
            props.map,
            props.data.isNotZoom,
            (feature) => {
              props.data.geometry = feature.geometry;
              if (
                !(
                  feature.geometry?.rings?.length ||
                  feature?.geometry?.paths?.length ||
                  feature?.geometry?.x
                )
              ) {
                message.open({
                  type: "info",
                  content: [
                    "PARCEL_PRIVACY",
                    "LGR_ROYAL",
                    "Landbase_Parcel",
                    "SALES_LANDS",
                  ].includes(props?.data?.layerName)
                    ? t("common:doesntReflectGDB")
                    : t("common:featureDoesntReflectGDB"),
                });
                return;
              }
              if (index === 1) {
                navigateToGoogle(
                  props.data.geometry.latitude ||
                    props.data.geometry.centroid.latitude,
                  props.data.geometry.longitude ||
                    props.data.geometry.centroid.longitude
                );
              } else if (index === 2) {
                zoomToFeatureDefault(props.data, props.map);
              }
            }
          );
      } else {
        const isPrivateLand = ["PARCEL_PRIVACY"].includes(
          props.outerSearchResult?.layerName
        );
        if (isPrivateLand) {
          let hasLandBaseParcelData =
            props.resultDetailsDataRef.current?.landBaseParcelData;

          if (!props?.data?.geometry) {
            if (hasLandBaseParcelData && hasLandBaseParcelData?.geometry) {
              if (index === 1) {
                navigateToGoogle(
                  hasLandBaseParcelData.geometry.latitude ||
                    hasLandBaseParcelData.geometry.centroid.latitude,
                  hasLandBaseParcelData.geometry.longitude ||
                    hasLandBaseParcelData.geometry.centroid.longitude
                );
              } else if (index === 2) {
                zoomToFeatureDefault(hasLandBaseParcelData, props.map);
              }
            } else if (
              hasLandBaseParcelData &&
              !hasLandBaseParcelData?.geometry
            )
              zoomToFeatureByObjectId(
                hasLandBaseParcelData,
                props.map,
                props.data.isNotZoom,
                (feature) => {
                  hasLandBaseParcelData.geometry = feature.geometry;
                  props.data.geometry = feature.geometry;
                  if (
                    !(
                      feature.geometry?.rings?.length ||
                      feature?.geometry?.paths?.length ||
                      feature?.geometry?.x
                    )
                  ) {
                    message.open({
                      type: "info",
                      content: t("common:doesntReflectGDB"),
                    });
                    return;
                  }
                  if (index === 1) {
                    navigateToGoogle(
                      hasLandBaseParcelData.geometry.latitude ||
                        hasLandBaseParcelData.geometry.centroid.latitude,
                      hasLandBaseParcelData.geometry.longitude ||
                        hasLandBaseParcelData.geometry.centroid.longitude
                    );
                  } else if (index === 2) {
                    zoomToFeatureDefault(hasLandBaseParcelData, props.map);
                  }
                }
              );
            else if (!hasLandBaseParcelData && props.data?.id) {
              zoomToFeatureBySpatialID(
                props.data,
                PARCEL_LANDS_LAYER_NAME,
                props.map,
                false,
                (data) => {
                  // props.setLandBaseParcelData(data);
                  props.resultDetailsDataRef.current.landBaseParcelData = data;
                  if (
                    !(
                      data?.geometry?.rings?.length ||
                      data?.geometry?.paths?.length ||
                      data?.geometry?.x
                    )
                  ) {
                    message.open({
                      type: "info",
                      content: t("common:doesntReflectGDB"),
                    });
                    return;
                  }
                  if (index === 1) {
                    navigateToGoogle(
                      data.geometry.latitude || data.geometry.centroid.latitude,
                      data.geometry.longitude ||
                        data.geometry.centroid.longitude
                    );
                  } else if (index === 2) {
                    zoomToFeatureDefault(data, props.map);
                  }
                },
                () =>
                  message.open({
                    type: "info",
                    content: t("common:retrievError"),
                  })
              );
            } else
              message.open({
                type: "info",
                content: t("common:doesntReflectGDB"),
              });
          } else {
            if (
              !(
                props?.data.geometry?.rings?.length ||
                props?.data?.geometry?.paths?.length ||
                props?.data?.geometry?.x
              )
            ) {
              message.open({
                type: "info",
                content: t("common:doesntReflectGDB"),
              });
              return;
            }
            zoomToFeatureDefault(props.data, props.map);
          }
        } else {
          //royal parcels .
          let hasLandBaseParcelData =
            props.resultDetailsDataRef.current?.landBaseParcelData;
          let royalSalesLandWhereClause = getWhereClauseFromRoyalLandInfo(
            props.data,
            props.outerSearchResult?.layerName === "LGR_ROYAL"
          );

          if (!props?.data?.geometry) {
            if (hasLandBaseParcelData && hasLandBaseParcelData?.geometry) {
              if (index === 1) {
                navigateToGoogle(
                  hasLandBaseParcelData.geometry.latitude ||
                    hasLandBaseParcelData.geometry.centroid.latitude,
                  hasLandBaseParcelData.geometry.longitude ||
                    hasLandBaseParcelData.geometry.centroid.longitude
                );
              } else if (index === 2) {
                zoomToFeatureDefault(hasLandBaseParcelData, props.map);
              }
            } else if (
              hasLandBaseParcelData &&
              !hasLandBaseParcelData?.geometry
            )
              zoomToFeatureByObjectId(
                hasLandBaseParcelData,
                props.map,
                props.data.isNotZoom,
                (feature) => {
                  hasLandBaseParcelData.geometry = feature.geometry;
                  props.data.geometry = feature.geometry;
                  if (
                    !(
                      feature.geometry?.rings?.length ||
                      feature?.geometry?.paths?.length ||
                      feature?.geometry?.x
                    )
                  ) {
                    message.open({
                      type: "info",
                      content: t("common:doesntReflectGDB"),
                    });
                    return;
                  }
                  if (index === 1) {
                    navigateToGoogle(
                      hasLandBaseParcelData.geometry.latitude ||
                        hasLandBaseParcelData.geometry.centroid.latitude,
                      hasLandBaseParcelData.geometry.longitude ||
                        hasLandBaseParcelData.geometry.centroid.longitude
                    );
                  } else if (index === 2) {
                    zoomToFeatureDefault(hasLandBaseParcelData, props.map);
                  }
                }
              );
            else if (!hasLandBaseParcelData && props.data?.id) {
              zoomToFeatureBySpatialID(
                props.data,
                PARCEL_LANDS_LAYER_NAME,
                props.map,
                false,
                (data) => {
                  // props.setLandBaseParcelData(data);
                  props.resultDetailsDataRef.current.landBaseParcelData = data;
                  if (
                    !(
                      data?.geometry?.rings?.length ||
                      data?.geometry?.paths?.length ||
                      data?.geometry?.x
                    )
                  ) {
                    message.open({
                      type: "info",
                      content: t("common:doesntReflectGDB"),
                    });
                    return;
                  }
                  if (index === 1) {
                    navigateToGoogle(
                      data.geometry.latitude || data.geometry.centroid.latitude,
                      data.geometry.longitude ||
                        data.geometry.centroid.longitude
                    );
                  } else if (index === 2) {
                    zoomToFeatureDefault(data, props.map);
                  }
                },
                () =>
                  message.open({
                    type: "info",
                    content: t("common:retrievError"),
                  })
              );
            } else if (
              props.data &&
              props.data?.land_no &&
              royalSalesLandWhereClause
            ) {
              zoomToFeatureByFilter(
                // isOldLandsBlock ? where +` and PLAN_NO IN ('${randomPlanNumbers.join("','")}')`:
                royalSalesLandWhereClause,
                PARCEL_LANDS_LAYER_NAME,
                props.map,
                null,
                (data) => {
                  // props.setLandBaseParcelData(data);
                  props.resultDetailsDataRef.current.landBaseParcelData = data;
                  if (
                    !(
                      data?.geometry?.rings?.length ||
                      data?.geometry?.paths?.length ||
                      data?.geometry?.x
                    )
                  ) {
                    message.open({
                      type: "info",
                      content: t("common:doesntReflectGDB"),
                    });
                    return;
                  }
                  if (index === 1) {
                    navigateToGoogle(
                      data.geometry.latitude || data.geometry.centroid.latitude,
                      data.geometry.longitude ||
                        data.geometry.centroid.longitude
                    );
                  } else if (index === 2) {
                    zoomToFeatureDefault(data, props.map);
                  }
                },
                "*"
              );
            } else
              message.open({
                type: "info",
                content: t("common:doesntReflectGDB"),
              });
          } else {
            if (
              !(
                props?.data.geometry?.rings?.length ||
                props?.data?.geometry?.paths?.length ||
                props?.data?.geometry?.x
              )
            ) {
              message.open({
                type: "info",
                content: t("common:doesntReflectGDB"),
              });
              return;
            }
            zoomToFeatureDefault(props.data, props.map);
          }
        }
      }
    }
    //get dependcy
    else if (index > 2) {
      let dependency = iconsData[index - 2];
      let layerdId = getLayerId(props.map.__mapInfo, dependency.name);
      let field = "";
      if (dependency.depName === "akarReportBtn") {
        if (isPrivateRoyalSalesLands && !props?.data["id"]) {
          notificationMessage("لا يوجد استمارة تملك لهذه الأرض", 5);
          return;
        }
        localStorage.setItem(
          "akarReportLandId",
          `PARCEL_SPATIAL_ID=${
            props?.data["PARCEL_SPATIAL_ID"] || props?.data["id"]
          }`
        );
        window.open(
          `${window.location.origin}/eexplorer` + dependency?.redirectURL,
          "_blank",
          "rel=noopener noreferrer"
        );
        return;
      } else if (dependency.depName === "gisAkarReportBtn") {
        if (isPrivateRoyalSalesLands && !props?.data["id"]) {
          notificationMessage("لا يوجد استمارة تملك لهذه الأرض", 5);
          return;
        }
        localStorage.setItem(
          "akarReportLandId",
          `PARCEL_SPATIAL_ID=${
            props?.data["PARCEL_SPATIAL_ID"] || props?.data["id"]
          }`
        );
        window.open(
          `${window.location.origin}/eexplorer` + dependency?.redirectURL,
          "_blank",
          "rel=noopener noreferrer"
        );
        return;
      }
      if (
        [
          "LGR_ROYAL",
          "PARCEL_PRIVACY",
          "SALES_LANDS",
          "KROKY_SUBMISSIONS",
          "FARZ_SUBMISSIONS",
          "CONTRACT_UPDATE_SUBMISSIONS",
          "ZAWAYED_SUBMISSIONS",
          "SERVICE_PROJECTS_SUBMISSIONS",
          "LICENSE_INFO",
        ].includes(dependency.name)
      ) {
        let isDepPrivateRoyalSales = [
          "LGR_ROYAL",
          "PARCEL_PRIVACY",
          "SALES_LANDS",
        ].includes(dependency.name);
        if (!(isDepPrivateRoyalSales && isPrivateRoyalSalesLands)) {
          //CHECK IF isPrivateOrRoyal true ---> check if landBaseParcelData is exsiting or not
          let reqData = isPrivateRoyalSalesLands
            ? props.resultDetailsDataRef.current?.landBaseParcelData?.attributes
            : props.data;
          //in case of reqData is existing
          isNotAPIFetchIcon = false;
          if (reqData) getDepDataAPIDepend(dependency, reqData, index);
          //TODO: get the landbasedata first
          else {
            if (!props.data?.id) setDependcyLayer([]);
            else {
              zoomToFeatureBySpatialID(
                props.data,
                PARCEL_LANDS_LAYER_NAME,
                props.map,
                false,
                (featData) => {
                  // props.setLandBaseParcelData(featData);
                  props.resultDetailsDataRef.current.landBaseParcelData =
                    featData;
                  if (featData)
                    getDepDataAPIDepend(dependency, featData, index);
                  else {
                    setDependcyLayer([]);

                    message.open({
                      type: "info",
                      content: [
                        "PARCEL_PRIVACY",
                        "LGR_ROYAL",
                        "Landbase_Parcel",
                        "SALES_LANDS",
                      ].includes(props?.data?.layerName)
                        ? t("common:doesntReflectGDB")
                        : t("common:featureDoesntReflectGDB"),
                    });
                    return;
                  }
                },
                () =>
                  message.open({
                    type: "info",
                    content: t("common:retrievError"),
                  })
              );
            }
          }
        } else {
          //in case of isPrivateOrRoyal is true and clicked on one of them
          let mappingRes = [
            {
              layerName: props.data?.layerName,
              id: "",
              attributes: { ...props.data },
              isExternalFetchedData: true,
            },
          ];
          setDependcyLayer(mappingRes);
          // props.outerOpenResultdetails(mappingRes);
        }
      } else if (dependency?.depName === "LandStatistics") {
        //todo: showing plan lands data
        let reqData = isPrivateRoyalSalesLands
          ? props.resultDetailsDataRef.current?.landBaseParcelData?.attributes
          : props.data;
        if (reqData) {
          setPlanDataModal({
            PLAN_SPATIAL_ID: reqData["PLAN_SPATIAL_ID"],
            PLAN_NO: reqData["PLAN_NO"],
            MUNICIPALITY_NAME: reqData["MUNICIPALITY_NAME_Code"],
          });
        } else {
          if (!props.data?.id) setDependcyLayer([]);
          else {
            zoomToFeatureBySpatialID(
              props.data,
              PARCEL_LANDS_LAYER_NAME,
              props.map,
              false,
              (featData) => {
                // props.setLandBaseParcelData(featData);
                props.resultDetailsDataRef.current.landBaseParcelData =
                  featData;
                if (featData)
                  setPlanDataModal({
                    PLAN_SPATIAL_ID: featData["PLAN_SPATIAL_ID"],
                    PLAN_NO: featData["PLAN_NO"],
                    MUNICIPALITY_NAME: featData["MUNICIPALITY_NAME_Code"],
                  });
                else {
                  message.open({
                    type: "info",
                    content: [
                      "PARCEL_PRIVACY",
                      "LGR_ROYAL",
                      "Landbase_Parcel",
                      "SALES_LANDS",
                    ].includes(props?.data?.layerName)
                      ? t("common:doesntReflectGDB")
                      : t("common:featureDoesntReflectGDB"),
                  });
                  return;
                }
              },
              () =>
                message.open({
                  type: "info",
                  content: t("common:retrievError"),
                })
            );
          }
        }
      } else if (dependency?.depName === "districtLandStatistics") {
        let reqData = isPrivateRoyalSalesLands
          ? props.resultDetailsDataRef.current?.landBaseParcelData?.attributes
          : props.data;
        if (reqData) {
          setDistrictDataModal({
            value: reqData["DISTRICT_NAME"],
            DISTRICT_NAME: reqData["DISTRICT_NAME_Code"],
            MUNICIPALITY_NAME: reqData["MUNICIPALITY_NAME_Code"],
          });
        } else {
          if (!props.data?.id) setDependcyLayer([]);
          else {
            zoomToFeatureBySpatialID(
              props.data,
              PARCEL_LANDS_LAYER_NAME,
              props.map,
              false,
              (featData) => {
                // props.setLandBaseParcelData(featData);
                props.resultDetailsDataRef.current.landBaseParcelData =
                  featData;
                if (featData)
                  setDistrictDataModal({
                    value: featData["DISTRICT_NAME"],
                    DISTRICT_NAME: featData["DISTRICT_NAME_Code"],
                    MUNICIPALITY_NAME: featData["MUNICIPALITY_NAME_Code"],
                  });
                else {
                  message.open({
                    type: "info",
                    content: [
                      "PARCEL_PRIVACY",
                      "LGR_ROYAL",
                      "Landbase_Parcel",
                      "SALES_LANDS",
                    ].includes(props?.data?.layerName)
                      ? t("common:doesntReflectGDB")
                      : t("common:featureDoesntReflectGDB"),
                  });
                  return;
                }
              },
              () =>
                message.open({
                  type: "info",
                  content: t("common:retrievError"),
                })
            );
          }
        }
      } else if (dependency.name === "GalleryArchive") {
        let isDepRoyalLandLayer = ["LGR_ROYAL"].includes(
          props?.data?.layerName
        );
        let isDepSalesLandLayer = ["SALES_LANDS"].includes(
          props?.data?.layerName
        );
        let param = props.data["arc_serial"];
        if (param) {
          let url =
            window.webApiUrl + `GetAllocationArchiveFiles?arcSerial=${param}`;
          if (isDepRoyalLandLayer) url += "&isgrant=true";
          else if (isDepSalesLandLayer) url += "&isSale=true";
          let token = props.mainData.user?.token;
          axios
            .get(url, {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            })
            .then((res) => {
              let galleryData = res.data;
              setArchiveDataModal(galleryData);
            })
            .catch((err) => {
              console.log(err);
              showLoading(false);
              notificationMessage("حدث خطأ أثناء استرجاع البيانات", 5);
            });
        } else {
          notificationMessage("لا يوجد صور للأرشيف لهذه الأرض", 5);
        }
      } else {
        if (dependency.name === "Archive Data") {
          let queryParams = "";
          let munName = props?.data["MUNICIPALITY_NAME"];
          if (munName)
            queryParams += queryParams
              ? `&ProvinceName=${munName}`
              : `ProvinceName=${munName}`;
          let districtName = props?.data["DISTRICT_NAME"];
          if (districtName)
            queryParams += queryParams
              ? `&DistrictName=${districtName}`
              : `DistrictName=${districtName}`;
          let planNo = props?.data["PLAN_NO"];
          if (planNo)
            queryParams += queryParams
              ? `&PlanNo=${planNo}`
              : `PlanNo=${planNo}`;
          let parcelNo = props?.data["PARCEL_PLAN_NO"];
          if (parcelNo)
            queryParams += queryParams
              ? `&LandNo=${parcelNo}`
              : `LandNo=${parcelNo}`;
          // * local storage approach to save data archive query
          // localStorage.setItem("archiveParams",queryParams);
          // window.open("/eexplorer/Archive", "_blank").focus();
          //******************* */
          // * query string approach to save data archive query
          window.open("/eexplorer/Archive?" + queryParams, "_blank").focus();
        } else if (dependency.name === "UnplannedParcels") {
          console.log("unplannedParcelArchive");
          // getDepDataAPIDepend(dependency, props.data, index)
          let url =
            window.ApiUrl +
            `/GetCorrespondenceInfo?displayNo=${props.data["SUBMISSIONNO"]}`;
          let token = props.mainData.user?.token;
          axios
            .get(url, {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            })
            .then((res) => {
              let archiveData = res.data;
              setArchiveDataModal(archiveData);
            })
            .catch((err) => {
              console.log(err);
              showLoading(false);
              notificationMessage("حدث خطأ أثناء استرجاع البيانات", 5);
            });
        } else {
          let reqData = isPrivateRoyalSalesLands
            ? props.resultDetailsDataRef.current.landBaseParcelData?.attributes
            : props.data;
          if (reqData) {
            if (dependency.filter) {
              field = dependency.filter;
            } else field = layersSetting[dependency.name].filter;

            let where = field + "= '" + reqData[field] + "'";

            queryTask({
              url: window.mapUrl + "/" + layerdId,
              outFields: layersSetting[dependency.name].outFields,
              where: where,
              callbackResult: ({ features }) => {
                if (features.length) {
                  getFeatureDomainName(features, layerdId).then((res) => {
                    res.forEach((r) => {
                      r.attributes.layerName = dependency.name;
                    });

                    if (dependency.isTable) {
                      setDependcyLayer(res[0]);
                    } else {
                      let mappingRes = res.map((f) => {
                        return {
                          layerName: dependency.name,
                          id: f.attributes["OBJECTID"],
                          ...f.attributes,
                        };
                      });

                      props.setOuterSearchResult(mappingRes);

                      if (res.length > 1) {
                        props.generalOpenResultMenu();
                      } else {
                        props.outerOpenResultdetails(mappingRes[0]);
                      }
                    }
                  });
                } else {
                  setDependcyLayer([]);
                }
              },
            });
          } else {
            if (!props.data?.id) setDependcyLayer([]);
            else {
              zoomToFeatureBySpatialID(
                props.data,
                PARCEL_LANDS_LAYER_NAME,
                props.map,
                false,
                (featData) => {
                  // props.setLandBaseParcelData(featData);
                  props.resultDetailsDataRef.current.landBaseParcelData =
                    featData;
                  if (featData) {
                    if (dependency.filter) {
                      field = dependency.filter;
                    } else field = layersSetting[dependency.name].filter;

                    let where = field + "= '" + featData[field] + "'";

                    queryTask({
                      url: window.mapUrl + "/" + layerdId,
                      outFields: layersSetting[dependency.name].outFields,
                      where: where,
                      callbackResult: ({ features }) => {
                        if (features.length) {
                          getFeatureDomainName(features, layerdId).then(
                            (res) => {
                              res.forEach((r) => {
                                r.attributes.layerName = dependency.name;
                              });

                              if (dependency.isTable) {
                                setDependcyLayer(res[0]);
                              } else {
                                let mappingRes = res.map((f) => {
                                  return {
                                    layerName: dependency.name,
                                    id: f.attributes["OBJECTID"],
                                    ...f.attributes,
                                  };
                                });

                                props.setOuterSearchResult(mappingRes);

                                if (res.length > 1) {
                                  props.generalOpenResultMenu();
                                } else {
                                  props.outerOpenResultdetails(mappingRes[0]);
                                }
                              }
                            }
                          );
                        } else {
                          setDependcyLayer([]);
                        }
                      },
                    });
                  } else {
                    setDependcyLayer([]);

                    message.open({
                      type: "info",
                      content: [
                        "PARCEL_PRIVACY",
                        "LGR_ROYAL",
                        "Landbase_Parcel",
                        "SALES_LANDS",
                      ].includes(props?.data?.layerName)
                        ? t("common:doesntReflectGDB")
                        : t("common:featureDoesntReflectGDB"),
                    });
                    return;
                  }
                },
                () =>
                  message.open({
                    type: "info",
                    content: t("common:retrievError"),
                  })
              );
            }
          }
        }
      }
    } else if (index === 0 && isPrivateRoyalSalesLands) {
      //CHECK IF isPrivateOrRoyal true ---> check if landBaseParcelData is exsiting or not
      let reqData = isPrivateRoyalSalesLands
        ? props.resultDetailsDataRef.current?.landBaseParcelData?.attributes
        : props.data;
      //in case of reqData is existing
      if (reqData) {
        setDependcyLayer([]);
      }
      //TODO: get the landbasedata first
      else {
        const isPrivateLand = ["PARCEL_PRIVACY"].includes(
          props.outerSearchResult?.layerName
        );
        if (isPrivateLand) {
          if (!props.data.id) setDependcyLayer([]);
          else {
            setLoading(true);
            zoomToFeatureBySpatialID(
              props.data,
              PARCEL_LANDS_LAYER_NAME,
              props.map,
              false,
              (featData) => {
                // props.setLandBaseParcelData(featData);
                props.resultDetailsDataRef.current.landBaseParcelData =
                  featData;

                setLoading(false);
              },
              () => {
                setLoading(false);

                message.open({
                  type: "info",
                  content: t("common:retrievError"),
                });
              },
              true
            );
          }
        } else {
          let royalSalesLandWhereClause = getWhereClauseFromRoyalLandInfo(
            props.data,
            props.outerSearchResult?.layerName === "LGR_ROYAL"
          );
          if (
            !(
              props.data.id ||
              (props.data.land_no && royalSalesLandWhereClause)
            )
          )
            setDependcyLayer([]);
          else if (props.data.id) {
            setLoading(true);
            zoomToFeatureBySpatialID(
              props.data,
              PARCEL_LANDS_LAYER_NAME,
              props.map,
              false,
              (featData) => {
                // props.setLandBaseParcelData(featData);
                props.resultDetailsDataRef.current.landBaseParcelData =
                  featData;

                setLoading(false);
              },
              () => {
                setLoading(false);

                message.open({
                  type: "info",
                  content: t("common:retrievError"),
                });
              },
              true
            );
          } else if (
            !props.data.id &&
            props.data.land_no &&
            royalSalesLandWhereClause
          ) {
            zoomToFeatureByFilter(
              // isOldLandsBlock ? where +` and PLAN_NO IN ('${randomPlanNumbers.join("','")}')`:
              royalSalesLandWhereClause,
              PARCEL_LANDS_LAYER_NAME,
              props.map,
              null,
              (data) => {
                // props.setLandBaseParcelData(data);
                props.resultDetailsDataRef.current.landBaseParcelData = data;
                if (
                  !(
                    data?.geometry?.rings?.length ||
                    data?.geometry?.paths?.length ||
                    data?.geometry?.x
                  )
                ) {
                  message.open({
                    type: "info",
                    content: t("common:doesntReflectGDB"),
                  });
                  return;
                }
              },
              "*"
            );
          }
        }
      }
    }

    let dependency = iconsData[index - 2] || {};

    if (
      ["LandStatistics", "districtLandStatistics"].includes(
        dependency?.depName
      ) ||
      ["GalleryArchive", "UnplannedParcels"].includes(dependency?.name)
    )
      return;
    else if (
      ![1, 2].includes(index) &&
      isNotAPIFetchIcon &&
      dependency?.name !== "Archive Data"
    ) {
      setSelectedTab(index);
      if (!index) setDependcyLayer([]);
    }
  };
  const getDepDataAPIDepend = (dependencyData, reqData, index) => {
    showLoading(true);
    let layersSetting = props.mainData.layers;

    let token = props.mainData.user?.token;
    let requestURL = "",
      field = "";
    if (
      ["LGR_ROYAL", "PARCEL_PRIVACY", "SALES_LANDS"].includes(
        dependencyData.name
      )
    ) {
      if (dependencyData.filter) {
        field = dependencyData.filter;
      } else field = layersSetting[dependencyData.name].filter;
      requestURL = dependencyData.url + reqData[field];
    } else if (dependencyData.name === "LICENSE_INFO") {
      requestURL = dependencyData.url + reqData.PARCEL_SPATIAL_ID;
    } else {
      let parcel_no = reqData["PARCEL_PLAN_NO"];
      let mun_no = reqData["MUNICIPALITY_NAME_Code"];
      let plan_no = reqData["PLAN_NO"];
      let block_no = reqData["PARCEL_BLOCK_NO"] || "";
      let subdivision_type = reqData["SUBDIVISION_TYPE_Code"] || "";
      let subdivision_Desc = reqData["SUBDIVISION_DESCRIPTION"] || "";
      requestURL =
        dependencyData.url +
        `?Parcel_no=${parcel_no}&Mun_code=${mun_no}&plan_no=${plan_no}&block_no=${block_no}&subdivision_Desc=${subdivision_Desc}&subdivision_type=${subdivision_type}`;
    }
    axios
      .get(requestURL, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      .then((res) => {
        let data = res.data || [];
        if (["PARCEL_PRIVACY", "SALES_LANDS"].includes(dependencyData.name))
          data = data?.results || [];
        if (data.length) {
          data = data.map((item) => {
            return {
              attributes: { ...item },
            };
          });
          let mappingRes = data.map((f) => {
            return {
              layerName: dependencyData.name,
              id: f.attributes["OBJECTID"] || f.attributes["fileId"],
              attributes: { ...f.attributes },
              isExternalFetchedData: true,
            };
          });
          setDependcyLayer(mappingRes);

          // props.outerOpenResultdetails(mappingRes);
        } else {
          setDependcyLayer([]);
        }
        setSelectedTab(index);
        showLoading(false);
      })
      .catch((err) => {
        console.log(err);
        showLoading(false);
        notificationMessage("حدث خطأ أثناء استرجاع البيانات", 5);
      });
  };

  useEffect(() => {
    console.log("mount the component");
    let hasLandBaseParcelData =
      props.resultDetailsDataRef.current?.landBaseParcelData;
    if (isPrivateRoyalSalesLands) {
      //set dependency layer
      let mappingRes = [
        {
          layerName: props.data?.layerName,
          id: "",
          attributes: { ...props.data },
          isExternalFetchedData: true,
        },
      ];
      setDependcyLayer(mappingRes);
      // props.outerOpenResultdetails(mappingRes);

      let royalSalesLandWhereClause = ["LGR_ROYAL", "SALES_LANDS"].includes(
        props.outerSearchResult?.layerName
      )
        ? getWhereClauseFromRoyalLandInfo(
            props.data,
            props.outerSearchResult?.layerName === "LGR_ROYAL"
          )
        : "";

      //zoom stuff
      let data = hasLandBaseParcelData || props.data;
      if (data && !data?.isNotZoom) {
        // console.log("detail data", props.data);
        if (!data.geometry) {
          if (hasLandBaseParcelData)
            zoomToFeatureByObjectId(props.data, props.map, false, (feature) => {
              hasLandBaseParcelData.geometry = feature.geometry;
            });
          else if (!hasLandBaseParcelData && props.data?.id) {
            zoomToFeatureBySpatialID(
              props.data,
              PARCEL_LANDS_LAYER_NAME,
              props.map,
              false,
              (featData) => {
                props.resultDetailsDataRef.current.landBaseParcelData =
                  featData;
              },
              () =>
                message.open({
                  type: "info",
                  content: t("common:retrievError"),
                }),
              true
            );
          } else if (
            !props.data.id &&
            props.data.land_no &&
            royalSalesLandWhereClause &&
            ["LGR_ROYAL", "SALES_LANDS"].includes(
              props.outerSearchResult?.layerName
            )
          ) {
            // todo: get where clause from royal land info
            zoomToFeatureByFilter(
              // isOldLandsBlock ? where +` and PLAN_NO IN ('${randomPlanNumbers.join("','")}')`:
              royalSalesLandWhereClause,
              PARCEL_LANDS_LAYER_NAME,
              props.map,
              null,
              (data) => {
                props.resultDetailsDataRef.current.landBaseParcelData = data;
              },
              "*"
            );
          }
        } else zoomToFeatureDefault(props.data, props.map);
      }
    } else {
      if (props.data && !props.data.isNotZoom) {
        console.log("detail data", props.data);

        // console.log("detail data", props.data);
        if (!props.data?.geometry) {
          zoomToFeatureByObjectId(props.data, props.map, false, (feature) => {
            props.data.geometry = feature.geometry;
          });
        } else {
          if (
            props?.data?.geometry?.rings?.length ||
            props?.data?.geometry?.paths?.length ||
            props?.data?.geometry?.x
          )
            zoomToFeatureDefault(props.data, props.map);
        }
      }
    }

    return () => {
      clearGraphicLayer("ZoomGraphicLayer", props.map);
      // props.setLandBaseParcelData();
      props.resultDetailsDataRef.current.landBaseParcelData = null;
    };
  }, []);

  const getWhereClauseFromRoyalLandInfo = (landInfo, isRoyal) => {
    let where = isRoyal ? "USING_SYMBOL NOT LIKE '%خ%'" : "";
    let fieldsNames = [
      "city_name",
      "plan_no",
      "block_no",
      "land_no",
      "district_desc",
      "center_desc",
      "category_desc",
    ];
    let centerDescValue =
      landInfo["center_desc"] && landInfo["center_desc"] !== "بدون"
        ? landInfo["center_desc"]
        : ""; // todo: landInfo['center_desc'] && landInfo['center_desc'] !== 'بدون' ? landInfo['center_desc'] : '
    let districtDescValue =
      landInfo["district_desc"] && landInfo["district_desc"] !== "بدون"
        ? landInfo["district_desc"]
        : "";
    fieldsNames.forEach((fieldName) => {
      let munCode;
      // 1- mun code
      let fieldValue = landInfo[fieldName];
      if (fieldName === "city_name" && fieldValue && fieldValue !== "بدون") {
        if (fieldValue)
          munCode = municipilitiesForRoyal.find((i) => {
            if (fieldValue && fieldValue.includes(i.name)) return i;
            return i.name.includes(fieldValue);
          })?.GISCode;
        if (munCode)
          where += `${where ? " AND " : ""}MUNICIPALITY_NAME=${munCode} `;
      } else if (
        fieldName === "plan_no" &&
        fieldValue &&
        fieldValue !== "بدون"
      ) {
        if (fieldValue) {
          where += `${where ? " AND " : ""}PLAN_NO='${fieldValue}' `;
        }
      } else if (
        fieldName === "land_no" &&
        fieldValue &&
        fieldValue !== "بدون"
      ) {
        if (fieldValue) {
          where += `${where ? " AND " : ""}PARCEL_PLAN_NO='${fieldValue}' `;
        }
      } else if (
        fieldName === "block_no" &&
        fieldValue &&
        fieldValue !== "0" &&
        fieldValue !== "بدون"
      ) {
        if (fieldValue) {
          where += `${where ? " AND " : ""}PARCEL_BLOCK_NO='${fieldValue}' `;
        }
      } else if (
        fieldName === "district_desc" &&
        fieldValue &&
        fieldValue !== "0" &&
        fieldValue !== "بدون" &&
        !centerDescValue
      ) {
        let megawarahWhere = `SUBDIVISION_TYPE=${subdivisionTypes.megawrah} AND SUBDIVISION_DESCRIPTION LIKE '%مجاور%' `;
        let districtDescSplitStr = fieldValue.split(" ")?.slice(1);
        let districtNumber = districtDescSplitStr?.join(" ");
        let megawrahObj = getNumbersInStringsByValue(districtNumber);

        // let numbers = districtStringNumberByValue.match(/\d+/g);     //return array of matched
        // let megawrahObj = megawrahNumbersInString.find(i => numbers[0] === i.value);
        if (typeof megawrahObj.like === "string") {
          megawarahWhere += `${
            megawarahWhere ? " AND " : ""
          }  SUBDIVISION_DESCRIPTION LIKE '%${megawrahObj.like}%'`;
        } else {
          // like: [{OR:["أحد","حادي"]}, {AND:"عشر"}]

          if (megawrahObj.like?.OR && !megawrahObj.like?.AND) {
            megawarahWhere +=
              (megawarahWhere ? " AND " : "") +
              megawrahObj.like?.OR.map(
                (word) => `(SUBDIVISION_DESCRIPTION LIKE '%${word}%')`
              ).join(" OR ");
          } else if (megawrahObj.like?.OR && megawrahObj.like?.AND) {
            megawarahWhere +=
              (megawarahWhere ? " AND " : "") +
              megawrahObj.like?.OR.map(
                (word) =>
                  `(SUBDIVISION_DESCRIPTION LIKE '%${word}%' AND SUBDIVISION_DESCRIPTION LIKE '%${megawrahObj.like.AND}%')`
              ).join(" OR ");
          }
        }
        if (megawrahObj.NOT) {
          megawarahWhere += ` ${
            megawarahWhere ? " AND " : ""
          }  ( SUBDIVISION_DESCRIPTION NOT LIKE '%${megawrahObj.NOT}%')`;
        }
        where += (where ? " AND " : "") + `(${megawarahWhere})`;
      } else if (
        fieldName === "center_desc" &&
        fieldValue &&
        fieldValue !== "بدون"
      ) {
        // todo:
        let centerSplitStr = fieldValue.split(" ")?.slice(1);
        let centerNumber = centerSplitStr?.join(" ");
        let stringNumberByValue = getNumbersInStringsByValue(centerNumber);
        let centerWhere = "";
        if (typeof stringNumberByValue.like === "string") {
          centerWhere = ` (SUBDIVISION_DESCRIPTION LIKE '%${stringNumberByValue.like}%')`;
        } else {
          // like: [{OR:["أحد","حادي"]}, {AND:"عشر"}]

          if (stringNumberByValue.like?.OR && !stringNumberByValue.like?.AND) {
            centerWhere =
              (centerWhere ? " AND " : "") +
              stringNumberByValue.like?.OR.map(
                (word) => `(SUBDIVISION_DESCRIPTION LIKE '%${word}%')`
              ).join(" OR ");
          } else if (
            stringNumberByValue.like?.OR &&
            stringNumberByValue.like?.AND
          ) {
            centerWhere =
              (centerWhere ? " AND " : "") +
              stringNumberByValue.like?.OR.map(
                (word) =>
                  `(SUBDIVISION_DESCRIPTION LIKE '%${word}%' AND SUBDIVISION_DESCRIPTION LIKE '%${stringNumberByValue.like.AND}%')`
              ).join(" OR ");
          }
        }
        if (stringNumberByValue.NOT && !districtDescValue) {
          centerWhere += ` ${
            centerWhere ? " AND " : ""
          }( SUBDIVISION_DESCRIPTION NOT LIKE '%${stringNumberByValue.NOT}%')`;
        }
        centerWhere += ` ${centerWhere ? " AND " : ""} SUBDIVISION_TYPE=${
          subdivisionTypes.megawrah
        } AND ( ${
          districtDescValue ? `SUBDIVISION_DESCRIPTION LIKE '%مجاور%'` : ``
        } OR (SUBDIVISION_DESCRIPTION LIKE '%حي%' OR SUBDIVISION_DESCRIPTION LIKE '%حى%'))`;

        if (districtDescValue) {
          let megawarahWhere = ``;
          let numbers = districtDescValue.match(/\d+/g);
          let megawrahObj = megawrahNumbersInString.find(
            (i) => numbers[0] === i.value
          );
          if (typeof megawrahObj.like === "string") {
            megawarahWhere += `${
              megawarahWhere ? " AND " : ""
            }  SUBDIVISION_DESCRIPTION LIKE '%${megawrahObj.like}%'`;
          } else {
            // like: [{OR:["أحد","حادي"]}, {AND:"عشر"}]

            if (megawrahObj.like?.OR && !megawrahObj.like?.AND) {
              megawarahWhere +=
                (megawarahWhere ? " AND " : "") +
                megawrahObj.like?.OR.map(
                  (word) => `(SUBDIVISION_DESCRIPTION LIKE '%${word}%')`
                ).join(" OR ");
            } else if (megawrahObj.like?.OR && megawrahObj.like?.AND) {
              megawarahWhere +=
                (megawarahWhere ? " AND " : "") +
                megawrahObj.like?.OR.map(
                  (word) =>
                    `(SUBDIVISION_DESCRIPTION LIKE '%${word}%' AND SUBDIVISION_DESCRIPTION LIKE '%${megawrahObj.like.AND}%')`
                ).join(" OR ");
            }
          }
          if (megawrahObj.NOT) {
            // megawarahWhere += ` ${megawarahWhere ? " AND " : ""}  ( SUBDIVISION_DESCRIPTION NOT LIKE '%${megawrahObj.NOT}%')`;
          }
          centerWhere += (centerWhere ? " AND " : "") + `(${megawarahWhere})`;
          where += (where ? " AND " : "") + `(${centerWhere})`;
        }
      } else if (
        fieldName === "category_desc" &&
        fieldValue !== "بدون" &&
        fieldValue
      ) {
        // todo:
        let categoryAlphabet = fieldValue.match(/\(\s*(.)\s*\)/);
        let valueFromAlphabetCatregory = fieldValue;
        if (categoryAlphabet.length) {
          valueFromAlphabetCatregory = alphbetNumbers.find((al) => {
            if (typeof al.name === "string")
              return al.name === categoryAlphabet[1];
            else {
              return al.name.includes(categoryAlphabet[1]);
            }
          });
        }
        where += `${where ? " AND (" : "("} (SUBDIVISION_TYPE=${
          subdivisionTypes.category
        } OR SUBDIVISION_TYPE=${subdivisionTypes.district}) AND (${
          typeof valueFromAlphabetCatregory.name === "string"
            ? `SUBDIVISION_DESCRIPTION LIKE '%${valueFromAlphabetCatregory.name}%'`
            : "( " +
              valueFromAlphabetCatregory.name
                .map((n) => `SUBDIVISION_DESCRIPTION LIKE '%${n}%'`)
                .join(" OR ") +
              " ) "
        } OR SUBDIVISION_DESCRIPTION LIKE '%${
          valueFromAlphabetCatregory.value
        }%' OR SUBDIVISION_DESCRIPTION LIKE '%فئة%' OR SUBDIVISION_DESCRIPTION LIKE '%فئه%') )`;
      }
    });
    return where;
  };

  return (
    <div className="generalResultDetails cardDetailsHelp">
      {archiveDataModal && (
        <ArchiveModal
          isOpen={true}
          galleryData={archiveDataModal}
          closeModal={() => setArchiveDataModal()}
        >
          <PdfViewer
            showLoading={showLoading}
            isOpen={true}
            data={archiveDataModal}
            isUnPlannedLands={props.data?.layerName === "UnplannedParcels"}
            closeModal={() => setArchiveDataModal()}
            title={
              props.data?.layerName === "UnplannedParcels"
                ? t("common:unplannedParcelArchive")
                : t("common:imageArchive")
            }
            mainData={props.mainData}
          />
        </ArchiveModal>
      )}

      {planDataModal && (
        <PlanDataModal
          map={props.map}
          open={planDataModal}
          planData={planDataModal}
          closeModal={() => setPlanDataModal()}
          landbaseParcelData={props.mainData.layers["Landbase_Parcel"]}
          userGroups={props?.mainData?.user?.groups || []}
        />
      )}
      {districtDataModal && (
        <DistrictDataModal
          map={props.map}
          open={districtDataModal}
          districtData={districtDataModal}
          closeModal={() => setDistrictDataModal()}
          landbaseParcelData={props.mainData.layers["Landbase_Parcel"]}
          userGroups={props?.mainData?.user?.groups || []}
        />
      )}
      {props.data && (
        <Tabs
          defaultFocus={true}
          selectedIndex={selectedTab}
          onSelect={(x) => makeClickAction(x)}
        >
          <TabList>
            <Tab>
              <Tooltip title={t("map:results")} placement="top">
                <img
                  // style={{ width: "18px", padding: "0" }}
                  src={info}
                  alt="info"
                />
              </Tooltip>
            </Tab>

            <Tab>
              <Tooltip
                title={t("map:mapToolsServices.googleMaps")}
                placement="top"
              >
                <button className="tooltipButton">
                  <img
                    // style={{ width: "18px", padding: "0" }}
                    src={googleLocation}
                    alt="googleLocation"
                  />
                </button>
              </Tooltip>
            </Tab>

            {iconsData.map((ic) => {
              if (
                ic.depName === "LandStatistics" &&
                randomPlanNumbers.includes(props.data?.PLAN_NO)
              )
                return undefined;
              if (
                ic.showingField === undefined ||
                ic.showingField !== "OWNER_TYPE" ||
                !isPrivateRoyalSalesLands ||
                (isPrivateRoyalSalesLands && ic.name === props.data.layerName)
              )
                return (
                  <Tab>
                    <Tooltip title={t(`layers:${ic.tooltip}`)} placement="top">
                      <IconButton className="tooltipButton">
                        {ic.icon ? (
                          <img src={ic.icon} />
                        ) : (
                          <img
                            id=""
                            className={
                              ic.className === "contzwa2edClass"
                                ? "updaeContractImgClass"
                                : ""
                            }
                            alt="Icon"
                            src={ic.imgIconSrc}
                            style={{
                              cursor: "pointer",
                            }}
                          />
                        )}
                      </IconButton>
                    </Tooltip>
                  </Tab>
                );
            })}
          </TabList>
          <TabPanel>
            <Table
              striped
              responsive
              hover
              className="mt-2 outerSearchDetailTrStyle"
            >
              {(isPrivateRoyalSalesLands &&
                props?.resultDetailsDataRef.current?.landBaseParcelData) ||
              (!isPrivateRoyalSalesLands && props?.data?.layerName) ? (
                props.mainData.layers[
                  isPrivateRoyalSalesLands
                    ? PARCEL_LANDS_LAYER_NAME
                    : props.data.layerName
                ].outFields
                  .filter(
                    (x) => x !== "OBJECTID" && x.indexOf("SPATIAL_ID") < 0
                  )
                  .map((attribute, index) => {
                    return (
                      <tr key={index}>
                        <td className="infoTableTd">
                          {props.mainData.layers[
                            isPrivateRoyalSalesLands
                              ? PARCEL_LANDS_LAYER_NAME
                              : props.data.layerName
                          ]?.notInConfig
                            ? props.languageState === "ar"
                              ? props.mainData.layers[
                                  isPrivateRoyalSalesLands
                                    ? PARCEL_LANDS_LAYER_NAME
                                    : props.data.layerName
                                ].aliasOutFields[index]
                              : props.mainData.layers[
                                  isPrivateRoyalSalesLands
                                    ? PARCEL_LANDS_LAYER_NAME
                                    : props.data.layerName
                                ].outFields[index]
                            : props.languageState === "ar" &&
                              props.mainData.layers[
                                isPrivateRoyalSalesLands
                                  ? PARCEL_LANDS_LAYER_NAME
                                  : props.data.layerName
                              ].aliasOutFields[index].match(
                                "[\u0600-\u06ff]|[\u0750-\u077f]|[\ufb50-\ufbc1]|[\ufbd3-\ufd3f]|[\ufd50-\ufd8f]|[\ufd92-\ufdc7]|[\ufe70-\ufefc]|[\uFDF0-\uFDFD]"
                              )
                            ? props.mainData.layers[
                                isPrivateRoyalSalesLands
                                  ? PARCEL_LANDS_LAYER_NAME
                                  : props.data.layerName
                              ].aliasOutFields[index]
                            :
                            attribute == "UNITS_NUMBER"?
                            t(
                                `${
                                  props.mainData.layers[
                                    isPrivateRoyalSalesLands
                                      ? PARCEL_LANDS_LAYER_NAME
                                      : props.data.layerName
                                  ].aliasOutFields[index]
                                }`,
                                { count: props.data.UNITS_NUMBER }
                              ):
                            t(
                                `layers:${
                                  props.mainData.layers[
                                    isPrivateRoyalSalesLands
                                      ? PARCEL_LANDS_LAYER_NAME
                                      : props.data.layerName
                                  ].aliasOutFields[index]
                                }`
                              )}
                        </td>
                        <td
                          className="infoTableData"
                          style={{ textAlign: "center" }}
                        >
                          {attribute === "CREATED_DATE"
                            ? toArabic(
                                moment(
                                  new Date(
                                    (isPrivateRoyalSalesLands
                                      ? props.resultDetailsDataRef.current
                                          ?.landBaseParcelData?.attributes
                                      : props.data)["CREATED_DATE"]
                                  )
                                ).format("iYYYY/iMM/iDD")
                              )
                            : convertToArabic(
                                (attribute.indexOf("_AREA") > -1 &&
                                isNumber(
                                  (isPrivateRoyalSalesLands
                                    ? props.resultDetailsDataRef.current
                                        ?.landBaseParcelData?.attributes
                                    : props.data)[attribute]
                                )
                                  ? (+(
                                      isPrivateRoyalSalesLands
                                        ? props.resultDetailsDataRef.current
                                            ?.landBaseParcelData?.attributes
                                        : props.data
                                    )[attribute]).toFixed(2)
                                  : (isPrivateRoyalSalesLands
                                      ? props.resultDetailsDataRef.current
                                          ?.landBaseParcelData?.attributes
                                      : props.data)[attribute]) ||
                                  t("common:notAvailable")
                              )}
                        </td>
                      </tr>
                    );
                  })
              ) : isPrivateRoyalSalesLands &&
                !props.resultDetailsDataRef.current?.landBaseParcelData &&
                !loading ? (
                <p className="noDataStyle noData_outerSearchResult">
                  {" "}
                  {t("common:notAvailableData")}
                </p>
              ) : (
                isPrivateRoyalSalesLands &&
                !props.resultDetailsDataRef.current?.landBaseParcelData &&
                loading && <Spinner />
              )}
            </Table>
          </TabPanel>

          <TabPanel></TabPanel>
          <TabPanel></TabPanel>

          {iconsData
            .filter((y) => y.id !== 1)
            .map((ic, idx) => {
              return (
                <TabPanel>
                  {dependcyLayer &&
                  !dependcyLayer?.length &&
                  dependcyLayer?.attributes &&
                  props.mainData.layers[dependcyLayer?.attributes.layerName] &&
                  !dependcyLayer?.attributes.isExternalFetchedData ? (
                    <Table
                      striped
                      responsive
                      hover
                      className={`mt-2 outerSearchDetailTrStyle ${
                        dependcyLayer.length === idx + 1
                          ? ""
                          : "dash-bottom-border"
                      }`}
                    >
                      {props.mainData.layers[
                        dependcyLayer.attributes.layerName
                      ].outFields
                        .filter(
                          (x) => x !== "OBJECTID" && x.indexOf("SPATIAL_ID") < 0
                        )
                        .map((attribute, index) => {
                          return (
                            <tr key={index}>
                              <td className="infoTableTd">
                                {t(
                                  `layers:${
                                    props.mainData.layers[
                                      dependcyLayer.attributes.layerName
                                    ].aliasOutFields[index]
                                  }`
                                )}
                              </td>
                              <td
                                className="infoTableData"
                                style={{ textAlign: "center" }}
                              >
                                {convertToArabic(
                                  (attribute.indexOf("_AREA") > -1 &&
                                  isNumber(dependcyLayer.attributes[attribute])
                                    ? (+dependcyLayer.attributes[
                                        attribute
                                      ]).toFixed(2)
                                    : showDataSplittedBySlash(
                                        dependcyLayer.attributes[attribute]
                                      )) || t("common:notAvailable")
                                )}
                              </td>
                            </tr>
                          );
                        })}
                    </Table>
                  ) : dependcyLayer && dependcyLayer?.length ? (
                    dependcyLayer?.map((item, idx) => {
                      let isDepPrivateRoyalSales = [
                        "LGR_ROYAL",
                        "PARCEL_PRIVACY",
                        "SALES_LANDS",
                      ].includes(item.layerName);
                      let outFields =
                        isDepPrivateRoyalSales &&
                        props.mainData.layers[item.layerName].fields
                          ? props.mainData.layers[item.layerName].fields
                          : props.mainData.layers[item.layerName].outFields;
                      return (
                        <Table
                          striped
                          responsive
                          hover
                          className={`mt-2 outerSearchDetailTrStyle ${
                            dependcyLayer.length === idx + 1
                              ? ""
                              : "dash-bottom-border"
                          }`}
                        >
                          {outFields
                            .filter((x) => x.name !== "id")
                            .filter((attr) => {
                              if (item.layerName === "SALES_LANDS") {
                                if (
                                  [
                                    "block_no",
                                    "subdivision_description",
                                    "subdivision_type",
                                  ].includes(attr.name)
                                ) {
                                  if (
                                    item.attributes[attr.name] &&
                                    ![
                                      "Null",
                                      "0",
                                      0,
                                      null,
                                      undefined,
                                      "",
                                    ].includes(item.attributes[attr.name])
                                  )
                                    return attr;
                                  else return undefined;
                                }
                              }
                              return attr;
                            })
                            .map((attribute, index) => {
                              let token = props.mainData.user?.token;
                              let dependency = iconsData.find(
                                (it) => it.name === item.layerName
                              );

                              return (
                                <tr key={index}>
                                  <td className="infoTableTd">
                                    {t(`layers:${attribute.alias}`)}
                                  </td>
                                  <td
                                    className="infoTableData"
                                    style={{ textAlign: "center" }}
                                  >
                                    {convertToArabic(
                                      attribute.isAnchor &&
                                        attribute.name === "request_no" &&
                                        item.attributes[attribute.name] ? (
                                        <a
                                          target="_blank"
                                          rel="noreferrer noopener"
                                          href={
                                            dependency.workflowUrl +
                                            item.attributes["id"] +
                                            `?tk=${token}`
                                          }
                                        >
                                          {showDataSplittedBySlash(
                                            item.attributes[attribute.name]
                                          )}
                                        </a>
                                      ) : attribute.isHijriDateFormat ? (
                                        getDateFromConcatNumbers(
                                          item?.attributes[attribute.name]
                                        )
                                      ) : (
                                        showDataSplittedBySlash(
                                          item.attributes[attribute.name]
                                        ) || t("common:notAvailable")
                                      )
                                    )}
                                  </td>
                                </tr>
                              );
                            })}
                        </Table>
                      );
                    })
                  ) : dependcyLayer && dependcyLayer.length === 0 ? (
                    <p className="noDataStyle noData_outerSearchResult">
                      {" "}
                      {t("common:notAvailableData")}
                    </p>
                  ) : (
                    <></>
                  )}
                </TabPanel>
              );
            })}

          {/*beginning: split,contract,kroky */}
          <TabPanel></TabPanel>
          <TabPanel></TabPanel>
          <TabPanel></TabPanel>
          {/*end: split,contract,kroky */}
        </Tabs>
      )}
    </div>
  );
}
export default OuterSearchResultDetails;
