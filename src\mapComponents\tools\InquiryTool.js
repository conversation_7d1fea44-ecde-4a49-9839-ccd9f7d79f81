import React, { useEffect, useRef, useState } from "react";
import Fade from "react-reveal/Fade";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes, faInfo, faArchive } from "@fortawesome/free-solid-svg-icons";
import { Select, Form } from "antd";
import { Table } from "react-bootstrap";
import { DownCircleFilled } from "@ant-design/icons";
import { Resizable } from "re-resizable";
import IconButton from "@mui/material/IconButton";
import { styled, Tooltip } from "@mui/material";
import axios from "axios";
import { useTranslation } from "react-i18next";
import arrow_drop_down from "../../assets/icons/arrow_drop_down.svg";
import arrow_drop_up from "../../assets/icons/arrow_drop_up.svg";

import results_icon from "../../assets/icons/inquiry-tool/النتائج.svg";
import real_estate_icon from "../../assets/icons/inquiry-tool/الصحيفة العقارية.svg";
import archive_icon from "../../assets/icons/archieve.svg";
import building_regulations_icon from "../../assets/icons/inquiry-tool/إشتراطات البناء.svg";
import zoom_in_icon from "../../assets/icons/inquiry-tool/تكبير.svg";
import building_license_icon from "../../assets/icons/inquiry-tool/رخص البناء.svg";
import shop_license_icon from "../../assets/icons/inquiry-tool/رخص المحلات.svg";
import google_maps_icon from "../../assets/icons/inquiry-tool/عرض خرائط جوجل.svg";

import land_data_1 from "../../assets/icons/left-bar/land_data_1.svg";
import land_data_2 from "../../assets/icons/left-bar/land_data_2.svg";
import land_data_3 from "../../assets/icons/left-bar/land_data_3.svg";
import land_data_4 from "../../assets/icons/left-bar/land_data_4.svg";
import land_data_5 from "../../assets/icons/left-bar/land_data_5.svg";
import land_data_6 from "../../assets/icons/left-bar/land_data_6.svg";

import {
  addPictureSymbol,
  getFeatureDomainName,
  makeIdentify,
  showLoading,
  getLayerId,
  clearCanvasLine,
  drawLine,
  highlightFeature,
  queryTask,
  IsNotNull,
  clearGraphicLayer,
  convertToArabic,
  navigateToGoogle,
  zoomToFeatures,
} from "../../helper/common_func";
import PlanDataModal from "../../tables/Modals/PlanLandsData/PlanDataModal";
import DistrictDataModal from "../../tables/Modals/DistrictLandsData/DistrictDataModal";
import LandDataModal from "../landsData/LandDataModal";
import identifyIcon from "../../assets/images/identify.gif";
import {
  convertNumbersToEnglish,
  getDateFromConcatNumbers,
  isNumber,
  notificationMessage,
  showDataSplittedBySlash,
} from "../../helper/utilsFunc";
import { useCallback } from "react";
import { PARCEL_LANDS_LAYER_NAME } from "../../helper/constants";

let handler = {};

export default function InquiryTool(props) {
  const { t } = useTranslation("common", "layers");
  const [defaultLayers] = useState([
    "Landbase_Parcel",
    "Plan_Data",
    "District_Boundary",
    "Municipality_Boundary",
    "UrbanAreaBoundary",
  ]);
  const [open, setOpen] = useState(false);
  const [planDataModal, setPlanDataModal] = useState();
  const [districtDataModal, setDistrictDataModal] = useState();
  const [landData, setLandData] = useState([]); // State for service icons
  const [landDataOpened, setLandDataOpened] = useState(null); // State for LandDataModal
  const [searchLayer, setSearchLayer] = useState(undefined);
  const [identifyResults, setIdentifyResults] = useState(undefined);
  const [depData, setDepData] = useState();
  const searchLayerRef = useRef(undefined);
  const landSpatialIDRef = useRef();

  const groupByKey = (list, key) =>
    list.reduce(
      (hash, obj) => ({
        ...hash,
        [obj[key]]: (hash[obj[key]] || []).concat(obj),
      }),
      {}
    );

  const handleSelect = (layer) => {
    setOpen(false);
    setSearchLayer(layer);
    searchLayerRef.current = layer;
    if (searchLayer !== layer) {
      setDepData(undefined);
      setPlanDataModal();
      setDistrictDataModal();
      setLandData([]); // Reset service icons
    }

    if (["Landbase_Parcel"].includes(layer)) {
      fetchServicesData(layer);
      props.setPopupInfo({ ...(props.popupInfo || {}), currentLayer: layer });
    } else if (["Street_Naming"].includes(layer)) {
      fetchServicesData(layer);
      props.setPopupInfo({
        ...(props.popupInfo || {}),
        currentLayer: layer,
        isStreet: true,
      });
    } else {
      let popupInfoClone = { ...props.popupInfo };
      if (popupInfoClone.currentLayer) delete popupInfoClone.currentLayer;
      props.setPopupInfo(popupInfoClone);
    }
    highlightFeature(
      identifyResults[layer].map((res) => res.feature),
      props.map,
      {
        layerName: "SelectGraphicLayer",
        isZoom: true,
        zoomDuration: 2000,
      }
    );
  };

  const mapIdentifyResultWithDomain = (results) => {
    return new Promise((resolve) => {
      results = groupByKey(results, "layerName");
      let count = Object.keys(results).length;

      Object.keys(results).forEach((layerkey) => {
        let layerFeatures = results[layerkey];
        getFeatureDomainName(
          layerFeatures.map((layer) => layer.feature),
          layerFeatures[0].layerId,
          false,
          undefined,
          true
        ).then((domainResult) => {
          domainResult.forEach((d, index) => {
            layerFeatures[index].feature = d;
          });
          --count;
          if (count === 0) {
            resolve(results);
          }
        });
      });
    });
  };

  const getIdenitfyLayersId = () => {
    let layersSetting = props.mainData.layers;
    let layers = Object.keys(layersSetting).filter(
      (l) => !layersSetting[l].isHidden
    );
    return layers
      .map((layer) => getLayerId(props.map.__mapInfo, layer))
      .filter((i) => typeof i === "number");
  };

  const setDataToUI = (data) => {
    setIdentifyResults(data);
    let names = Array.from(
      new Set([
        ...defaultLayers.filter((item) => Object.keys(data).includes(item)),
        ...Object.keys(data),
      ])
    );
    setSearchLayer(names[0]);
    searchLayerRef.current = names[0];
    showLoading(false);
    return searchLayerRef.current;
  };

  //   const fetchServicesData = (currentLayer, queryResults) => {
  //   // Fetch service icons based on layer and attributes
  //   const isCommercial =
  //     currentLayer === "Landbase_Parcel" &&
  //     queryResults["Landbase_Parcel"]?.[0]?.feature?.attributes?.["PARCEL_MAIN_LUSE"] === "تجاري";
  //   const isResidential =
  //     currentLayer === "Landbase_Parcel" &&
  //     queryResults["Landbase_Parcel"]?.[0]?.feature?.attributes?.["PARCEL_MAIN_LUSE"]?.includes("سكن");
  //   const isStreet = currentLayer === "Street_Naming";

  //   axios
  //     .get(
  //       `${window.ApiUrl}/GetUsageData?isCommercial=${isCommercial}&isResidential=${isResidential}&isStreet=${isStreet}`
  //     )
  //     .then((res) => {
  //       if (typeof res.data === "object" && res.data?.length === 0) {
  //         setLandData([]);
  //       } else {
  //         let dataInJson = res.data
  //           .map((d) => JSON.parse(d)[0])
  //           .reverse();
  //         if (currentLayer === "Street_Naming") {
  //           setLandData(dataInJson.filter((ic) => ic.title === "رخص الحفريات"));
  //         } else if (currentLayer === "Landbase_Parcel") {
  //           setLandData(dataInJson.filter((ic) => ic.title !== "رخص الحفريات"));
  //         } else {
  //           setLandData([]);
  //         }
  //       }
  //     })
  //     .catch((err) => {
  //       console.error(err);
  //       setLandData([]);
  //     });
  // };
  const fetchServicesData = (currentLayer, queryResults) => {
    const isCommercial =
      currentLayer === "Landbase_Parcel" &&
      queryResults["Landbase_Parcel"]?.[0]?.feature?.attributes?.[
        "PARCEL_MAIN_LUSE"
      ] === "تجاري";
    const isResidential =
      currentLayer === "Landbase_Parcel" &&
      queryResults["Landbase_Parcel"]?.[0]?.feature?.attributes?.[
        "PARCEL_MAIN_LUSE"
      ]?.includes("سكن");
    const isStreet = currentLayer === "Street_Naming";

    axios
      .get(
        `${window.ApiUrl}/GetUsageData?isCommercial=${isCommercial}&isResidential=${isResidential}&isStreet=${isStreet}`
      )
      .then((res) => {
        if (typeof res.data === "object" && res.data?.length === 0) {
          setLandData([]);
        } else {
          let dataInJson = res.data.map((d) => JSON.parse(d)[0]).reverse();
          if (currentLayer === "Street_Naming") {
            setLandData(dataInJson.filter((ic) => ic.title === "رخص الحفريات"));
          } else if (currentLayer === "Landbase_Parcel") {
            setLandData(dataInJson.filter((ic) => ic.title !== "رخص الحفريات"));
          } else {
            setLandData([]);
          }
        }
      })
      .catch((err) => {
        console.error(err);
        setLandData([]);
      });
  };

  const showLandDataDetails = (link) => {
    setLandDataOpened(link);
  };
  useEffect(() => {
    // window.handles.remove("map-events");
    window.handles.push(
      props.map.view.on("click", (event) => {
        props.map.view.goTo(event.mapPoint);
        addPictureSymbol(
          event.mapPoint,
          identifyIcon,
          "identifyGraphicLayer",
          props.map
        );

        showLoading(true);
        makeIdentify(
          props.map.view,
          event.mapPoint,
          getIdenitfyLayersId(),
          5
        ).then(({ results }) => {
          if (results.length > 0) {
            mapIdentifyResultWithDomain(results).then((res) => {
              if (res["Landbase_Parcel"]) {
                let layerdId = getLayerId(
                  props.map.__mapInfo,
                  "Tbl_SHOP_LIC_MATCHED"
                );
                let query = res["Landbase_Parcel"].map(
                  (f) =>
                    `PARCEL_SPATIAL_ID = ${f.feature.attributes["PARCEL_SPATIAL_ID"]}`
                );

                queryTask({
                  url: window.mapUrl + "/" + layerdId,
                  where: query.join(" or "),
                  outFields: ["PARCEL_SPATIAL_ID", "S_SHOP_LIC_NO"],
                  returnGeometry: false,
                  callbackResult: (shops) => {
                    shops.features.forEach((shop) => {
                      let parcel = res["Landbase_Parcel"].find(
                        (f) =>
                          f.feature.attributes["PARCEL_SPATIAL_ID"] ==
                          shop.attributes["PARCEL_SPATIAL_ID"]
                      );
                      if (parcel) parcel.feature.isShopLic = true;
                    });
                    let popupInfo = { mapPoint: event.mapPoint };
                    let currentLayer = setDataToUI(res);

                    if (
                      res["Landbase_Parcel"].length === 1 ||
                      res["Street_Naming"]?.length === 1
                    ) {
                      let landAttributes =
                        res["Landbase_Parcel"]?.[0]?.feature?.attributes || {};
                      fetchServicesData(currentLayer, res);
                      props.setPopupInfo({
                        ...popupInfo,
                        isStreet: res["Street_Naming"]?.length ? true : false,
                        isCommercial:
                          landAttributes["PARCEL_MAIN_LUSE"] === "تجاري",
                        isResidential:
                          landAttributes["PARCEL_MAIN_LUSE"]?.includes("سكن"),
                        landSpatialID: landAttributes["PARCEL_SPATIAL_ID"],
                        currentLayer,
                      });
                    } else {
                      props.setPopupInfo({ ...popupInfo, currentLayer });
                    }
                  },
                  callbackError: (err) => {
                    showLoading(false);
                    setIdentifyResults(null);
                    setLandData([]);
                    if (err?.response?.status === 401) {
                      notificationMessage(t("common:sessionFinished"), 5);
                      localStorage.removeItem("user");
                      localStorage.removeItem("token");
                      window.open(window.hostURL + "/home/<USER>", "_self");
                    } else {
                      notificationMessage(t("common:retrievError"), 5);
                    }
                  },
                });
              } else if (res["Street_Naming"]) {
                let currentLayer = setDataToUI(res);
                //fetchServicesData(currentLayer, res); // Pass res to fetchServicesData
                props.setPopupInfo({
                  mapPoint: event.mapPoint,
                  currentLayer,
                  isStreet: true,
                  isCommercial: false,
                  isResidential: false,
                });
              } else {
                let currentLayer = setDataToUI(res);
              }
            });
          } else {
            showLoading(false);
            setIdentifyResults(null);
            setLandData([]);
          }
        });
      })
    );

    return () => {
      props.setPopupInfo();
      clearGraphicLayer("SelectGraphicLayer", props.map);
      clearGraphicLayer("identifyGraphicLayer", props.map);
    };
  }, []);

  // useEffect(() => {
  //   handler = props.map.view.on("click", (event) => {
  //     props.map.view.goTo(event.mapPoint);
  //     addPictureSymbol(
  //       event.mapPoint,
  //       identifyIcon,
  //       "identifyGraphicLayer",
  //       props.map
  //     );

  //     showLoading(true);
  //     makeIdentify(
  //       props.map.view,
  //       event.mapPoint,
  //       getIdenitfyLayersId(),
  //       5
  //     ).then(({ results }) => {
  //       if (results.length > 0) {
  //         mapIdentifyResultWithDomain(results).then((res) => {
  //           if (res["Landbase_Parcel"]) {
  //             let layerdId = getLayerId(
  //               props.map.__mapInfo,
  //               "Tbl_SHOP_LIC_MATCHED"
  //             );
  //             let query = res["Landbase_Parcel"].map(
  //               (f) =>
  //                 `PARCEL_SPATIAL_ID = ${f.feature.attributes["PARCEL_SPATIAL_ID"]}`
  //             );

  //             queryTask({
  //               url: window.mapUrl + "/" + layerdId,
  //               where: query.join(" or "),
  //               outFields: ["PARCEL_SPATIAL_ID", "S_SHOP_LIC_NO"],
  //               returnGeometry: false,
  //               callbackResult: (shops) => {
  //                 shops.features.forEach((shop) => {
  //                   let parcel = res["Landbase_Parcel"].find(
  //                     (f) =>
  //                       f.feature.attributes["PARCEL_SPATIAL_ID"] ==
  //                       shop.attributes["PARCEL_SPATIAL_ID"]
  //                   );
  //                   if (parcel) parcel.feature.isShopLic = true;
  //                 });
  //                 let popupInfo = { mapPoint: event.mapPoint };
  //                 let currentLayer = setDataToUI(res);

  //                 if (
  //                   res["Landbase_Parcel"].length === 1 ||
  //                   res["Street_Naming"]?.length === 1
  //                 ) {
  //                   let landAttributes =
  //                     res["Landbase_Parcel"]?.[0]?.feature?.attributes || {};
  //                   //   fetchServicesData(currentLayer); // Fetch service icons
  //                   props.setPopupInfo({
  //                     ...popupInfo,
  //                     isStreet: res["Street_Naming"]?.length ? true : false,
  //                     isCommercial:
  //                       landAttributes["PARCEL_MAIN_LUSE"] === "تجاري",
  //                     isResidential:
  //                       landAttributes["PARCEL_MAIN_LUSE"]?.includes("سكن"),
  //                     landSpatialID: landAttributes["PARCEL_SPATIAL_ID"],
  //                     currentLayer,
  //                   });
  //                 } else {
  //                   props.setPopupInfo({ ...popupInfo, currentLayer });
  //                 }
  //               },
  //               callbackError: (err) => {
  //                 showLoading(false);
  //                 setIdentifyResults(null);
  //                 setLandData([]);
  //                 if (err?.response?.status === 401) {
  //                   notificationMessage(t("common:sessionFinished"), 5);
  //                   localStorage.removeItem("user");
  //                   localStorage.removeItem("token");
  //                   window.open(window.hostURL + "/home/<USER>", "_self");
  //                 } else {
  //                   notificationMessage(t("common:retrievError"), 5);
  //                 }
  //               },
  //             });
  //           } else if (res["Street_Naming"]) {
  //             let currentLayer = setDataToUI(res);
  //             props.setPopupInfo({
  //               mapPoint: event.mapPoint,
  //               currentLayer,
  //               isStreet: true,
  //               isCommercial: false,
  //               isResidential: false,
  //             });
  //           } else {
  //             let currentLayer = setDataToUI(res);
  //           }
  //         });
  //       } else {
  //         showLoading(false);
  //         setIdentifyResults(null);
  //         setLandData([]);
  //       }
  //     });
  //   });

  //   return () => {
  //     props.setPopupInfo();
  //     clearGraphicLayer("SelectGraphicLayer", props.map);
  //     clearGraphicLayer("identifyGraphicLayer", props.map);
  //   };
  // }, []);

  const checkIfEnableHighlight = (feature) => {
    if (feature.geometry.type === "polygon") {
      return (
        props.map.view.extent.xmin < feature.geometry.extent.xmin &&
        props.map.view.extent.xmax > feature.geometry.extent.xmax &&
        props.map.view.extent.ymin < feature.geometry.extent.ymin &&
        props.map.view.extent.ymax > feature.geometry.extent.ymax
      );
    }
    return true;
  };

  const onMouseMoveOnFeature = (feature, e) => {
    if (checkIfEnableHighlight(feature)) {
      highlightFeature(feature, props.map, {
        layerName: "SelectGraphicLayer",
        isAnimatedLocation: true,
      });
      drawLine({ feature, map: props.map, event: e });
    }
  };

  const clearFeatures = () => {
    props.map.findLayerById("SelectGraphicLayer").removeAll();
    clearCanvasLine();
  };

  const closeIdentify = () => {
    window.unSubscribeMapEvents();
    props.map.findLayerById("identifyGraphicLayer").removeAll();

    props.closeToolsData();
    setLandData([]);
  };

  const getBuildingLic = (feature) => {
    let layersSetting = props.mainData.layers;
    let layerdId = getLayerId(props.map.__mapInfo, "TBL_Parcel_LIC");
    queryTask({
      url: window.mapUrl + "/" + layerdId,
      where: `PARCEL_SPATIAL_ID = ${feature.attributes["PARCEL_SPATIAL_ID"]}`,
      outFields: layersSetting["TBL_Parcel_LIC"].outFields,
      returnGeometry: false,
      callbackResult: ({ features }) => {
        getFeatureDomainName(features, layerdId).then((res) => {
          feature.buildingLic = { ...res[0].attributes };
          setIdentifyResults({ ...identifyResults });
        });
      },
    });
  };

  const getShopLic = (feature) => {
    let layersSetting = props.mainData.layers;
    let layerdId = getLayerId(props.map.__mapInfo, "Tbl_SHOP_LIC_MATCHED");
    queryTask({
      url: window.mapUrl + "/" + layerdId,
      where: `PARCEL_SPATIAL_ID = ${feature.attributes["PARCEL_SPATIAL_ID"]}`,
      outFields: layersSetting["Tbl_SHOP_LIC_MATCHED"].outFields,
      returnGeometry: false,
      callbackResult: ({ features }) => {
        getFeatureDomainName(features, layerdId).then((res) => {
          feature.shopLic = { ...res[0].attributes };
          setIdentifyResults({ ...identifyResults });
        });
      },
    });
  };

  const handleClickDepBtns = useCallback(
    (index, item, depend) => {
      let layersSetting = props.mainData.layers;
      if (
        depend.name == "Landbase_Parcel" &&
        depend.tooltip == "gisAkarReportBtn"
      ) {
        localStorage.setItem(
          "akarReportLandId",
          `PARCEL_SPATIAL_ID=${item?.attributes["PARCEL_SPATIAL_ID"]}`
        );
        window.open(
          `${window.location.origin}/eexplorer` + depend?.redirectURL,
          "_blank",
          "rel=noopener noreferrer"
        );
      } else if (!index && depend.name === "Info Data") {
        setDepData(undefined);
      } else if (depend.name === "Archive Data") {
        let queryParams = "";
        let munName = item?.attributes["MUNICIPALITY_NAME"];
        if (munName)
          queryParams += queryParams
            ? `&ProvinceName=${munName}`
            : `ProvinceName=${munName}`;
        let districtName = item?.attributes["DISTRICT_NAME"];
        if (districtName)
          queryParams += queryParams
            ? `&DistrictName=${districtName}`
            : `DistrictName=${districtName}`;
        let planNo = item?.attributes["PLAN_NO"];
        if (planNo)
          queryParams += queryParams ? `&PlanNo=${planNo}` : `PlanNo=${planNo}`;
        let parcelNo = item?.attributes["PARCEL_PLAN_NO"];
        if (parcelNo)
          queryParams += queryParams
            ? `&LandNo=${parcelNo}`
            : `LandNo=${parcelNo}`;
        window.open("/eexplorer/Archive?" + queryParams, "_blank").focus();
      } else if (depend?.name === "LandStatistics") {
        setPlanDataModal({
          PLAN_SPATIAL_ID: item?.attributes["PLAN_SPATIAL_ID"],
          PLAN_NO: item?.attributes["PLAN_NO"],
          MUNICIPALITY_NAME: item?.attributes["MUNICIPALITY_NAME_Code"],
        });
      } else if (depend?.name === "districtLandStatistics") {
        setDistrictDataModal({
          value: item?.attributes["DISTRICT_NAME"],
          DISTRICT_NAME: item?.attributes["DISTRICT_NAME_Code"],
          MUNICIPALITY_NAME: item?.attributes["MUNICIPALITY_NAME_Code"],
        });
      } else if (depend?.name === "googleMapsLink") {
        navigateToGoogle(
          item.geometry.latitude || item.geometry.centroid.latitude,
          item.geometry.longitude || item.geometry.centroid.longitude
        );
      } else if (depend?.name === "zoomIn") {
        zoomToFeatures([item], props.map);
      } else {
        let dependency = depend;
        let layerdId = getLayerId(props.map.__mapInfo, dependency.name);
        let field = dependency.filter || layersSetting[dependency.name].filter;

        let where = `${field}= '${item.attributes[field]}'`;
        queryTask({
          url: window.mapUrl + "/" + layerdId,
          outFields: layersSetting[dependency.name].outFields,
          where,
          callbackResult: ({ features }) => {
            if (features.length) {
              getFeatureDomainName(features, layerdId).then((res) => {
                res.forEach((r) => {
                  r.attributes.layerName = dependency.name;
                });
                if (dependency.isTable) {
                  setDepData({ data: res[0], dependencyData: dependency });
                } else {
                  let mappingRes = res.map((f) => ({
                    layerName: dependency.name,
                    id: f.attributes["OBJECTID"],
                    ...f.attributes,
                  }));
                  setDepData({ data: mappingRes, dependencyData: dependency });
                }
              });
            } else {
              setDepData({ data: [], dependencyData: dependency });
            }
          },
        });
      }
    },
    [depData?.dependencyData?.name]
  );

  const getDepDataAPIDepend = (dependencyData, mainLayerData) => {
    showLoading(true);
    let layersSetting = props.mainData.layers;
    let token = props.mainData.user?.token;
    let requestURL = "",
      field = "";
    if (
      ["LGR_ROYAL", "PARCEL_PRIVACY", "SALES_LANDS"].includes(
        dependencyData.name
      )
    ) {
      field =
        dependencyData.filter || layersSetting[dependencyData.name].filter;
      requestURL = dependencyData.url + mainLayerData.attributes[field];
    } else if (dependencyData.name === "LICENSE_INFO") {
      requestURL =
        dependencyData.url + mainLayerData.attributes.PARCEL_SPATIAL_ID;
    } else {
      let parcel_no = mainLayerData.attributes["PARCEL_PLAN_NO"] || "";
      let mun_no = mainLayerData.attributes["MUNICIPALITY_NAME_Code"] || "";
      let plan_no =
        mainLayerData.attributes["PLAN_NO"] &&
        mainLayerData.attributes["PLAN_NO"] !== "Null"
          ? mainLayerData.attributes["PLAN_NO"]
          : "";
      let block_no =
        mainLayerData.attributes["PARCEL_BLOCK_NO"] &&
        mainLayerData.attributes["PARCEL_BLOCK_NO"] !== "Null"
          ? mainLayerData.attributes["PARCEL_BLOCK_NO"]
          : "";
      let subdivision_type =
        mainLayerData.attributes["SUBDIVISION_TYPE_Code"] &&
        mainLayerData.attributes["SUBDIVISION_TYPE_Code"] !== "Null"
          ? mainLayerData.attributes["SUBDIVISION_TYPE_Code"]
          : "";
      let subdivision_Desc =
        mainLayerData.attributes["SUBDIVISION_DESCRIPTION"] &&
        mainLayerData.attributes["SUBDIVISION_DESCRIPTION"] !== "Null"
          ? mainLayerData.attributes["SUBDIVISION_DESCRIPTION"]
          : "";
      requestURL =
        dependencyData.url +
        `?Parcel_no=${parcel_no}&Mun_code=${mun_no}&plan_no=${plan_no}&block_no=${block_no}&subdivision_Desc=${subdivision_Desc}&subdivision_type=${subdivision_type}`;
    }
    axios
      .get(requestURL, { headers: { Authorization: `Bearer ${token}` } })
      .then((res) => {
        let data = res.data || [];
        if (["PARCEL_PRIVACY", "SALES_LANDS"].includes(dependencyData.name))
          data = data?.results || [];
        if (data.length) {
          data = data.map((item) => ({ attributes: { ...item } }));
          let mappingRes = data.map((f) => ({
            layerName: dependencyData.name,
            id: f.attributes["OBJECTID"],
            attributes: { ...f.attributes },
            isExternalFetchedData: true,
          }));
          setDepData({ data: mappingRes, dependencyData });
        } else {
          setDepData({ data: [], dependencyData });
        }
        showLoading(false);
      })
      .catch((err) => {
        showLoading(false);
        if (err?.response?.status === 401) {
          notificationMessage(t("common:sessionFinished"), 5);
          localStorage.removeItem("user");
          localStorage.removeItem("token");
          window.open(window.hostURL + "/home/<USER>", "_self");
        } else {
          notificationMessage(t("common:retrievError"), 5);
        }
      });
  };

  const getLandStatisticsIcon = (layerName) => {
    if (layerName === "Plan_Data") {
      let hasLandStatDep = props.mainData.layers[layerName]?.dependecies?.find(
        (dep) => dep.depName === "LandStatistics"
      );
      if (hasLandStatDep)
        return {
          id: 22,
          icon: hasLandStatDep.icon,
          tooltip: hasLandStatDep.tooltip,
          data: hasLandStatDep.depName,
          name: hasLandStatDep.depName,
        };
    } else if (layerName === "District_Boundary") {
      let hasLandStatDep = props.mainData.layers[layerName]?.dependecies?.find(
        (dep) => dep.depName === "districtLandStatistics"
      );
      if (hasLandStatDep)
        return {
          id: 22,
          icon: hasLandStatDep.icon,
          tooltip: hasLandStatDep.tooltip,
          data: hasLandStatDep.depName,
          name: hasLandStatDep.depName,
        };
    }
    return undefined;
  };

  return (
    <>
      {planDataModal && (
        <PlanDataModal
          map={props.map}
          open={planDataModal}
          planData={planDataModal}
          closeModal={() => setPlanDataModal()}
          landbaseParcelData={props.mainData.layers["Landbase_Parcel"]}
          fromInquiryTool={true}
          userGroups={props?.mainData?.user?.groups || []}
        />
      )}
      {districtDataModal && (
        <DistrictDataModal
          map={props.map}
          open={districtDataModal}
          districtData={districtDataModal}
          closeModal={() => setDistrictDataModal()}
          landbaseParcelData={props.mainData.layers["Landbase_Parcel"]}
          fromInquiryTool={true}
          userGroups={props?.mainData?.user?.groups || []}
        />
      )}
      {landDataOpened &&
        landData
          .filter((item) => item.link === landDataOpened)
          .map((item) => (
            <LandDataModal
              key={item.link}
              popupInfo={props.popupInfo}
              showlandDataDetails={showLandDataDetails}
              landDataOpened={landDataOpened}
              {...item}
            />
          ))}
      <Fade left collapse>
        <div className="layersMenu" style={{ top: "0%", position: "absolute" }}>
          <Resizable
            className="leftToolMenu"
            defaultSize={{ width: 400, height: "auto" }}
            maxWidth={800}
            maxHeight={620}
            bounds="window"
            style={{
              left: "55px",
              backgroundColor: "rgba(255, 255, 255, 0.85)",
              borderRadius: "16px",
            }}
          >
            <p
              className={
                identifyResults ? "Heading_tocComponent" : "galleryHead"
              }
              style={
                identifyResults && {
                  flexDirection: "row-reverse",
                  padding: "5px",
                }
              }
            >
              {identifyResults && (
                <span
                  style={{
                    fontSize: "16px",
                    fontWeight: "400",
                    color: "#284587",
                  }}
                >
                  الاستعلام
                </span>
              )}
              <span>
                <FontAwesomeIcon
                  className="closeServMenu"
                  icon={faTimes}
                  onClick={closeIdentify}
                  style={{ cursor: "pointer" }}
                />
              </span>
              {!identifyResults && (
                <span className="heading_inquiryTool">
                  {t("common:clickOnMap")}
                </span>
              )}
            </p>
            {identifyResults && (
              <div>
                <Form.Item
                  label={"طبقة البحث"}
                  className="select-cust"
                  style={{ marginInlineEnd: "10px", marginInlineStart: "3px" }}
                >
                  <Select
                    onDropdownVisibleChange={(flag) => setOpen(flag)}
                    virtual={false}
                    suffixIcon={
                      open ? (
                        <img src={arrow_drop_up} alt="" />
                      ) : (
                        <img src={arrow_drop_down} alt="" />
                      )
                    }
                    className="dont-show"
                    onChange={handleSelect}
                    value={searchLayer}
                    placeholder={t("common:chooseLayer")}
                    getPopupContainer={(trigger) => trigger.parentNode}
                    optionFilterProp="value"
                    filterOption={(input, option) =>
                      option.value.indexOf(input) >= 0
                    }
                  >
                    {Array.from(
                      new Set([
                        ...defaultLayers.filter((item) =>
                          Object.keys(identifyResults).includes(item)
                        ),
                        ...Object.keys(identifyResults),
                      ])
                    ).map(
                      (s) =>
                        props.mainData.layers[s] && (
                          <Select.Option value={s} id={s}>
                            {props.languageState === "ar"
                              ? props.mainData.layers[s].arabicName
                              : props.mainData.layers[s].englishName}
                          </Select.Option>
                        )
                    )}
                  </Select>
                </Form.Item>
                <div className="identifyScreen">
                  <div>
                    {identifyResults[searchLayer] &&
                      identifyResults[searchLayer].map((item) => {
                        return (
                          <>
                            {searchLayer === "Landbase_Parcel" ? (
                              <div
                                style={{
                                  display: "flex",
                                  flexDirection: "row",
                                  justifyContent: "flex-start",
                                  gap: "10px",
                                  // flexWrap: "wrap",
                                  overflow: "auto",
                                  marginInlineEnd: "10px",
                                  marginInlineStart: "3px",
                                }}
                              >
                                {[
                                  {
                                    id: 1,
                                    imgIconSrc: results_icon,
                                    tooltip: "landsData",
                                    data: "Info Data",
                                    name: "Info Data",
                                  },
                                  {
                                    id: 2,
                                    imgIconSrc: google_maps_icon,
                                    tooltip: "googleMapsLink",
                                    data: "Google Map",
                                    name: "googleMapsLink",
                                  },
                                  {
                                    id: 3,
                                    imgIconSrc: zoom_in_icon,
                                    tooltip: "zoomIn",
                                    data: "Zoom To",
                                    name: "zoomIn",
                                  },
                                  ...(props.mainData.layers["Landbase_Parcel"]
                                    .dependecies || []),
                                  ...(props?.mainData?.logged &&
                                  props?.mainData?.user &&
                                  props?.mainData?.layers["Plan_Data"]
                                    ? [
                                        {
                                          id: 10,
                                          imgIconSrc: archive_icon,
                                          tooltip: "archive",
                                          data: "Archive Data",
                                          name: "Archive Data",
                                        },
                                      ]
                                    : []),
                                  // ...(landData.map((p, idx) => ({
                                  //   id: 100 + idx,
                                  //   // imgIconSrc: p.icon.replace("http", "https"),
                                  //   imgIconSrc: [
                                  //     land_data_1,
                                  //     land_data_2,
                                  //     land_data_3,
                                  //     land_data_4,
                                  //     land_data_5,
                                  //     land_data_6,
                                  //   ][idx],
                                  //   tooltip: p.title,
                                  //   data: p.link,
                                  //   name: p.link,
                                  // })) || []),
                                ]
                                  .filter((ic) => {
                                    if (
                                      searchLayer === PARCEL_LANDS_LAYER_NAME &&
                                      ic?.showingField === "OWNER_TYPE"
                                    ) {
                                      return (
                                        ic?.codeValue ===
                                        item?.feature?.attributes
                                          ?.OWNER_TYPE_Code
                                      );
                                    }
                                    return ic;
                                  })
                                  .map((ic, index) => {
                                    if (ic)
                                      return (
                                        <Tooltip
                                          key={"asd" + index}
                                          title={t(`${ic.tooltip}`, {
                                            ns: "layers",
                                          })}
                                          placement="top"
                                          className="InqueryTool"
                                        >
                                          <IconButton
                                            style={{
                                              borderRadius: "5px 5px 0 0",
                                            }}
                                            className={
                                              depData?.dependencyData?.name ===
                                                ic.name ||
                                              (!depData &&
                                                ic.data === "Info Data")
                                                ? "tooltipButton activeBtn"
                                                : ic?.name === "Archive Data"
                                                ? "tooltipButton archiveIcon"
                                                : "tooltipButton"
                                            }
                                            onClick={() =>
                                              ic.name.startsWith("http")
                                                ? showLandDataDetails(ic.name)
                                                : handleClickDepBtns(
                                                    index,
                                                    item.feature,
                                                    ic
                                                  )
                                            }
                                          >
                                            <img
                                              alt="icon"
                                              src={ic.imgIconSrc}
                                              style={{
                                                cursor: "pointer",
                                                borderRadius: "5px 5px 0 0",
                                                width: "24px",
                                                height: "24px",
                                                filter:
                                                  "brightness(0) saturate(100%) invert(23%) sepia(73%) saturate(689%) hue-rotate(187deg) brightness(94%) contrast(96%)",
                                              }}
                                            />
                                          </IconButton>
                                        </Tooltip>
                                      );
                                  })}
                              </div>
                            ) : [
                                "District_Boundary",
                                "Plan_Data",
                                "Municipality_Boundary",
                              ].includes(searchLayer) ? (
                              <div
                                style={{
                                  display: "flex",
                                  flexDirection: "row",
                                  justifyContent: "space-around",
                                }}
                              >
                                {[
                                  props?.mainData?.logged &&
                                  props?.mainData?.user &&
                                  (props?.mainData?.layers["Plan_Data"] ||
                                    props?.mainData?.layers[
                                      "District_Boundary"
                                    ])
                                    ? {
                                        id: 1,
                                        imgIconSrc: real_estate_icon,
                                        tooltip: "archive",
                                        data: "Archive Data",
                                        name: "Archive Data",
                                      }
                                    : null,
                                  getLandStatisticsIcon(searchLayer),
                                ].map((ic, index) => {
                                  if (ic)
                                    return (
                                      <Tooltip
                                        key={"asd111" + index}
                                        title={t(`layers:${ic?.tooltip}`)}
                                        placement="top"
                                        className="InqueryTool"
                                      >
                                        <IconButton
                                          style={{
                                            borderRadius: "5px 5px 0 0",
                                          }}
                                          className={
                                            ic?.name === "Archive Data"
                                              ? "tooltipButton archiveIcon"
                                              : "tooltipButton"
                                          }
                                          onClick={() =>
                                            handleClickDepBtns(
                                              index,
                                              item?.feature,
                                              ic
                                            )
                                          }
                                        >
                                          <img
                                            alt="icon"
                                            src={ic.imgIconSrc}
                                            style={{
                                              cursor: "pointer",
                                              borderRadius: "5px 5px 0 0",
                                              width: "24px",
                                              height: "24px",
                                            }}
                                          />
                                        </IconButton>
                                      </Tooltip>
                                    );
                                })}
                              </div>
                            ) : null}

                            <div
                              style={{
                                padding: "10px",
                                margin: "10px 5px",
                                borderRadius: "10px",
                                boxShadow:
                                  "0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%)",
                              }}
                            >
                              {!depData ? (
                                <Table
                                  striped
                                  responsive
                                  hover
                                  className="identifyTableStyle"
                                  onMouseLeave={clearFeatures}
                                  onMouseMove={(e) =>
                                    onMouseMoveOnFeature(item.feature, e)
                                  }
                                >
                                  {props.mainData.layers[searchLayer].outFields
                                    .filter(
                                      (x) =>
                                        x !== "OBJECTID" &&
                                        x.indexOf("SPATIAL_ID") < 0
                                    )
                                    .map((attribute, index) => {
                                      return (
                                        IsNotNull(
                                          item.feature.attributes[attribute]
                                        ) && (
                                          <tr
                                            key={index}
                                            className="identifyTR"
                                          >
                                            <td className="infoTableTd">
                                              {props.mainData.layers[
                                                searchLayer
                                              ]?.notInConfig
                                                ? props.languageState === "ar"
                                                  ? props.mainData.layers[
                                                      searchLayer
                                                    ].aliasOutFields[index]
                                                  : props.mainData.layers[
                                                      searchLayer
                                                    ].outFields[index]
                                                : props.languageState ===
                                                    "ar" &&
                                                  props.mainData.layers[
                                                    searchLayer
                                                  ].aliasOutFields[index].match(
                                                    "[\u0600-\u06ff]|[\u0750-\u077f]|[\ufb50-\ufbc1]|[\ufbd3-\ufd3f]|[\ufd50-\ufd8f]|[\ufd92-\ufdc7]|[\ufe70-\ufefc]|[\uFDF0-\uFDFD]"
                                                  )
                                                ? props.mainData.layers[
                                                    searchLayer
                                                  ].aliasOutFields[index]
                                                : t(
                                                    `layers:${props.mainData.layers[searchLayer].aliasOutFields[index]}`
                                                  )}
                                            </td>
                                            <td
                                              style={{ textAlign: "center" }}
                                              className="infoTableData"
                                            >
                                              {convertToArabic(
                                                attribute.indexOf("_AREA") >
                                                  -1 &&
                                                  isNumber(
                                                    item.feature.attributes[
                                                      attribute
                                                    ]
                                                  )
                                                  ? (+item.feature.attributes[
                                                      attribute
                                                    ]).toFixed(2)
                                                  : item.feature.attributes[
                                                      attribute
                                                    ]
                                              ) || t("common:notAvailable")}
                                            </td>
                                          </tr>
                                        )
                                      );
                                    })}
                                </Table>
                              ) : depData?.data &&
                                !depData?.data?.length &&
                                depData?.data?.attributes &&
                                props.mainData.layers[
                                  depData?.data?.attributes.layerName
                                ] &&
                                !depData?.data?.attributes
                                  .isExternalFetchedData ? (
                                <Table
                                  striped
                                  responsive
                                  hover
                                  className="mt-2 outerSearchDetailTrStyle"
                                  // className="mt-2 outerSearchDetailTrStyle dash-bottom-border"
                                >
                                  {props.mainData.layers[
                                    depData?.data.attributes.layerName
                                  ].outFields
                                    .filter(
                                      (x) =>
                                        x !== "OBJECTID" &&
                                        x.indexOf("SPATIAL_ID") < 0
                                    )
                                    .map((attribute, index) => {
                                      return (
                                        <tr key={index}>
                                          <td
                                            className="infoTableTd"
                                            style={{ padding: "10px" }}
                                          >
                                            {t(
                                              `layers:${
                                                props.mainData.layers[
                                                  depData?.data.attributes
                                                    .layerName
                                                ].aliasOutFields[index]
                                              }`
                                            )}
                                          </td>
                                          <td
                                            className="infoTableData"
                                            style={{ textAlign: "center" }}
                                          >
                                            {convertToArabic(
                                              attribute.indexOf("_AREA") > -1 &&
                                                isNumber(
                                                  depData?.data.attributes[
                                                    attribute
                                                  ]
                                                )
                                                ? (+depData?.data.attributes[
                                                    attribute
                                                  ]).toFixed(2)
                                                : showDataSplittedBySlash(
                                                    depData?.data.attributes[
                                                      attribute
                                                    ]
                                                  )
                                            ) || t("common:notAvailable")}
                                          </td>
                                        </tr>
                                      );
                                    })}
                                </Table>
                              ) : depData?.data && depData?.data?.length ? (
                                depData?.data?.map((item, idx) => {
                                  let isDepPrivateRoyalSales = [
                                    "LGR_ROYAL",
                                    "PARCEL_PRIVACY",
                                    "SALES_LANDS",
                                  ].includes(item.layerName);
                                  let outFields =
                                    isDepPrivateRoyalSales &&
                                    props.mainData.layers[item.layerName].fields
                                      ? props.mainData.layers[item.layerName]
                                          .fields
                                      : props.mainData.layers[item.layerName]
                                          .outFields;
                                  return (
                                    <Table
                                      striped
                                      responsive
                                      hover
                                      className={`mt-2 outerSearchDetailTrStyle `}
                                      // className={`mt-2 outerSearchDetailTrStyle
                                      //   ${
                                      //     depData?.data?.length === idx + 1
                                      //       ? ""
                                      //       : "dash-bottom-border"
                                      //   }
                                      // `}
                                      style={{ padding: "10px" }}
                                    >
                                      {outFields
                                        .filter((x) => x.name !== "id")
                                        .filter((attr) => {
                                          if (
                                            item.layerName === "SALES_LANDS"
                                          ) {
                                            if (
                                              [
                                                "block_no",
                                                "subdivision_description",
                                                "subdivision_type",
                                              ].includes(attr.name)
                                            ) {
                                              if (
                                                item.attributes[attr.name] &&
                                                ![
                                                  "Null",
                                                  "0",
                                                  0,
                                                  null,
                                                  undefined,
                                                  "",
                                                ].includes(
                                                  item.attributes[attr.name]
                                                )
                                              )
                                                return attr;
                                              else return undefined;
                                            }
                                          }
                                          return attr;
                                        })
                                        .map((attribute, index) => {
                                          let token =
                                            props.mainData.user?.token;
                                          let dependency =
                                            depData?.dependencyData;
                                          return (
                                            <tr key={index}>
                                              <td className="infoTableTd">
                                                {t(`layers:${attribute.alias}`)}
                                              </td>
                                              <td
                                                className="infoTableData"
                                                style={{ textAlign: "center" }}
                                              >
                                                {convertToArabic(
                                                  attribute.isAnchor &&
                                                    attribute.name ===
                                                      "request_no" &&
                                                    item.attributes[
                                                      attribute.name
                                                    ] ? (
                                                    <a
                                                      target="_blank"
                                                      rel="noreferrer"
                                                      href={
                                                        dependency.workflowUrl +
                                                        item.attributes["id"] +
                                                        `?tk=${token}`
                                                      }
                                                    >
                                                      {showDataSplittedBySlash(
                                                        item.attributes[
                                                          attribute.name
                                                        ]
                                                      )}
                                                    </a>
                                                  ) : attribute?.isHijriDateFormat ? (
                                                    getDateFromConcatNumbers(
                                                      item.attributes[
                                                        attribute.name
                                                      ]
                                                    ) ||
                                                    t("common:notAvailable")
                                                  ) : (
                                                    showDataSplittedBySlash(
                                                      item.attributes[
                                                        attribute.name
                                                      ]
                                                    ) ||
                                                    t("common:notAvailable")
                                                  )
                                                )}
                                              </td>
                                            </tr>
                                          );
                                        })}
                                    </Table>
                                  );
                                })
                              ) : depData?.data &&
                                depData?.data.length === 0 ? (
                                <p
                                  className="noDataStyle"
                                  style={{ marginTop: "1rem" }}
                                >
                                  {t("common:notAvailableData")}
                                </p>
                              ) : (
                                <></>
                              )}
                            </div>
                          </>
                        );
                      })}
                  </div>
                </div>
              </div>
            )}
          </Resizable>
        </div>
      </Fade>
    </>
  );
}
