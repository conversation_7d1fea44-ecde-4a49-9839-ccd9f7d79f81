import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Switch, Toolt<PERSON> } from "antd";
import reserves_image from "../assets/images/sidemenu/reserves_image.svg";
import protected_area_image from "../assets/images/sidemenu/protected_area_image.svg";
import existing_image from "../assets/images/sidemenu/existing.svg";
import deviceIcon from "../assets/images/wireless/device.png";
import emergencyIcon from "../assets/images/wireless/emergencey_call.png";
import voiceCallIcon from "../assets/images/wireless/voice_call.png";
import proposed_2025_image from "../assets/images/sidemenu/proposed_2025.svg";
import proposed_2030_image from "../assets/images/sidemenu/proposed_2030.svg";
import ecosystem_image from "../assets/images/sidemenu/ecosystem.svg";
import ecosystem_item_image from "../assets/images/sidemenu/ecosystem_item.svg";
import environmental_regions_img from "../assets/images/sidemenu/environmental_regions.svg";
import bio_diversity_image from "../assets/images/sidemenu/bio-diversity-image.svg";
import surveillance_cameras_image from "../assets/images/sidemenu/surveillance_cameras.svg";
import wild_img from "../assets/images/sidemenu/wild_img.svg";
import nautical_img from "../assets/images/sidemenu/nautical_img.svg";
import mammals_img from "../assets/images/sidemenu/mammals.svg";
import Reserves from "../assets/images/sidemenu/Reserves.svg";
import reptiles_img from "../assets/images/sidemenu/reptiles.svg";
import baboon from "../assets/images/sidemenu/babbons.svg";
import cameras from "../assets/images/sidemenu/cameras.svg";
import wireless from "../assets/images/sidemenu/wireless.svg";
import Invertebrates_img from "../assets/images/sidemenu/Invertebrates.svg";
import habitats_img from "../assets/images/sidemenu/habitats.svg";
import marine_organisms_img from "../assets/images/sidemenu/marine_organisms.svg";
import Woodcutting from "../assets/images/sidemenu/Woodcutting.svg";
import Grazing from "../assets/images/sidemenu/Grazing.svg";
import Infringements from "../assets/images/sidemenu/Infringements.svg";
import breakthrough from "../assets/images/sidemenu/breakthrough.svg";
import pollution from "../assets/images/sidemenu/pollution.svg";
import tunnel from "../assets/images/sidemenu/tunnel.svg";
import fish_img from "../assets/images/sidemenu/fish.svg";
import hunt from "../assets/images/sidemenu/hunt.svg";
import Wetlands_img from "../assets/images/sidemenu/Wetlands.svg";
import wildlife_image from "../assets/images/sidemenu/wildlife.svg";
import national_parks_img from "../assets/images/sidemenu/national_parks.svg";
import sites_img from "../assets/images/sidemenu/sites.svg";
import birds_img from "../assets/images/sidemenu/birds.svg";
import mangrove_img from "../assets/images/sidemenu/Mangrove.svg";
import royal_commission_image from "../assets/images/sidemenu/royal_commission.svg";
import coral_reefs_img from "../assets/images/sidemenu/coral_reefs.svg";
import seaweed_img from "../assets/images/sidemenu/seaweed.svg";
import proposed_image from "../assets/images/sidemenu/proposed_image.svg";
import { useTranslation } from "react-i18next";
import {
  getFromEsriRequest,
  getLayerId,
  getLayerInfo,
  highlightFeature,
  showLoading,
} from "../helper/common_func";
import { MdKeyboardArrowDown } from "react-icons/md";
import * as watchUtils from "@arcgis/core/core/watchUtils";
import eventBus from "../helper/EventBus";
import FeatureLayer from "@arcgis/core/layers/FeatureLayer";
import usePersistentState from "../helper/usePersistentState";
// import ReactPlayer from "react-player";
import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer";
import Point from "@arcgis/core/geometry/Point";
import Graphic from "@arcgis/core/Graphic";
import PictureMarkerSymbol from "@arcgis/core/symbols/PictureMarkerSymbol";

const componentName = "LayersMenu";

export default function LayersMenu(props) {

  const prevSelectionValues = React.useRef({});
  let constProtectedAreaFeatures = [];

  const { t } = useTranslation("layersmenu");
  let handle;
  const [firstIntalize, setFirstIntalize] = usePersistentState(
    "firstIntalize",
    false,
    componentName
  );
  const [isToggled, setIsToggled] = usePersistentState(
    "isToggled",
    true,
    componentName
  );

  const [isProtectedAreaToggled, setIsProtectedAreaToggled] = usePersistentState(
    "isProtectedAreaToggled",
    false,
    componentName
  );

  const toggleProtectedAreaRef = useRef(isProtectedAreaToggled);

  useEffect(() => {
    toggleProtectedAreaRef.current = isProtectedAreaToggled;
  }, [isProtectedAreaToggled]);

  const [cameraInfo, setCameraInfo] = useState(-1);

  const hidePopUpsLayers = ["BROADCAST_CAMERA"];

  const defaultVisibleLayers = [
    "PROTECTED_AREA_BOUNDARY"
  ];

  const editingLayers = [
    "PROTECTED_AREA_BOUNDARY",
    "MANGROVE",
    "CORAL_REEF",
    "SEA_GRASS",
    "ECO_REGION",
    "ECO_SYSTEM",
    "ECO_HOTSPOT",
    "WET_LAND",
    "MARINE_SPECIE",
    "TERRESTRIAL_SPECIE",
    "BROADCAST_CAMERA",
  ];

  const existing_list = [
    {
      id: 1,
      text: "National Center for Wildlife Development",
      img: wildlife_image,
      alt: "wildlife_image",
      domainValue: "NCW",
      legendValue: "PA,NCW",
    },
    {
      id: 2,
      text: "National parks",
      img: national_parks_img,
      alt: "national_parks_img",
      domainValue: "NCVC",
      legendValue: "PA,NCVC",
    },
    {
      id: 3,
      text: "Royal Commission for AlUla",
      img: royal_commission_image,
      alt: "royal_commission_image",
      domainValue: "RCU",
      legendValue: "PA,RCU",
    },
    {
      id: 4,
      text: "Property",
      img: royal_commission_image,
      alt: "royal_commission_image",
      domainValue: "ROYAL",
      legendValue: "PA,ROYAL",
    },
    {
      id: 5,
      text: "Other",
      img: proposed_image,
      alt: "other",
      domainValue: "OTHER",
      legendValue: "PA,OTHER",
    },
  ];

  const proposed_2025_list = [
    {
      id: 1,
      text: "National Center for Wildlife Development",
      img: wildlife_image,
      alt: "wildlife_image",
      domainValue: "NCW",
      legendValue: "PPA_25,NCW",
    },
    {
      id: 2,
      text: "Proposed by other parties",
      img: proposed_image,
      alt: "proposed_image",
      domainValue: "OTHER",
      legendValue: "PPA_25,OTHER",
    },
  ];

  const proposed_2030_list = [
    {
      id: 1,
      text: "National Center for Wildlife Development",
      img: wildlife_image,
      alt: "wildlife_image",
      domainValue: "NCW",
      legendValue: "PPA_30,NCW",
    },
    {
      id: 2,
      text: "Proposed by other parties",
      img: proposed_image,
      alt: "proposed_image",
      domainValue: "OTHER",
      legendValue: "PPA_30,OTHER",
    },
  ];

  const updateLegendList = (isProptected_Area_changed) => {
    let legendArray = [];
    let reserves_selected_list = [
      { selectedIds: selectedExistingIds, list: existing_list },
      { selectedIds: selectedProposed_2025_Ids, list: proposed_2025_list },
      { selectedIds: selectedProposed_2030_Ids, list: proposed_2030_list },
    ];

    let reserves_selected_Legned = [];

    reserves_selected_list.forEach((item) => {
      item.selectedIds.forEach((id) => {
        let legendValue = item.list.find((x) => x.id == id).legendValue;
        let layerLegened = props.map.__mapInfo.info.$legends.find(
          (x) => x.layerName == "PROTECTED_AREA_BOUNDARY"
        ).legend;

        let selectLenged = layerLegened.find((l) => l.values[0] == legendValue);

        if (selectLenged) {
          reserves_selected_Legned.push({
            image: selectLenged.imageData,
            label: selectLenged.label,
            layerName: "PROTECTED_AREA_BOUNDARY",
            where:
              "EN_PROTECTED_AREA_STATUS = '" +
              legendValue.split(",")[0] +
              "' and EN_GOVERNANCE_AUTHORITY ='" +
              legendValue.split(",")[1] +
              "'",
          });
        }
      });
    });

    if (reserves_selected_Legned.length) {
      legendArray.push({
        layerName: "PROTECTED_AREA_BOUNDARY",
        icon: protected_area_image,
        label: t("System of protected areas"),
        list: reserves_selected_Legned
      });
    }

    selectedEcosystemsId.forEach((id) => {
      let legendValue = ecosystem_list.find((x) => x.id == id);
      let layerLegened = props.map.__mapInfo.info.$legends.find(
        (x) => x.layerName == legendValue.layerName
      ).legend;

      if (layerLegened.length) {
        if (layerLegened.length == 1) {
          let selectLenged = layerLegened[0];
          legendArray.push({
            image: selectLenged.imageData,
            label: t(legendValue.text),
            layerName: legendValue.layerName,
          });
        } else {
          legendArray.push({
            icon: legendValue.img,
            label: t(legendValue.text),
            layerName: legendValue.layerName,
            list: layerLegened.map((f) => {
              return {
                image: f.imageData,
                label: f.label,
                layerName: legendValue.layerName,
                values: f.values ? f.values[0] : null,
                where: legendValue.uniqueValueRenderer.split(",").length > 1?
                  (legendValue.uniqueValueRenderer.split(",")[0] + " = '" +
                    f.values[0]?.split(",")[0] +
                    "' and "+ legendValue.uniqueValueRenderer.split(",")[1] + "='" +
                    f.values[0]?.split(",")[1] +
                    "'") :
                  legendValue.uniqueValueRenderer + (f.values ? "='" + f.values[0] + "'" : "=" + null),
              };
            }),
          });
        }
      }
    });

    selectedWildId.forEach((id) => {
      let legendValue = wild_list.find((x) => x.id == id).legendValue;
      let layerLegened = props.map.__mapInfo.info.$legends.find(
        (x) => x.layerName == "TERRESTRIAL_SPECIE"
      ).legend;

      let selectLenged = layerLegened.find((l) => l.values[0] == legendValue);

      if (selectLenged) {
        legendArray.push({
          image: selectLenged.imageData,
          label: selectLenged.label,
          where:
            "AR_NCW_CATEGORY = '" +
            wild_list.find((x) => x.id == id).domainValue +
            "'",
          layerName: "TERRESTRIAL_SPECIE",
        });
      }
    });

    selectedMarineOrganismsId.forEach((id) => {
      let legendValue = marine_organisms_list.find(
        (x) => x.id == id
      ).legendValue;
      let layerLegened = props.map.__mapInfo.info.$legends.find(
        (x) => x.layerName == "MARINE_SPECIE"
      ).legend;

      let selectLenged = layerLegened.find((l) => l.values[0] == legendValue);

      if (selectLenged) {
        legendArray.push({
          image: selectLenged.imageData,
          label: selectLenged.label,
          where:
            "AR_NCW_CATEGORY = '" +
            marine_organisms_list.find((x) => x.id == id).domainValue +
            "'",
          layerName: "MARINE_SPECIE",
        });
      }
    });

    selectedHabitatsId.forEach((id) => {
      let legendValue = habitats_list.find((x) => x.id == id);
      let layerLegened = props.map.__mapInfo.info.$legends.find(
        (x) => x.layerName == legendValue.layerName
      ).legend;

      if (layerLegened.length) {
        let selectLenged = layerLegened[0];
        legendArray.push({
          image: selectLenged.imageData,
          label: t(legendValue.text),
          layerName: legendValue.layerName,
        });
      }
    });

    eventBus.dispatch("updateLegendList", { message: legendArray, isProptected_Area_changed: isProptected_Area_changed });
  };

  useEffect(() => {
    const intalizeLayers = async () => {
      handle = watchUtils.watch(props.map.view, ["updating"], (e) => {
        // Do something
        if (e == false) {
          showLoading(e);
        }
      });

      eventBus.dispatch("openIdentify", { message: { show: true } });

      showLoading(true);
      let isAddFeatureLayer = false;

      const cameraId = getLayerInfo(props.map.__mapInfo, "BROADCAST_CAMERA").id;
     // window.handles.remove("map-events");
      window.handles.push(props.map.view.on("pointer-move", (event) => {
        props.map.view.hitTest(event).then((response) => {
          const tooltip = document.getElementById("hovertooltip");
          const results = response.results.filter(
            (result) => result.graphic.layer?.layerId === cameraId
          );

          if (results.length > 0) {
            const graphic = results[0].graphic;
            const attrs = graphic.attributes; // Modify field as needed
            tooltip.style.display = "block";
            tooltip.style.left = event.x + 10 + "px";
            tooltip.style.top = event.y + 10 + "px";
            tooltip.innerHTML = `<strong>${" ( " +
              attrs.LOCATION_DESCRIPTION +
              " - Cam" +
              attrs.BROADCAST_CAMERA_ID +
              " ) "
              }</strong>`;
          } else {
            tooltip.style.display = "none";
          }
        });
      }));

      window.handles.push(props.map.view.on("click", (event) => {
        props.map.view.hitTest(event).then((response) => {
          const graphic = response.results.find(
            (result) => result.graphic.layer?.layerId === cameraId
          )?.graphic;

          if (graphic) {
            //console.log("Clicked Feature Attributes:", graphic.attributes);
            setTimeout(() => {
              props.map.view.popup.close();
            }, 500);

            eventBus.dispatch("openIdentify", { message: { show: false } });

            setCameraInfo(graphic.attributes);
            setOpenCamerasModal(true);
          }
        });
      }));

      for (const [index, layer_name] of editingLayers.entries()) {
        let layerInfo = getLayerInfo(props.map.__mapInfo, layer_name);
        let layerId = layerInfo.id;

        let identifyLayersFields =
          props.mainData.identifyLayers[layer_name]?.fields;
        let dbFields = props.mainData.identifyLayers[layer_name]?.outFields_Db;

        identifyLayersFields = identifyLayersFields?.filter(
          (x) => !dbFields.find((y) => y.name == x.fieldName)?.is_hidden
        );

        let flLayer = new FeatureLayer(window.mapUrl + "/" + layerId, {
          id: "fl_" + layerId,
          outFields: identifyLayersFields
            ? ["OBJECTID", ...identifyLayersFields.map((f) => f.fieldName)]
            : ["OBJECTID"],
          visible: defaultVisibleLayers.indexOf(layer_name) > -1,
          /*popupTemplate: identifyLayersFields ? {
            title: identifyLayersFields ? identifyLayersFields[0].alias + ":{" + identifyLayersFields[0].fieldName + "}" : null,
            content: "<table>" + 
              identifyLayersFields.map((field) =>
              "<tr><td>" + field.alias + "</td><td>{" + field.fieldName + "}</td></tr>") +
              "</table>"
          } : null*/
          /*popupTemplate: identifyLayersFields
            ? {
              title: "{" + identifyLayersFields[0].fieldName + "}",
              content: [
                {
                  type: "fields",
                  fieldInfos: identifyLayersFields.map((field) => {
                    return { fieldName: field.fieldName, label: field.alias };
                  }),
                },
              ],
            }
            : "",*/
        });

        if (layer_name == "REGION") {
          let info = await getFromEsriRequest(
            window.mapUrl + "/" + layerId + "?f=pjson"
          );

          info.drawingInfo.labelingInfo[0].labelExpressionInfo.expression =
            "$feature.AR_REGION_NAME";
          info.drawingInfo.labelingInfo[0].labelPlacement = "always-horizontal";
          info.drawingInfo.labelingInfo[0].symbol.type = "text";

          flLayer.labelingInfo = info.drawingInfo?.labelingInfo;
          flLayer.labelsVisible = true;
        }

        if (
          props.map.__mapInfo.info.$layers.layers.find(
            (x) => x.name == layer_name
          ).geometryType == "esriGeometryPoint" &&
          layer_name != "ECO_HOTSPOT" &&
          hidePopUpsLayers.indexOf(layer_name) <= -1
        ) {
          enableCluster(flLayer, true);
        }

        if (layer_name == "BROADCAST_CAMERA") {
          flLayer.outFields = [
            "OBJECTID",
            "BROADCAST_CAMERA_ID",
            "LOCATION_DESCRIPTION",
          ];
        }

        /*else {
          flLayer.popupTemplate = null;
        }*/

        if (!props.map.findLayerById("fl_" + layerId)) {
          isAddFeatureLayer = true;
          props.map.add(flLayer);
          props.map.layers.reorder(flLayer, index + 1);
          props.map.view.whenLayerView(flLayer).then((layerView) => {
            // if a feature is already highlighted, then remove the highlight
            flLayer.__layerView = layerView;
            if (layer_name == "PROTECTED_AREA_BOUNDARY") {
              layerView.watch("updating", function (isUpdating) {
                if (!isUpdating) {


                  if (!constProtectedAreaFeatures.length) {
                    let query = flLayer.createQuery();
                    query.where = "1=1";
                    flLayer.__layerView.queryFeatures(query).then((result) => {
                      constProtectedAreaFeatures = result.features;
                    });
                  }
                  //highlightProtectArea(toggleProtectedAreaRef.current);
                }
              });
            }

          });
        }

        if (layer_name == "BROADCAST_CAMERA") {
          flLayer.when(() => {
            // Get the current renderer
            const renderer = flLayer.renderer;

            // Check if the renderer is a SimpleRenderer
            if (renderer && renderer.type === "simple") {
              // Get the current symbol
              const symbol = renderer.symbol;

              // Check if the symbol is a SimpleMarkerSymbol
              // Update the symbol size
              symbol.width = 30; // New size
              symbol.height = 30; // New size

              // Refresh the layer to apply the changes
              flLayer.refresh();
            }
          });
        }
      }

      setTimeout(() => {
        if (!isAddFeatureLayer) showLoading(false);
      }, 1000);

      
      hideFeatureLayerFromBasemap();

      //props.map.findLayerById("baseMap").visible = false;
    };

    intalizeLayers();
  }, []);

  const hideFeatureLayerFromBasemap=()=>{
    
    editingLayers.forEach((layer)=>
    {
      props.map.findLayerById("baseMap")
      .allSublayers.items.find((x) => x.title == layer).visible = false;
  
    });    
  }

  useEffect(() => {
    return () => {
      //sessionStorage.setItem("isOpenedBefore", true);

      //fire when component closed
      /*handleClearProtectedAreasAfterCloseControl(true);
      handleClearEcosystem(true);
      handleClearBioDiversity(true);
      */

      eventBus.dispatch("openIdentify", { message: { show: false } });

      let hideLayers = props.map.layers.items.filter(
        (x) => x.id.indexOf("fl_") > -1 && !x.visible
      );
      hideLayers.forEach((layer) => {
        layer.destroy();
      });

      handle.remove();
      eventBus.dispatch("updateLegendList", { hideLegend: true });

      //props.map.findLayerById("baseMap").visible = true;
    };
  }, []);

  /************** start first box states *****************/

  /***
   * show protected areas
   */
  const [showProtectedAreas, setShowProtectedAreas] = usePersistentState(
    "showProtectedAreas",
    true,
    componentName
  );

  /***
   * protected areas
   */
  const [selectedProtectedArea, setSelectedProtectedArea] = usePersistentState(
    "selectedProtectedArea",
    "",
    componentName
  );

  /**
   * selected existing list id
   */
  const [selectedExistingIds, setSelectedExistingIds] = usePersistentState(
    "selectedExistingIds",
    existing_list.map((x) => x.id),
    componentName
  );

  /**
   * selected proposed ids
   */
  const [selectedProposed_2025_Ids, setSelectedProposed_2025_Ids] =
    usePersistentState(
      "selectedProposed_2025_Ids",
      proposed_2025_list.map((x) => x.id),
      componentName
    );
  const [selectedProposed_2030_Ids, setSelectedProposed_2030_Ids] =
    usePersistentState(
      "selectedProposed_2030_Ids",
      proposed_2030_list.map((x) => x.id),
      componentName
    );

  /************** end first box states *****************/

  /************** start second box states *****************/

  /**
   * show ecosystem items
   */
  const [showEcosystemItems, setShowEcosystemItems] = usePersistentState(
    "showEcosystemItems",
    false,
    componentName
  );

  /***
   * ecosystem
   */
  const [selectedEcosystemsId, setSelectedEcosystemsId] = usePersistentState(
    "selectedEcosystemsId",
    [],
    componentName
  );

  /************** end second box states *****************/

  /************** start third box states *****************/

  /**
   * show bioDiversity list
   */
  const [showBioDiversityList, setShowBioDiversityList] = usePersistentState(
    "showBioDiversityList",
    false,
    componentName
  );

  /**
   * diversity
   */
  const [selectedDiversity, setSelectedDiversity] = usePersistentState(
    "selectedDiversity",
    "",
    componentName
  );

  /**
   * selected nautical item
   */
  const [selectedNauticalItem, setSelectedNauticalItem] = usePersistentState(
    "selectedNauticalItem",
    "",
    componentName
  );

  /***
   * selected Marine organisms id
   */
  const [selectedMarineOrganismsId, setSelectedMarineOrganismsId] =
    usePersistentState("selectedMarineOrganismsId", [], componentName);

  /**
   * selected Habitats id
   */
  const [selectedHabitatsId, setSelectedHabitatsId] = usePersistentState(
    "selectedHabitatsId",
    [],
    componentName
  );

  /***
   * selected wild id
   */
  const [selectedWildId, setSelectedWildId] = usePersistentState(
    "selectedWildId",
    [],
    componentName
  );

  /***
   * forth box
   */
  const [showSurveillanceCameras, setShowSurveillanceCameras] =
    usePersistentState("showSurveillanceCameras", false, componentName);

  const [selectedSurveillanceCamerasId, setSelectedSurveillanceCamerasId] =
    usePersistentState("selectedSurveillanceCamerasId", [], componentName);

  const [openCamerasModal, setOpenCamerasModal] = useState(false);

  let updatedDevicesList = [
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060136690",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060140390",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060142420",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060143360",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060143400",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060144410",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060147390",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060148610",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060341360",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060348360",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060349360",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060440130",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060441840",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060442230",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060443130",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060445210",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060445490",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060448940",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060449220",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060449840",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060449960",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060541170",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060541660",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060542160",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060544190",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060544450",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060544570",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060545030",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060546150",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Emergency Call",
      x: "42.644190000000002",
      y: "23.406001700000001",
      code: "300425060546570",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060547300",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060643080",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060644570",
    },
    {
      date: "02/24/2025 14:30:00",
      group: "SAT-100",
      calltype: "Voice Call",
      x: "+16.7389150",
      y: "+42.1130767",
      code: "300425060649550",
    },
  ];

  useEffect(() => {
    const getDevicesUpdates = async () => {
      try {
        const wirelessLayer = props.map.findLayerById("WIRELESS");
        if (wirelessLayer) {
          //  const response = await fetch("http://10.10.0.200:5010/get-new-logs");
          //  const data = await response.json();
          updatedDevicesList.forEach((item) => {
            const matchingGraphic = wirelessLayer.graphics.items.find(
              (graphic) => graphic.attributes?.code === item.code
            );
            if (matchingGraphic) {
              if (item.calltype.toLowerCase() == "voice call") {
                matchingGraphic.attributes.isVoiceCall = true;

                matchingGraphic.symbol = new PictureMarkerSymbol({
                  url: voiceCallIcon,
                  width: "30px",
                  height: "30px",
                });

                matchingGraphic.geometry = {
                  type: "point",
                  longitude: parseFloat(item.x),
                  latitude: parseFloat(item.y),
                };
              } else if (item.calltype.toLowerCase() == "emergency call") {
                matchingGraphic.attributes.isEmergencyCall = true;

                matchingGraphic.symbol = new PictureMarkerSymbol({
                  url: emergencyIcon,
                  width: "30px",
                  height: "30px",
                });

                matchingGraphic.geometry = {
                  type: "point",
                  longitude: parseFloat(item.x),
                  latitude: parseFloat(item.y),
                };
              }
            }
          });
        }
      } catch (error) {
        console.error("Failed to fetch logs:", error);
      }
    };

    let intervalId;
    if (selectedSurveillanceCamerasId.includes("WIRELESS")) {
      console.log("wirless on");
      intervalId = setInterval(getDevicesUpdates, 6000);
      //  getDevicesUpdates();
    } else {
      console.log("wireless off");
      clearInterval(intervalId);
    }

    return () => clearInterval(intervalId);
  }, [selectedSurveillanceCamerasId]);

  const handleSelectSurveillanceCameras = async (id) => {
    if (selectedSurveillanceCamerasId.includes(id)) {
      setSelectedSurveillanceCamerasId(
        selectedSurveillanceCamerasId.filter((i) => i !== id)
      );
    } else {
      setSelectedSurveillanceCamerasId([...selectedSurveillanceCamerasId, id]);
    }

    if (id == "WIRELESS") {
      let wirelessLayer = props.map.findLayerById("WIRELESS");

      if (wirelessLayer) {
        wirelessLayer.visible = !wirelessLayer.visible;
      } else {
        wirelessLayer = new GraphicsLayer({
          id: "WIRELESS",
          visible: true,
        });
        props.map.add(wirelessLayer);
        await initDevicesFromKML(wirelessLayer);
      }
    } else {
      let layerId = getLayerId(props.map.__mapInfo, id);
      let selectedFeatureLayer = props.map.findLayerById("fl_" + layerId);
      if (layerId) {
        if (!selectedFeatureLayer.visible) showLoading(true);

        if (!selectedFeatureLayer.visible) {
          let count =
            await selectedFeatureLayer.__layerView.queryFeatureCount();
          if (count > 0) {
            showLoading(false);
          }
        }
        selectedFeatureLayer.visible = !selectedFeatureLayer.visible;
      }
    }
  };

  const initDevicesFromKML = async (wirelessLayer) => {
    try {
      const response = await fetch("devices.kml");
      const kmlText = await response.text();

      const parser = new DOMParser();
      const kmlDoc = parser.parseFromString(kmlText, "application/xml");
      const placemarks = kmlDoc.querySelectorAll("Placemark");
      let devicesGraphics = [];
      placemarks.forEach((placemark) => {
        const code = placemark.querySelector("name")?.textContent;
        const coordText = placemark.querySelector("coordinates")?.textContent;

        const [long, lat] = coordText.split(",").map(Number);

        const point = new Point({ latitude: lat, longitude: long });
        const attributes = { code, isVoiceCall: false, isEmergencyCall: false };
        const graphic = new Graphic({
          id: `wireless_graphic_${code}`,
          geometry: point,
          attributes: { code, isVoiceCall: false, isEmergencyCall: false },
          symbol: new PictureMarkerSymbol({
            url: deviceIcon,
            width: "30px",
            height: "30px",
          }),
        });
        devicesGraphics.push(attributes);
        wirelessLayer.add(graphic);
      });

      console.log("devices graphics", devicesGraphics);
    } catch (err) {
      console.error("Failed to load KML file:", err);
    }

    console.log("wirless layer : ", wirelessLayer);
  };

  const handleClearSurveillanceCameras = () => {
    setSelectedSurveillanceCamerasId([]);

    let layerId = getLayerId(props.map.__mapInfo, "BROADCAST_CAMERA");
    props.map.findLayerById("fl_" + layerId).visible = false;
  };

  /************** end third box states *****************/

  // useEffect(() => {
  //   if (selectedProtectedAreasId.length != 1) {
  //     setSelectedButtonsId([]);
  //   }

  //   // setShowButtons(selectedProtectedAreasId.length == 1);
  // }, [selectedProtectedAreasId.length]);

  const enableCluster = (flLayer, isEnable) => {
    flLayer.featureReduction = isEnable
      ? {
        type: "cluster",
        clusterMinSize: 18,
        labelingInfo: [
          {
            deconflictionStrategy: "none",
            labelExpressionInfo: {
              expression: "Text($feature.cluster_count, '#,###')",
            },
            symbol: {
              type: "text",
              color: "white",
              font: {
                weight: "bold",
                family: "Noto Sans",
                size: "14px",
              },
            },
            labelPlacement: "center-center",
          },
        ],
        // information to display when the user clicks a cluster
        /*popupTemplate: {
      title: "تفاصيل العناصر",
      content: "عدد العناصر : <b>{cluster_count}</b>",
      fieldInfos: [
        {
          fieldName: "cluster_count",
          format: {
            places: 0,
            digitSeparator: true,
          },
        },
      ],
    },*/
      }
      : null;
  };

  /**
   * handleSelectProtectedAreas
   */
  const handleSelectProtectedAreas = (text, id) => {
    /*if (selectedProtectedAreasId.includes(id)) {
      setSelectedProtectedAreasId(
        selectedProtectedAreasId.filter((i) => i !== id)
      );
      setProtectedAreasDefinationQuery(
        selectedProtectedAreasId.filter((i) => i !== id)
      );
    } else {
      setSelectedProtectedAreasId([...selectedProtectedAreasId, id]);
      setProtectedAreasDefinationQuery([...selectedProtectedAreasId, id]);
    }*/

    setFirstIntalize(true);

    if (text != selectedProtectedArea) {
      setSelectedProtectedArea(text);
      //setProtectedAreasDefinationQuery();

      //setProtectedAreasDefinationQuery([id]);
      //setSelectedProposedIds([]);
    }
    /*else {
      setSelectedProtectedArea(null);
      //setProtectedAreasDefinationQuery([]);
      //setSelectedProposedIds([]);
      //setSelectedExistingIds([]);
    }*/
  };

  React.useEffect(() => {
    if (firstIntalize) {
      setProtectedAreasDefinationQuery();
    }

    let isProptected_Area_changed = false;
    if (prevSelectionValues.current.selectedExistingIds !== selectedExistingIds) {
      isProptected_Area_changed = true;
    }
    if (prevSelectionValues.current.selectedProposed_2025_Ids !== selectedProposed_2025_Ids) {
      isProptected_Area_changed = true;
    }
    if (prevSelectionValues.current.selectedProposed_2030_Ids !== selectedProposed_2030_Ids) {
      isProptected_Area_changed = true;
    }

    updateLegendList(isProptected_Area_changed);

    // Update previous values
    prevSelectionValues.current = {
      selectedExistingIds,
      selectedProposed_2025_Ids,
      selectedProposed_2030_Ids,
      selectedEcosystemsId,
      selectedWildId,
      selectedMarineOrganismsId,
      selectedHabitatsId,
    };

  }, [
    selectedExistingIds,
    selectedProposed_2025_Ids,
    selectedProposed_2030_Ids,
    selectedEcosystemsId,
    selectedWildId,
    selectedMarineOrganismsId,
    selectedHabitatsId,
  ]);

  /***
   * handle select existing ids
   */
  const handleSelectExistingIds = (id) => {
    if (selectedExistingIds.includes(id)) {
      setSelectedExistingIds(selectedExistingIds.filter((i) => i !== id));
    } else {
      setSelectedExistingIds([...selectedExistingIds, id]);
    }
  };

  /***
   * handle select proposed ids
   */
  const handleSelectProposed_2025_Ids = (id) => {
    if (selectedProposed_2025_Ids.includes(id)) {
      setSelectedProposed_2025_Ids(
        selectedProposed_2025_Ids.filter((i) => i !== id)
      );
    } else {
      setSelectedProposed_2025_Ids([...selectedProposed_2025_Ids, id]);
    }
  };

  const handleSelectProposed_2030_Ids = (id) => {
    if (selectedProposed_2030_Ids.includes(id)) {
      setSelectedProposed_2030_Ids(
        selectedProposed_2030_Ids.filter((i) => i !== id)
      );
    } else {
      setSelectedProposed_2030_Ids([...selectedProposed_2030_Ids, id]);
    }
  };

  /**
   * handleSelectReports
   */
  const handleSelectEcosystems = (id) => {
    if (selectedEcosystemsId.includes(id)) {
      setSelectedEcosystemsId(selectedEcosystemsId.filter((i) => i !== id));
      setEcosystemDefinationQuery(
        selectedEcosystemsId.filter((i) => i !== id),
        ecosystem_list,
        id
      );
    } else {
      setSelectedEcosystemsId([...selectedEcosystemsId, id]);
      setEcosystemDefinationQuery(
        [...selectedEcosystemsId, id],
        ecosystem_list,
        id
      );
    }
  };

  /***
   * handleSelectDiversity
   */
  const handleSelectDiversity = (text) => {
    setSelectedDiversity(text);
  };

  /**
   * handle select Marine organisms
   */
  const handleSelectMarineOrganisms = (id) => {
    if (selectedMarineOrganismsId.includes(id)) {
      setSelectedMarineOrganismsId(
        selectedMarineOrganismsId.filter((i) => i !== id)
      );

      setSelectedWilDefinationQuery(
        selectedMarineOrganismsId.filter((i) => i !== id),
        marine_organisms_list,
        "MARINE_SPECIE",
        "AR_NCW_CATEGORY"
      );
    } else {
      setSelectedMarineOrganismsId([...selectedMarineOrganismsId, id]);

      setSelectedWilDefinationQuery(
        [...selectedMarineOrganismsId, id],
        marine_organisms_list,
        "MARINE_SPECIE",
        "AR_NCW_CATEGORY"
      );
    }
  };

  /**
   * handle select Habitat
   */
  const handleSelectHabitats = (id) => {
    if (selectedHabitatsId.includes(id)) {
      setSelectedHabitatsId(selectedHabitatsId.filter((i) => i !== id));

      setEcosystemDefinationQuery(
        selectedHabitatsId.filter((i) => i !== id),
        habitats_list,
        id
      );
    } else {
      setSelectedHabitatsId([...selectedHabitatsId, id]);
      setEcosystemDefinationQuery(
        [...selectedHabitatsId, id],
        habitats_list,
        id
      );
    }
  };

  /***
   * handle select wild
   */
  const handleSelectWild = (id) => {
    if (selectedWildId.includes(id)) {
      setSelectedWildId(selectedWildId.filter((i) => i !== id));
      setSelectedWilDefinationQuery(
        selectedWildId.filter((i) => i !== id),
        wild_list,
        "TERRESTRIAL_SPECIE",
        "AR_NCW_CATEGORY"
      );
    } else {
      setSelectedWildId([...selectedWildId, id]);
      setSelectedWilDefinationQuery(
        [...selectedWildId, id],
        wild_list,
        "TERRESTRIAL_SPECIE",
        "AR_NCW_CATEGORY"
      );
    }
  };

  const setSelectedWilDefinationQuery = (
    selectedList,
    domainList,
    layerName,
    filterField
  ) => {
    showLoading(true);
    let filters = [];
    let where = "OBJECTID = -1";
    let layerId = getLayerId(props.map.__mapInfo, layerName);

    window.__SPECIE_CATEGORY = [];
    selectedList.forEach((item) => {
      let value = domainList.find(
        (x) => x.id == item || x.text == item
      ).domainValue;

      if (window.__SPECIE_CATEGORY.indexOf(value) < 0)
        window.__SPECIE_CATEGORY.push(value);

      filters.push(filterField + " = '" + value + "'");
    });

    if (selectedList?.length > 0) {
      where = filters.join(" or ");
    }

    props.map.findLayerById("fl_" + layerId).visible = true;

    props.map.findLayerById("fl_" + layerId).definitionExpression = where;
  };

  const setEcosystemDefinationQuery = async (reportsIds, layerList, id) => {
    showLoading(true);

    let layerId = getLayerId(
      props.map.__mapInfo,
      layerList.find((x) => x.id == id).layerName
    );
    let selectedFeatureLayer = props.map.findLayerById("fl_" + layerId);
    if (!selectedFeatureLayer.visible) {
      let count = await selectedFeatureLayer.__layerView.queryFeatureCount();
      if (count > 0) {
        showLoading(false);
      }
    }
    selectedFeatureLayer.visible = !selectedFeatureLayer.visible;
    if (!selectedFeatureLayer.visible) {
      showLoading(false);
    }
  };

  const getWhereCondition = (filterSelectedList, filterList) => {
    let where;
    if (filterSelectedList?.length > 0) {
      let govFilters = [];

      filterSelectedList.forEach((item) => {
        govFilters.push(
          "EN_GOVERNANCE_AUTHORITY = '" +
          filterList.find((x) => x.id == item).domainValue +
          "'"
        );
      });

      where = govFilters.join(" or ");
    }

    return where;
  };
  const setProtectedAreasDefinationQuery = () => {
    showLoading(true);

    let filters = [];
    let layerId = getLayerId(props.map.__mapInfo, "PROTECTED_AREA_BOUNDARY");

    protected_areas_list.forEach((item) => {
      let where =
        item.text == "existing"
          ? getWhereCondition(selectedExistingIds, existing_list)
          : item.text == "proposed_2025"
            ? getWhereCondition(selectedProposed_2025_Ids, proposed_2025_list)
            : getWhereCondition(selectedProposed_2030_Ids, proposed_2030_list);

      if (where) {
        // filters.push(
        //   "(RESERVESTATUS = '" +
        //     item.domainValue +
        //     "'" +
        //     " and ( " +
        //     where +
        //     "))"
        // );

        filters.push(
          "(EN_PROTECTED_AREA_STATUS = '" +
          item.domainValue +
          "'" +
          " and ( " +
          where +
          "))"
        );
      }
    });

    if (
      props.map.findLayerById("fl_" + layerId).definitionExpression ==
      (filters.join(" or ") || "OBJECTID = -1")
    ) {
      showLoading(false);
    }

    props.map.findLayerById("fl_" + layerId).definitionExpression =
      filters.join(" or ") || "OBJECTID = -1";

  };

  /***
   * handleClearProtectedAreas
   */
  const handleClearProtectedAreasAfterCloseControl = (isClosed) => {
    let reservesLayerId = getLayerId(
      props.map.__mapInfo,
      "PROTECTED_AREA_BOUNDARY"
    );
    props.map.findLayerById("fl_" + reservesLayerId).definitionExpression =
      null;
  };

  const handleClearProtectedAreas = () => {
    showLoading(true);

    setSelectedProtectedArea("");
    //setShowProtectedAreas(false);
    setFirstIntalize(false);

    setSelectedExistingIds([]);
    setSelectedProposed_2025_Ids([]);
    setSelectedProposed_2030_Ids([]);

    let reservesLayerId = getLayerId(
      props.map.__mapInfo,
      "PROTECTED_AREA_BOUNDARY"
    );
    // let reservesLayerId = getLayerId(
    //   props.map.__mapInfo,
    //   "PROTECTED_AREA_BOUNDARY"
    // );
    props.map.findLayerById("fl_" + reservesLayerId).definitionExpression =
      "OBJECTID=-1";

    //props.map.findLayerById("fl_" + reservesLayerId).refresh();
  };

  /**
   * handleClearReports
   */
  const handleClearEcosystem = (isClosed) => {
    //if (!isClosed) showLoading(true);

    setSelectedEcosystemsId([]);

    ecosystem_list.forEach((item) => {
      let layerId = getLayerId(props.map.__mapInfo, item.layerName);

      props.map.findLayerById("fl_" + layerId).visible = false;
    });
  };

  /**
   * handleClearBioDiversity
   */
  const handleClearBioDiversity = (isClosed) => {
    //if (!isClosed) showLoading(true);

    setSelectedDiversity("");
    setSelectedNauticalItem("");
    setSelectedMarineOrganismsId([]);
    setSelectedHabitatsId([]);
    setSelectedWildId([]);

    clearWildData();

    //props.map.findLayerById("baseMap").refresh();
    //setShowBioDiversityList(false);
  };

  const clearWildData = () => {
    let layerId = getLayerId(props.map.__mapInfo, "TERRESTRIAL_SPECIE");
    props.map.findLayerById("fl_" + layerId).visible = false;

    layerId = getLayerId(props.map.__mapInfo, "MARINE_SPECIE");
    props.map.findLayerById("fl_" + layerId).visible = false;

    habitats_list.forEach((item) => {
      let layerId = getLayerId(props.map.__mapInfo, item.layerName);
      props.map.findLayerById("fl_" + layerId).visible = false;
    });
  };

  /***
   * Protected areas list
   */
  const protected_areas_list = [
    {
      id: 1,
      text: "existing",
      img: existing_image,
      alt: "existing",
      domainValue: "PA",
    },
    {
      id: 2,
      text: "proposed_2025",
      img: proposed_2025_image,
      alt: "proposed_2025",
      domainValue: "PPA_25",
    },
    {
      id: 3,
      text: "proposed_2030",
      img: proposed_2030_image,
      alt: "proposed_2030",
      domainValue: "PPA_30",
    },
  ];

  /**
   * Reports list
   */
  const ecosystem_list = [
    {
      id: 1,
      img: ecosystem_item_image,
      alt: "Ecosystems",
      text: "Ecosystems",
      layerName: "ECO_SYSTEM",
      uniqueValueRenderer: "LEGEND",
    },
    {
      id: 2,
      img: environmental_regions_img,
      alt: "Environmental regions",
      text: "Environmental regions",
      layerName: "ECO_REGION",
      uniqueValueRenderer: "EN_ECOREGION",
    },
    {
      id: 3,
      img: Wetlands_img,
      alt: "Wetlands",
      text: "Wetlands",
      layerName: "WET_LAND",
    },
    {
      id: 4,
      img: sites_img,
      alt: "Important sites",
      text: "Important sites",
      layerName: "ECO_HOTSPOT",
      uniqueValueRenderer: "EN_KEY_BIOLOGY,EN_PRIORITY"
    },
  ];

  const bio_diversity_list = [
    {
      id: 1,
      img: nautical_img,
      alt: "nautical_img",
      text: "nautical",
    },
    {
      id: 2,
      img: wild_img,
      alt: "wild_img",
      text: "wild",
    },
  ];

  const surveillance_cameras_list = [
    {
      id: "",
      img: cameras,
      alt: "surveillance_cameras_image",
      text: "cameras",
    },
    {
      id: "WIRELESS",
      img: wireless,
      alt: "wireless_devices",
      text: "wireless_devices",
    },
    {
      id: "BROADCAST_CAMERA",
      img: baboon,
      alt: "surveillance_cameras_image",
      text: "baboon_monitoring",
    },
  ];

  const nautical_list = [
    {
      id: 1,
      img: marine_organisms_img,
      alt: "marine_organisms_img",
      text: "Marine organisms",
    },
    {
      id: 2,
      img: habitats_img,
      alt: "habitats_img",
      text: "Habitats",
    },
  ];

  const marine_organisms_list = [
    {
      id: 1,
      img: fish_img,
      alt: "fish_img",
      text: "fish",
      domainValue: "الأسماك",
      legendValue: "الأسماك",
    },
    {
      id: 2,
      img: nautical_img,
      alt: "nautical_img",
      text: "Marine mammals",
      domainValue: "الثدييات البحرية",
      legendValue: "الثدييات البحرية",
    },
    {
      id: 3,
      img: marine_organisms_img,
      alt: "marine_organisms_img",
      text: "Sea turtles",
      domainValue: "السلاحف",
      legendValue: "السلاحف",
    },
  ];

  const habitats_list = [
    {
      id: 1,
      img: mangrove_img,
      alt: "mangrove_img",
      text: "Mangrove",
      layerName: "MANGROVE",
    },
    {
      id: 2,
      img: coral_reefs_img,
      alt: "coral_reefs_img",
      text: "Coral reefs",
      layerName: "CORAL_REEF",
    },
    {
      id: 3,
      img: seaweed_img,
      alt: "seaweed_img",
      text: "Seaweed",
      layerName: "SEA_GRASS",
    },
  ];

  const wild_list = [
    {
      id: 1,
      img: mammals_img,
      alt: "mammals_img",
      text: "Mammals",
      domainValue: "الثدييات",
      legendValue: "الثدييات",
    },
    {
      id: 2,
      img: reptiles_img,
      alt: "reptiles_img",
      text: "Reptiles",
      domainValue: "الزواحف",
      legendValue: "الزواحف",
    },
    {
      id: 3,
      img: Invertebrates_img,
      alt: "Invertebrates_img",
      text: "Invertebrates",
      domainValue: "اللافقاريات",
      legendValue: "اللافقاريات",
    },
    {
      id: 4,
      img: birds_img,
      alt: "birds_img",
      text: "birds",
      domainValue: "الطيور",
      legendValue: "الطيور",
    },
    {
      id: 5,
      img: marine_organisms_img,
      alt: "marine_organisms_img",
      text: "Amphibians",
      domainValue: "البرمائيات",
      legendValue: "البرمائيات",
    },
    {
      id: 6,
      img: reserves_image,
      alt: "reserves_image",
      text: "plants",
      domainValue: "النباتات",
      legendValue: "النباتات",
    },
  ];

  const onChangeSwitchProtectedArea = (checked) => {
    setIsProtectedAreaToggled(checked);
    highlightProtectArea(checked);
  }

  const highlightProtectArea = (checked) => {
    let switcLayer = props.map.findLayerById("switch_PROTECTED_AREA_BOUNDARY");
    if (checked) {
      if (!switcLayer) {
        var graphicLayer = new GraphicsLayer({
          id: "switch_PROTECTED_AREA_BOUNDARY",
          opacity: 1,
        });
        props.map.layers.add(graphicLayer);
      }
      else {
        switcLayer.visible = true;
      }
      if (constProtectedAreaFeatures?.length) {
        highlightFeature(constProtectedAreaFeatures, props.map, {
          layerName: "switch_PROTECTED_AREA_BOUNDARY",
          isHighlighPolygonBorder: true
        });
      }
    }
    else {
      if (switcLayer) {
        switcLayer.visible = false;
      }
    }
  }

  const onChangeSwitchButton = (checked) => {
    setIsToggled(checked);
    editingLayers.forEach((layer_name) => {
      let layerInfo = getLayerInfo(props.map.__mapInfo, layer_name);
      let layerId = layerInfo.id;
      let flLayer = props.map.findLayerById("fl_" + layerId);

      if (
        props.map.__mapInfo.info.$layers.layers.find(
          (x) => x.name == layer_name
        ).geometryType == "esriGeometryPoint" &&
        layer_name != "ECO_HOTSPOT"
      ) {
        enableCluster(flLayer, checked);
      }
    });
  };

  return (
    <div className="layersMenuPage">
      {/* first box */}
      <div className="box">
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <div style={{ display: "flex", gap: "5px" }}>
            <img src={protected_area_image} alt="protected_area_image" />
            <div style={{ textAlign: "right" }}>{t("System of protected areas")}</div>
          </div>

          <div style={{ display: "flex", gap: "5px", alignItems: "center" }}>
            {showProtectedAreas && (
              <>
                <Tooltip title={t("highlightBorder")}>
                  <Switch checked={isProtectedAreaToggled} onChange={onChangeSwitchProtectedArea} />
                </Tooltip>

                <Button onClick={handleClearProtectedAreas}>
                  {t("removeAll")}
                </Button>
              </>
            )}

            <MdKeyboardArrowDown
              size={20}
              style={{
                cursor: "pointer",
                transform: `rotate(${showProtectedAreas ? "180deg" : 0})`,
              }}
              onClick={() => setShowProtectedAreas(!showProtectedAreas)}
            />
          </div>
        </div>

        {showProtectedAreas && (
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr 1fr",
              gap: "10px",
              marginBlock: "15px",
            }}
          >
            {protected_areas_list.map(({ text, id, img, alt }) => {
              return (
                <div
                  key={id}
                  className={`card ${selectedProtectedArea === text && "active"
                    }`}
                  onClick={() => handleSelectProtectedAreas(text, id)}
                >
                  <img src={img} alt={alt} />
                  <div>{t(text)}</div>
                </div>
              );
            })}
          </div>
        )}

        {showProtectedAreas && (
          <>
            {selectedProtectedArea === "existing" && (
              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr 1fr",
                  gap: "10px",
                  marginBlock: "15px",
                }}
              >
                {existing_list.map(({ id, img, alt, text }) => {
                  return (
                    <div
                      key={id}
                      className={`card ${selectedExistingIds.includes(id) && "active"
                        }`}
                      onClick={() => handleSelectExistingIds(id)}
                      title={t(text)}
                    >
                      <img src={img} alt={alt} />
                      <div>
                        {t(text).length > 20
                          ? t(text).slice(0, 20) + "..."
                          : t(text)}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {selectedProtectedArea === "proposed_2025" && (
              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: "10px",
                  marginBlock: "15px",
                }}
              >
                {proposed_2025_list.map(({ id, img, alt, text }) => {
                  return (
                    <div
                      key={id}
                      className={`card ${selectedProposed_2025_Ids.includes(id) && "active"
                        }`}
                      onClick={() => handleSelectProposed_2025_Ids(id)}
                    >
                      <img src={img} alt={alt} />
                      <div>{t(text)}</div>
                    </div>
                  );
                })}
              </div>
            )}

            {selectedProtectedArea === "proposed_2030" && (
              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: "10px",
                  marginBlock: "15px",
                }}
              >
                {proposed_2030_list.map(({ id, img, alt, text }) => {
                  return (
                    <div
                      key={id}
                      className={`card ${selectedProposed_2030_Ids.includes(id) && "active"
                        }`}
                      onClick={() => handleSelectProposed_2030_Ids(id)}
                    >
                      <img src={img} alt={alt} />
                      <div>{t(text)}</div>
                    </div>
                  );
                })}
              </div>
            )}
          </>
        )}
      </div>

      {/* second box */}
      <div className="box">
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <div style={{ display: "flex", gap: "5px" }}>
            <img src={ecosystem_image} alt="ecosystem_image" />
            <div>{t("Ecosystems")}</div>
          </div>
          <div>
            {showEcosystemItems && (
              <Button onClick={() => handleClearEcosystem()}>
                {t("removeAll")}
              </Button>
            )}

            <MdKeyboardArrowDown
              size={20}
              style={{
                cursor: "pointer",
                transform: `rotate(${showEcosystemItems ? "180deg" : 0})`,
              }}
              onClick={() => setShowEcosystemItems(!showEcosystemItems)}
            />
          </div>
        </div>

        {showEcosystemItems && (
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr 1fr 1fr",
              gap: "10px",
              marginBlock: "15px",
            }}
          >
            {ecosystem_list.map(({ id, img, alt, text }) => {
              return (
                // <Tooltip key={id} title={t(text)}>
                <div
                  key={id}
                  className={`card ${selectedEcosystemsId.includes(id) && "active"
                    }`}
                  onClick={() => handleSelectEcosystems(id)}
                >
                  <img src={img} alt={alt} />
                  <div
                  // style={{
                  //   textWrap: "nowrap",
                  //   overflow: "hidden",
                  //   textOverflow: "ellipsis",
                  // }}
                  >
                    {t(text)}
                  </div>
                </div>
                // </Tooltip>
              );
            })}
          </div>
        )}
      </div>

      {/* third box */}
      <div className="box">
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <div style={{ display: "flex", gap: "5px" }}>
            <img src={bio_diversity_image} alt="bio_diversity_image" />
            <div>{t("Biodiversity")}</div>
          </div>
          <div style={{ display: "flex", gap: "5px", alignItems: "center" }}>
            {showBioDiversityList && (
              <>
                <Tooltip title={t("collectPoints")}>
                  <Switch checked={isToggled} onChange={onChangeSwitchButton} />
                </Tooltip>

                <Button onClick={() => handleClearBioDiversity()}>
                  {t("removeAll")}
                </Button>
              </>
            )}

            <MdKeyboardArrowDown
              size={20}
              style={{
                cursor: "pointer",
                transform: `rotate(${showBioDiversityList ? "180deg" : 0})`,
              }}
              onClick={() => setShowBioDiversityList(!showBioDiversityList)}
            />
          </div>
        </div>

        {showBioDiversityList && (
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr",
              gap: "10px",
              marginBlock: "15px",
            }}
          >
            {bio_diversity_list.map(({ id, img, alt, text }) => {
              return (
                <div
                  key={id}
                  className={`card ${selectedDiversity === text && "active"}`}
                  onClick={() => handleSelectDiversity(text)}
                >
                  <img src={img} alt={alt} />
                  <div>{t(text)}</div>
                </div>
              );
            })}
          </div>
        )}

        {showBioDiversityList && (
          <>
            {selectedDiversity === "nautical" && (
              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: "10px",
                  marginBlock: "15px",
                }}
              >
                {nautical_list.map(({ id, img, alt, text }) => {
                  return (
                    <div
                      key={id}
                      className={`card ${selectedNauticalItem === text && "active"
                        }`}
                      onClick={() => setSelectedNauticalItem(text)}
                    >
                      <img src={img} alt={alt} />
                      <div>{t(text)}</div>
                    </div>
                  );
                })}
              </div>
            )}

            {selectedDiversity === "wild" && (
              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr 1fr",
                  gap: "10px",
                  marginBlock: "15px",
                }}
              >
                {wild_list.map(({ id, img, alt, text }) => {
                  return (
                    <div
                      key={id}
                      className={`card ${selectedWildId.includes(id) && "active"
                        }`}
                      onClick={() => handleSelectWild(id)}
                    >
                      <img src={img} alt={alt} />
                      <div>{t(text)}</div>
                    </div>
                  );
                })}
              </div>
            )}

            {selectedNauticalItem === "Marine organisms" &&
              selectedDiversity === "nautical" && (
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: "1fr 1fr 1fr",
                    gap: "10px",
                    marginBlock: "15px",
                  }}
                >
                  {marine_organisms_list.map(({ id, img, alt, text }) => {
                    return (
                      <div
                        key={id}
                        className={`card ${selectedMarineOrganismsId.includes(id) && "active"
                          }`}
                        onClick={() => handleSelectMarineOrganisms(id)}
                      >
                        <img src={img} alt={alt} />
                        <div>{t(text)}</div>
                      </div>
                    );
                  })}
                </div>
              )}

            {selectedNauticalItem === "Habitats" &&
              selectedDiversity === "nautical" && (
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: "1fr 1fr 1fr",
                    gap: "10px",
                    marginBlock: "15px",
                  }}
                >
                  {habitats_list.map(({ id, img, alt, text }) => {
                    return (
                      <div
                        key={id}
                        className={`card ${selectedHabitatsId.includes(id) && "active"
                          }`}
                        onClick={() => handleSelectHabitats(id)}
                      >
                        <img src={img} alt={alt} />
                        <div>{t(text)}</div>
                      </div>
                    );
                  })}
                </div>
              )}
          </>
        )}
      </div>

      {/* fourth box */}
      <div className="box">
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <div style={{ display: "flex", gap: "5px" }}>
            <img
              src={surveillance_cameras_image}
              alt="surveillance_cameras_image"
            />
            <div>{t("surveillance_cameras")}</div>
          </div>
          <div style={{ display: "flex", gap: "5px", alignItems: "center" }}>
            {showSurveillanceCameras && (
              <>
                <Button onClick={() => handleClearSurveillanceCameras()}>
                  {t("removeAll")}
                </Button>
              </>
            )}

            <MdKeyboardArrowDown
              size={20}
              style={{
                cursor: "pointer",
                transform: `rotate(${showSurveillanceCameras ? "180deg" : 0})`,
              }}
              onClick={() =>
                setShowSurveillanceCameras(!showSurveillanceCameras)
              }
            />
          </div>
        </div>

        {showSurveillanceCameras && (
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr 1fr",
              gap: "10px",
              marginBlock: "15px",
            }}
          >
            {surveillance_cameras_list.map(({ id, img, alt, text }) => {
              return (
                <div
                  key={id}
                  className={`card ${selectedSurveillanceCamerasId.includes(id) && "active"
                    }`}
                  onClick={() => handleSelectSurveillanceCameras(id)}
                >
                  <img src={img} alt={alt} />
                  <div>{t(text)}</div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      <Modal
        title={
          t("live_broadcast") +
          " ( " +
          cameraInfo.LOCATION_DESCRIPTION +
          " - Cam" +
          cameraInfo.BROADCAST_CAMERA_ID +
          " ) "
        }
        open={openCamerasModal}
        onCancel={() => setOpenCamerasModal(false)}
        className="liveBroadcastModal"
        width={800}
      >
        {openCamerasModal && (
          <iframe
            className="iframeliveCamera"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen
            src={window.cameraLiveUrl + cameraInfo.BROADCAST_CAMERA_ID}
            width="100%"
            height="100%"
          />
        )}
      </Modal>
    </div >
  );
}
