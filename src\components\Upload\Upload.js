import * as React from "react";
import { styled } from "@mui/joy/styles";
import Input from "@mui/joy/Input";
import { isArray, last, map } from "lodash";
import Axios from "axios";
import { Grid, Paper, Typography, Button } from "@material-ui/core";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import DeleteIcon from "@mui/icons-material/Delete";
import IconButton from "@mui/material/IconButton";
import styles from "./Loading.module.css";
import addPhoto from "../../assets/icons/addPhoto.svg";
import { IoMdClose } from "react-icons/io";
import { message } from "antd";
import { checkImage } from "../../helper/common_func";

const StyledInput = styled("input")(({ error }) => ({
  border: "none",
  borderRadius: "6px",
  minWidth: 0,
  outline: 0,
  padding: 0,
  paddingTop: "1em",
  flex: 1,
  color: error ? "red" : "inherit",
  textAlign: "right",
  backgroundColor: "transparent",
  fontFamily: "inherit",
  fontSize: "inherit",
  fontStyle: "inherit",
  fontWeight: "inherit",
  lineHeight: "inherit",
  "&::placeholder": {
    color: "#A8A8A8", // Fallback color for placeholder
  },
  "&:focus ~ label, &:not(:placeholder-shown) ~ label, &:-webkit-autofill ~ label":
    {
      top: "0.5rem",
      fontSize: "0.75rem",
    },
  "&:focus ~ label": {
    color: error ? "red" : "#A8A8A8",
  },
  // "&:-webkit-autofill:not(* + &)": {
  //   marginInlineStart: "calc(-1 * var(--Input-paddingInline))",
  //   paddingInlineStart: "var(--Input-paddingInline)",
  //   borderTopLeftRadius:
  //     "calc(var(--Input-radius) - var(--variant-borderWidth, 0px))",
  //   borderBottomLeftRadius:
  //     "calc(var(--Input-radius) - var(--variant-borderWidth, 0px))",
  // },
}));

const StyledLabel = styled("label")({
  position: "absolute",
  lineHeight: 1,
  top: "calc(50% - 8px)",
  color: "#A8A8A8",
  fontWeight: 700,
  fontSize: "12px",
  transition: "all 250ms ease-out", // Faster, simpler transition
  textAlign: "right",
});

const InnerInput = (props) => {
  const { label, placeholder, value, onChange, error, ...other } = props;
  const id = "upload"; // || React.useId();

  return (
    <>
      <StyledInput
        {...other}
        id={id}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        error={error}
      />
      <StyledLabel htmlFor={id}>{label}</StyledLabel>
    </>
  );
};

export default function Upload(props) {
  const {
    label = "Label",
    placeholder = "Enter text...",
    onInputChange, // New prop for handling input change
    error = false,
    helperText = "",
    loginUI,
    value,
    fileType,
    multiple,
    isView,
    fileObjectEnabled,
    ...rest
  } = props;
  const ref = React.useRef(null);
  const defaultFile = { data: "/images/uploader.svg", isPermenant: true };
  const [inputValue, setInputValue] = React.useState(
    isView
      ? (value?.length && [
          ...value.map((r) => ({ data: r, isPermenant: false })),
        ]) ||
          []
      : (value?.length && [
          ...value.map((r) => ({ data: r, isPermenant: false })),
          defaultFile,
        ]) || [defaultFile]
  );
  const [files, setFiles] = React.useState([]);
  const [checkValidity, setCheckValidity] = React.useState(false);
  const [maxFileSize, setMaxFileSize] = React.useState(10 * 1024);
  const { t } = useTranslation(["common"]);
  const handleChange = (data) => {
    debugger;

    setInputValue(
      (isView && [...inputValue.filter((r) => !r.isPermenant), ...data]) ||
        (multiple && [
          ...inputValue.filter((r) => !r.isPermenant),
          ...data,
          defaultFile,
        ]) || [...data, defaultFile]
    );
    if (onInputChange) {
      onInputChange(
        (multiple &&
          ((!fileObjectEnabled &&
            [...inputValue.filter((r) => !r.isPermenant), ...data]
              ?.map((r) => r.data)
              ?.join(", ")) ||
            [...inputValue.filter((r) => !r.isPermenant), ...data]?.map(
              (r) => r
            ))) ||
          (!fileObjectEnabled && [...data]?.map((r) => r.data)?.join(", ")) ||
          [...data]?.map((r) => r)
      ); // Call the parent's function with the new value
    }
  };
  const isObject = (val) => {
    return val instanceof Object;
  };

  const handleRemoveFile = (evt, key) => {
    evt.preventDefault();
    evt.stopPropagation();
    const updatedPreviews = inputValue.filter((r, index) => index !== key);

    // Revoke URLs for removed files
    // const removedFiles = filePreviews.filter((file) => file.id === id);
    // removedFiles.forEach((file) => URL.revokeObjectURL(file.url));

    setInputValue(updatedPreviews);

    if (onInputChange) {
      onInputChange(
        (!fileObjectEnabled &&
          [...updatedPreviews.filter((r) => !r.isPermenant)]
            ?.map((r) => r.data)
            ?.join(", ")) ||
          [...updatedPreviews.filter((r) => !r.isPermenant)]?.map((r) => r)
      ); // Call the parent's function with the new value
    }
  };

  const renderThumb = (file, key) => {
    let image;
    if (file) {
      image =
        (!isObject(file) && file) ||
        (isArray(file) && (file?.[0]?.data || file?.[0])) ||
        (!isArray(file) && (file?.data || file));
      if (
        image &&
        typeof image == "image" &&
        image?.toLowerCase()?.includes("gisapi")
      ) {
        image = `${image
          .toLowerCase()
          .split("gisapi")[1]
          .replace(/\\/g, "/")
          .replace("//", "/")}`;
      }
    }

    const type =
      image && typeof image == "image" && last(image.split(".")).toLowerCase();
    return (
      <>
        {(file.isPermenant && (
          <Grid
            item
            xs={4}
            style={{
              border: "1px dashed #284587",
              borderRadius: "5px",
              width: "65px",
              height: "65px",
              maxWidth: "65px",
              flexBasis: "65px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              margin: "5px",
              position: "relative",
              flexDirection: "column",
              textWrap: "auto",
              gap: "7px",
            }}
            onClick={(evt) => {
              ref.current.firstChild.click();
            }}
          >
            <img
              src={addPhoto}
              alt=""
              type={"uploader"}
              file={image}
              key={key}
              style={{
                filter:
                  "brightness(0) saturate(100%) invert(22%) sepia(95%) saturate(747%) hue-rotate(193deg) brightness(91%) contrast(93%)",
              }}
            />
            <div style={{ display: "flex", alignItems: "center" }}>
              <p
                // className={styles.addPhoto}
                style={{ fontSize: "7px", fontWeight: "700", color: "#284587" }}
              >
                {label}
                {(label === "صور الملكية/PDF" ||
                  label === "Ownership images/PDF") && (
                  <span className={styles.iconSrtick}>*</span>
                )}
              </p>
            </div>
          </Grid>
        )) || (
          <Grid
            item
            xs={2}
            style={{
              border: "1px solid #A8A8A8",
              borderRadius: "10px",
              width: "65px",
              height: "65px",
              maxWidth: "65px",
              flexBasis: "65px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              margin: "5px",
              position: "relative",
            }}
          >
            {(image &&
              // <img
              //   style={{
              //     width: "60px",
              //     height: "60px",
              //     objectFit: "cover",
              //   }}
              //   src={
              //     (type == "pdf" && pdfPhoto) ||
              //     (type == "dwg" && cadPhoto) ||
              //     window.filesURL + image
              //   }
              //   alt=""
              //   type={type}
              //   file={image}
              //   key={key}
              //   onError={(evt) => (evt.target.src = addPhoto)}
              // />
              checkImage(props, window.filesURL + image, {
                width: "60px",
                height: "60px",
                objectFit: "cover",
              })) || <></>}

            {file && (
              <div
                style={{
                  position: "absolute",
                  top: "-3px",
                  right: "3px",
                }}
                onClick={(evt) => handleRemoveFile(evt, key)}
              >
                <IoMdClose color="#000" />
              </div>
            )}
          </Grid>
        )}
      </>
    );
  };

  const uploadFile = () => {
    ref.current.firstChild.value = "";
    const formData = new window.FormData();
    for (let i = 0; i < files.length; i++) {
      formData.append("file" + i, files[i]);
    }

    //handleChange([...files].map((r) => ""));
    return Axios.post(`${window.ApiUrl}/uploadMultifiles`, formData).then(
      ({ data }) => {
        setFiles([]);
        handleChange(data);
      },
      (error) => {
        setFiles([]);
        handleChange([]);
      }
    );
  };

  const onChangeFile = (event) => {
    setCheckValidity(true);
    setFiles(event.target.files);
  };

  React.useEffect(() => {
    if (!value?.length) {
      setInputValue([...inputValue.filter((file) => file.isPermenant)]);
    } else {
      setInputValue(
        isView
          ? (value?.length && [
              ...value.map((r) => ({ data: r, isPermenant: false })),
            ]) ||
              []
          : (value?.length && [
              ...value.map((r) => ({ data: r, isPermenant: false })),
              defaultFile,
            ]) || [defaultFile]
      );
    }
  }, [value]);

  React.useEffect(() => {
    if (files?.length && checkValidity) {
      let loadingFiles = [];
      var sizeByKiloByte = 2 * 1024 * +maxFileSize;
      Object.values(files).forEach((file) => {
        if (
          (fileType.toLowerCase().split(",").indexOf("image/*") != -1 &&
            ["jpg", "jpeg", "gif", "bmp", "png"].indexOf(
              file.name.split(".").pop().toLowerCase()
            ) != -1) ||
          fileType
            .toLowerCase()
            .split(",")
            .indexOf(`.${file.name.split(".").pop().toLowerCase()}`) != -1
        ) {
          if (file.size < sizeByKiloByte) {
            loadingFiles.push(file);
          } else {
            message.error(t("sidemenu:uploadFilesError"));
            //toast.error(`'${file.name}' ${t("common:fileVeryHuge")}`);
          }
        } else {
          message.error(t("sidemenu:uploadFilesError"));
          //toast.error(`'${file.name}' ${t("common:fileNoSupported")}`);
        }
      });
      if (loadingFiles.length > 0) {
        setCheckValidity(false);
        setFiles(loadingFiles);
      }
    } else if (files?.length && !checkValidity) {
      uploadFile();
    }
  }, [files, checkValidity]);

  return (
    <>
      <Grid container className={styles.PDFContent}>
        {!inputValue.length && <p>{label}</p>}
        <Input
          style={{ display: "none" }}
          slots={{ input: InnerInput }}
          slotProps={{
            input: {
              placeholder,
              onChange: onChangeFile,
              type: "file",
              error,
              accept: fileType,
              multiple: multiple,
              ...rest,
            },
          }}
          sx={{
            "--Input-minHeight": "56px",
            "--Input-radius": "6px",
          }}
          ref={ref}
        />
        {map(inputValue, renderThumb)}
      </Grid>
      {helperText && (
        <div
          style={{
            color: "red",
            fontSize: "12px",
            // textAlign: "center",
            marginTop: `${loginUI ? "0px" : "-9px"}`,
          }}
        >
          {helperText}
        </div>
      )}
    </>
  );
}
