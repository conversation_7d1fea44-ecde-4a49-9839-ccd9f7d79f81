import React, { useEffect, useRef, useState } from "react";
import edit_icon from "../../assets/images/sidemenu/edit.svg";
import interactive_edit from "../../assets/icons/interactive_edit.svg";
import delete_icon from "../../assets/images/sidemenu/delete.svg";
import { useTranslation } from "react-i18next";
import { MdKeyboardArrowDown } from "react-icons/md";
import special_maps_logo from "../../assets/icons/archiveInfo.svg";
import shared_maps_logo from "../../assets/images/interactive-map/shared_maps_logo.svg";
import share from "../../assets/icons/share.svg";
import TrashBin2 from "../../assets/icons/Trash Bin 2.svg";
import axios from "axios";
import { Button, message, Modal, Select, Tooltip } from "antd";
import Extent from "@arcgis/core/geometry/Extent";
import Graphic from "@arcgis/core/Graphic";
import SpatialReference from "@arcgis/core/geometry/SpatialReference";
import Polyline from "@arcgis/core/geometry/Polyline";
import SimpleLineSymbol from "@arcgis/core/symbols/SimpleLineSymbol";
import PictureMarkerSymbol from "@arcgis/core/symbols/PictureMarkerSymbol";
import Point from "@arcgis/core/geometry/Point";
import { Checkbox, FormControlLabel, FormGroup } from "@mui/material";
import { RiArrowDropDownFill } from "react-icons/ri";
import { IoMdClose } from "react-icons/io";
import { showLoading } from "../../helper/common_func";
import arrow_drop_down from "../../assets/icons/arrow_drop_down.svg";
import arrow_drop_up from "../../assets/icons/arrow_drop_up.svg";
import { FaCheck, FaRegCheckCircle } from "react-icons/fa";
export default function InteractiveMapMaps(props) {
  const { t } = useTranslation("common");

  const [showSpecialMaps, setShowSpecialMaps] = useState(true);
  const [showSharedMaps, setShowSharedMaps] = useState(true);
  const [drawingsData, setDrawingsData] = useState([]);
  const [sharedDrawings, setSharedDrawings] = useState([]);
  const [activeDeleteName, setActiveDeleteName] = useState();
  const [deleteModalVisible, setdeleteModalVisible] = useState(false);
  const [drawnGraphicsModal, setDrawnGraphicsModal] = useState(false);
  const [clickedMap, setClickedMap] = useState();
  const [activeAction, setActiveAction] = useState();
  const [open1, setOpen1] = useState(false);
  const [open2, setOpen2] = useState(false);
  useEffect(() => {
    getPrivateDrawings();
  }, []);

  useEffect(() => {
    props.setDrawGraphicsRef(drawMap);
  }, [props.setDrawGraphicsRef]);

  const getPrivateDrawings = async () => {
    await axios
      .get(`${window.ApiUrl}/StudyDrawing/GetAll?pageSize=100`)
      .then((privateResponse) => {
        let privateDrawings = privateResponse.data.results.map(
          (drawing) => drawing
        );
        setDrawingsData([...privateDrawings]);
        getSharedDrawings();
      })
      .catch((error) => {
        message.warning(t("ErrorRequest"));
      });
  };

  const getSharedDrawings = async () => {
    await axios
      .get(`${window.ApiUrl}/StudyDrawing/shared?pageSize=100`)
      .then((sharedResponse) => {
        let sharedMaps = sharedResponse.data.results.map((drawing) => drawing);
        setSharedDrawings([...sharedMaps]);
      })
      .catch((error) => {
        message.warning(t("ErrorRequest"));
      });
  };

  const handleDrawingClick = (drawing) => {
    setClickedMap(() => drawing);
    setActiveAction("draw");
    if (window.__undoStackLength > 0 && !window.__isInteractiveMapSaved) {
      setDrawnGraphicsModal(true);
    } else {
      drawMap(drawing);
    }
  };

  const drawMap = (drawing) => {
    props.map.findLayerById("InteractiveMapGraphicLayer").graphics.removeAll();
    let savedGraphicsData = JSON.parse(drawing.drawing);
    drawSavedDrawings(savedGraphicsData);
  };

  const drawSavedDrawings = (savedGraphicsData) => {
    props.map.view.extent = Extent.fromJSON(savedGraphicsData.mapExtent);
    let graphicsLayersNames = Object.keys(savedGraphicsData).filter(
      (key) => key != "mapExtent"
    );

    showLoading(true);

    let drawingLayer = props.map.findLayerById("InteractiveMapGraphicLayer");

    props.map.view.whenLayerView(drawingLayer).then((layerView) => {
      layerView.watch("updating", (isUpdating) => {
        if (!isUpdating) {
          showLoading(false);
          animateAllGraphicsInLayer(drawingLayer);
        }
      });
    });

    graphicsLayersNames.forEach((graphicsLayer) => {
      if (savedGraphicsData[graphicsLayer].animation.length > 0) {
        savedGraphicsData[graphicsLayer].animation.forEach((animationData) => {
          let paths;
          let symbolName;
          if (animationData.paths.paths) {
            paths = animationData.paths.paths[0];
            symbolName = animationData.symbol_name;
          } else {
            paths = [
              [animationData.paths.x, animationData.paths.y],
              [animationData.paths.x, animationData.paths.y],
            ];
            symbolName = animationData.symbol_name + "_right";
          }
          const fileURL = animationData.fileURL ? animationData.fileURL : null;
          addGraphicsToLayer(graphicsLayer, paths, symbolName, fileURL);
        });
      }
      if (savedGraphicsData[graphicsLayer].sketches.length > 0) {
        savedGraphicsData[graphicsLayer].sketches.forEach((sketch) => {
          let graphic = Graphic.fromJSON(sketch);
          graphic._layerName = graphicsLayer;
          drawingLayer.graphics.add(graphic);
        });
      }
    });
  };

  const addGraphicsToLayer = (layerName, paths, symbolName, fileURL) => {
    const spatialReference = new SpatialReference({ wkid: 102100 });

    const lineGeometry = new Polyline({
      paths: paths.map((point) => [point[0], point[1]]),
      spatialReference: spatialReference,
    });

    const lineSymbol = new SimpleLineSymbol({
      color: [0, 0, 0, 0.3],
      width: 2,
      style: "dash",
    });

    const lineGraphic = new Graphic({
      geometry: lineGeometry,
      symbol: lineSymbol,
    });

    lineGraphic.has_animation = true;
    lineGraphic._layerName = layerName;
    if (fileURL) {
      lineGraphic._fileURL = fileURL;
    }

    props.map
      .findLayerById("InteractiveMapGraphicLayer")
      .graphics.add(lineGraphic);

    const pictureUrl = symbolName.includes("SubAttachments/uploadMultifiles/")
      ? symbolName
      : symbolName != "location_marker" &&
        symbolName != "rocks" &&
        symbolName != "ruler" &&
        symbolName != "word" &&
        symbolName != "excel" &&
        symbolName != "engineer" &&
        symbolName != "pdf" &&
        symbolName != "castle"
      ? null
      : require(`../../assets/images/interactive-map/map-GIFs/${symbolName}.png`);

    const pointSymbol = new PictureMarkerSymbol({
      url: pictureUrl
        ? pictureUrl.default
          ? pictureUrl.default
          : pictureUrl
        : null,
      width: 40,
      height: 40,
    });

    const pointGraphic = new Graphic({
      geometry: new Point({
        x: paths[0][0],
        y: paths[0][1],
        spatialReference: new SpatialReference({ wkid: 102100 }),
      }),
      symbol: pointSymbol,
    });

    pointGraphic._layerName = layerName;
    if (fileURL) {
      pointGraphic._fileURL = fileURL;
    }

    animateGraphicOnPath(pointGraphic, paths, spatialReference, symbolName);
  };

  const animateAllGraphicsInLayer = (drawingLayer) => {
    drawingLayer.graphics.forEach((graphic) => {
      if (graphic._animate) {
        graphic._animate();
      }
    });
  };

  const animateGraphicOnPath = (
    pointGraphic,
    paths,
    spatialReference,
    symbolName
  ) => {
    let drawingLayer = props.map.findLayerById("InteractiveMapGraphicLayer");
    let directions;
    let leftSidePicture;
    let rightSidePicture;
    let pictureUrl = pointGraphic.symbol.url;
    let tempGraphic;

    if (paths.length >= 2) {
      directions = analyzeGeometryDirections(paths);
      tempGraphic = new Graphic({
        id: "temp_graphic",
        geometry: new Point({
          x: paths[0][0],
          y: paths[0][1],
          spatialReference: new SpatialReference({ wkid: 102100 }),
        }),
        visible: false,
      });
    }

    if (pictureUrl == null) {
      directions = analyzeGeometryDirections(paths);
      symbolName = symbolName.replaceAll("_right", "").replaceAll("_left", "");
      if (symbolName != "") {
        rightSidePicture = require(`../../assets/images/interactive-map/map-GIFs/${symbolName}_right.gif`);
        leftSidePicture = require(`../../assets/images/interactive-map/map-GIFs/${symbolName}_left.gif`);
        if (directions.Left && !directions.Right) {
          pointGraphic.symbol.url = rightSidePicture.default;
          tempGraphic.symbol = new PictureMarkerSymbol({
            url: leftSidePicture.default,
            width: 40,
            height: 40,
          });
        } else if (directions.Right && !directions.Left) {
          pointGraphic.symbol.url = leftSidePicture.default;
          tempGraphic.symbol = new PictureMarkerSymbol({
            url: rightSidePicture.default,
            width: 40,
            height: 40,
          });
        } else if (directions.Right && directions.Left) {
          if (directions.Right[0].from == 0) {
            pointGraphic.symbol.url = leftSidePicture.default;
            tempGraphic.symbol = new PictureMarkerSymbol({
              url: rightSidePicture.default,
              width: 40,
              height: 40,
            });
          } else {
            pointGraphic.symbol.url = rightSidePicture.default;
            tempGraphic.symbol = new PictureMarkerSymbol({
              url: leftSidePicture.default,
              width: 40,
              height: 40,
            });
          }
        }
      } else {
        pointGraphic.symbol.url = pointGraphic._fileURL;
        tempGraphic.symbol = new PictureMarkerSymbol({
          url: pointGraphic._fileURL,
          width: 40,
          height: 40,
        });
      }

      drawingLayer.graphics.add(tempGraphic);
    }

    let pointIndex = 0;
    let fraction = 0;

    function animate() {
      const startPoint = paths[pointIndex];
      const endPoint = paths[pointIndex + 1];

      const x = startPoint[0] + fraction * (endPoint[0] - startPoint[0]);
      const y = startPoint[1] + fraction * (endPoint[1] - startPoint[1]);

      pointGraphic.geometry = new Point({
        x: x,
        y: y,
        spatialReference: spatialReference,
      });

      fraction += window.__speedFactor == 0 ? 0 : window.__speedFactor / 100;

      if (fraction >= 1) {
        fraction = 0;
        pointIndex += 1;

        // Determine direction for current segment
        if (pictureUrl == null && directions) {
          let direction = "";
          if (
            directions.Right &&
            directions.Right.some(
              ({ from, to }) => from === pointIndex && to === pointIndex + 1
            )
          ) {
            direction = "left";
          } else if (
            directions.Left &&
            directions.Left.some(
              ({ from, to }) => from === pointIndex && to === pointIndex + 1
            )
          ) {
            direction = "right";
          }

          if (direction) {
            let directionUrl =
              direction == "right" ? rightSidePicture : leftSidePicture;

            pointGraphic.symbol.url = directionUrl.default
              ? directionUrl.default
              : pictureUrl;
          }
        }
      }

      if (pointIndex >= paths.length - 1) {
        pointIndex = 0;
      }
      pointGraphic._animateId = requestAnimationFrame(animate);
    }

    pointGraphic._animate = animate;

    drawingLayer.graphics.add(pointGraphic);
  };

  function analyzeGeometryDirections(geometries) {
    const directions = [];
    const threshold = 0;

    for (let i = 0; i < geometries.length - 1; i++) {
      const currentGeometry = geometries[i];
      const nextGeometry = geometries[i + 1];

      const x1 = currentGeometry[0];
      const y1 = currentGeometry[1];
      const x2 = nextGeometry[0];
      const y2 = nextGeometry[1];

      const deltaX = x2 - x1;
      const deltaY = y2 - y1;

      let direction = "";

      if (deltaX > threshold) {
        direction = "Right"; // East-like movement
      } else if (deltaX < -threshold) {
        direction = "Left"; // West-like movement
      } else if (deltaY !== 0) {
        // For vertical movement, treat as horizontal equivalent
        direction = deltaY > 0 ? "Right" : "Left";
      }

      // Handle cases where movement is near zero (stationary)
      if (!direction) {
        const prevDirection = i > 0 ? directions[i - 1]?.direction : "Right";
        direction = prevDirection;
      }

      directions.push({ from: i, to: i + 1, direction });
    }

    // Adjust initial point direction
    if (directions.length > 0 && directions[0].direction === "") {
      const nextDirection = directions[1].direction;
      directions[0].direction = nextDirection;
    }

    // Group by directions
    const groupedDirections = directions.reduce(
      (acc, { from, to, direction }) => {
        if (!acc[direction]) acc[direction] = [];
        acc[direction].push({ from, to });
        return acc;
      },
      {}
    );

    return groupedDirections;
  }

  //// delete drawing ////

  const deleteDrawing = async (drawingId) => {
    try {
      const delteUrl = `${window.ApiUrl}/StudyDrawing/${drawingId}`;
      await axios.delete(delteUrl);
      return { success: true };
    } catch (error) {
      return { success: false };
    }
  };

  const showDelete = (drawingName) => {
    setActiveDeleteName(drawingName);
    setdeleteModalVisible(true);
  };

  const submitDeleteDrawing = async () => {
    const drawingToDelete = drawingsData.find(
      (drawing) => drawing.name == activeDeleteName
    );
    const result = await deleteDrawing(drawingToDelete.id);
    if (!result.success) {
      message.warning(t("ErrorRequest"));
    } else {
      const otherDrawings = drawingsData.filter(
        (drawing) => drawing.id != drawingToDelete.id
      );
      setDrawingsData(otherDrawings);
      drawingDeletedSuccessfully();
    }
  };

  const drawingDeletedSuccessfully = () => {
    setActiveDeleteName();
    setdeleteModalVisible(false);
    message.warning(t("mapDeletedSuccessfully"));
    props.defaultMapView();
  };

  // share modal

  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [selectedDepartments, setSelectedDepartments] = useState([]);
  const [checked, setChecked] = useState({
    public: false,
    persons: false,
    departments: false,
  });
  const [drawingToShare, setDrawingToShare] = useState();
  const [usersList, setUsersList] = useState([]);
  const [departmentsList, setDepartmentsList] = useState([]);

  useEffect(() => {
    const searchInputs = document.querySelectorAll(
      ".ant-select-selection-search-input"
    );
    if (searchInputs.length > 0) {
      searchInputs.forEach((searchInput) =>
        searchInput.setAttribute("maxLength", 30)
      );
    }
  }, [checked]);

  const updateDrawing = async (drawingObject) => {
    try {
      const putUrl = `${window.ApiUrl}/StudyDrawing/${drawingObject.id}`;
      await axios.put(putUrl, drawingObject).then((response) => {
        let updatedDrawings = drawingsData.map((drawing) =>
          drawing.id != drawingObject.id ? drawing : { ...drawingObject }
        );
        setDrawingsData([...updatedDrawings]);
      });
      return { success: true };
    } catch (error) {
      return { success: false };
    }
  };

  const showShareModal = (drawing) => {
    setIsShareModalOpen(true);
    setDrawingToShare({ ...drawing });
    if (drawing.is_public) {
      handleChangeShareOption({ target: { name: "public", checked: true } });
    }
    if (drawing.shared_users && drawing.shared_departments) {
      handleChangeShareOption({ persons: true, departmens: true });
      setSelectedUsers([
        ...drawing.shared_users.map((user) => {
          return {
            id: user.user_id,
            name: user.user_name,
          };
        }),
      ]);
      setSelectedDepartments([
        ...drawing.shared_departments.map((dept) => {
          return {
            id: dept.department_id,
            name: dept.department_name,
          };
        }),
      ]);
    } else if (drawing.shared_users) {
      handleChangeShareOption({ target: { name: "persons", checked: true } });
      setSelectedUsers([
        ...drawing.shared_users.map((user) => {
          return {
            id: user.user_id,
            name: user.user_name,
          };
        }),
      ]);
    } else if (drawing.shared_departments) {
      handleChangeShareOption({
        target: { name: "departments", checked: true },
      });
      setSelectedDepartments([
        ...drawing.shared_departments.map((dept) => {
          return {
            id: dept.department_id,
            name: dept.department_name,
          };
        }),
      ]);
    }
  };

  const handleShareModalOk = () => {
    setIsShareModalOpen(false);
  };

  const handleShareModalCancel = () => {
    setIsShareModalOpen(false);

    setSelectedUsers([]);
    setSelectedDepartments([]);

    setChecked({
      public: false,
      persons: false,
      departments: false,
    });
  };

  const handleChangeShareOption = (event) => {
    if (event.persons && event.departmens) {
      setChecked({
        public: false,
        persons: true,
        departments: true,
      });
    } else if (event.target.name == "public" && event.target.checked) {
      setChecked({
        public: true,
        persons: false,
        departments: false,
      });
    } else {
      setChecked({ ...checked, [event.target.name]: event.target.checked });
    }
  };

  const searchEntity = async (name, entityType) => {
    if (name.trim().length >= 3 && name) {
      switch (entityType) {
        case "select-user":
          setOpenUserDropDown(true);
          await axios
            .get(
              `${window.ApiUrl}/user?pageSize=100&filter_key=name&q=${name}&contain=1`
            )
            .then((response) => {
              setUsersList([...response.data.results]);
            })
            .catch((error) => message.warning(t("ErrorRequest")));
          break;
        case "select-department":
          setOpenDeptDropDown(true);
          await axios
            .get(
              `${window.ApiUrl}/department/GetAll?pageSize=100&filter_key=name&q=${name}&contain=1`
            )
            .then((response) => {
              setDepartmentsList([...response.data.results]);
            })
            .catch((error) => message.warning(t("ErrorRequest")));
          break;

        default:
          break;
      }
    } else {
      setUsersList([]);
      setDepartmentsList([]);
      entityType == "select-user"
        ? setOpenUserDropDown(false)
        : setOpenDeptDropDown(false);
    }
  };

  const handleSelectedDepartment = (id) => {
    if (id) {
      let selectedDepartment = departmentsList.find((dept) => dept.id == id);

      let isExist = selectedDepartments.find(
        (dept) => dept.id == selectedDepartment.id
      );
      if (isExist) {
        message.warning(t("alreadySelected"));
      } else {
        setSelectedDepartments((prevState) => {
          return [
            ...prevState,
            { id: selectedDepartment.id, name: selectedDepartment.name },
          ];
        });
      }
    }
  };

  const handleDepartmentDeletion = (department) => {
    let depts = selectedDepartments.filter((dept) => dept.id != department.id);
    setSelectedDepartments([...depts]);
  };

  const handleSelectedUser = (id) => {
    if (id) {
      let selectedUser = usersList.find((user) => user.id == id);

      let isExist = selectedUsers.find((user) => user.id == selectedUser.id);
      if (isExist) {
        message.warning(t("alreadySelected"));
      } else {
        setSelectedUsers((prevState) => {
          return [
            ...prevState,
            { id: selectedUser.id, name: selectedUser.name },
          ];
        });
      }
    }
  };

  const handleUserDeletion = (user) => {
    let users = selectedUsers.filter((usr) => usr.id != user.id);
    setSelectedUsers([...users]);
  };

  const handleShareMap = () => {
    let drawing = { ...drawingToShare };
    if (!checked.public && !checked.persons && !checked.departments) {
      message.warning(t("NoShareChoice"));
    } else if (checked.public && drawing) {
      drawing.is_public = true;
      drawing.shared_users = null;
      drawing.shared_departments = null;
    } else {
      drawing.is_public = false;
      if (selectedUsers.length > 0) {
        let usersToShare = selectedUsers.map((user) => {
          return {
            user_id: user.id,
            user_name: user.name,
            drawing_id: drawing.id,
          };
        });
        drawing.shared_users = [...usersToShare];
      } else {
        drawing.shared_users = null;
      }
      if (selectedDepartments.length > 0) {
        let deptsToShare = selectedDepartments.map((dept) => {
          return {
            department_id: dept.id,
            department_name: dept.name,
            drawing_id: drawing.id,
          };
        });

        drawing.shared_departments = [...deptsToShare];
      } else {
        drawing.shared_departments = null;
      }
    }

    let success = updateDrawing(drawing);
    if (!success) {
      message.warning(t("ErrorRequest"));
    } else {
      message.warning(t("sharedSuccessfully"));
      handleShareModalCancel();
    }
  };

  const [openUserDropDown, setOpenUserDropDown] = useState(false);
  const [opendeptDropDown, setOpenDeptDropDown] = useState(false);

  //// edit drawing ////

  const handleEdit = (drawing) => {
    setClickedMap(() => drawing);
    setActiveAction("edit");
    if (window.__undoStackLength > 0 && !window.__isInteractiveMapSaved) {
      setDrawnGraphicsModal(true);
    } else {
      editDrawing(drawing);
    }
  };

  const editDrawing = (drawing) => {
    props.setEditDrawingData(drawing);
    props.setSelectedTab("tools");
  };

  return (
    <>
      <>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "10px",
            height: "calc(100vh - 170px)",
            overflow: "auto",
            marginTop: "10px",
          }}
        >
          {/* start special maps */}
          <div className="box">
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                color: "#fff",
              }}
            >
              <div
                style={{ display: "flex", gap: "5px", alignItems: "center" }}
              >
                <img src={special_maps_logo} alt="maps logo" />
                <span className="interactive_map_span">
                  {t("special", { ns: "sidemenu" })}
                </span>
              </div>

              <MdKeyboardArrowDown
                size={20}
                style={{
                  cursor: "pointer",
                  transform: `rotate(${!showSpecialMaps ? "180deg" : 0})`,
                }}
                onClick={() => setShowSpecialMaps(!showSpecialMaps)}
                className="MdKeyboardArrowDown_icon"
              />
            </div>

            {showSpecialMaps &&
              drawingsData.length > 0 &&
              drawingsData.map((drawing, indx) => {
                return (
                  <div
                    key={indx}
                    className="generalSearchCard"
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      padding: "16px",
                      borderRadius: "16px",
                      background: "#FFFFFF99",
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDrawingClick(drawing);
                    }}
                  >
                    <div>
                      <div className="interactive_drawing_label">
                        {" "}
                        {drawing.name}{" "}
                      </div>
                      <div
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          gap: "5px",
                        }}
                      >
                        {drawing.zone_name && (
                          <label className="interactive_drawing_label_2">
                            {`${t("zone", { ns: "common" })} :  ${
                              drawing.zone_name
                            }`}
                          </label>
                        )}
                        {drawing.region_name && (
                          <label className="interactive_drawing_label_2">
                            {`${t("region", { ns: "common" })} :  ${
                              drawing.region_name
                            }`}
                          </label>
                        )}
                      </div>
                    </div>
                    <div style={{ display: "flex", gap: "10px" }}>
                      <Tooltip title={t("share")}>
                        <img
                          src={share}
                          alt="share icon"
                          onClick={(e) => {
                            e.stopPropagation();
                            showShareModal(drawing);
                          }}
                        />
                      </Tooltip>
                      <Tooltip title={t("edit")}>
                        <img
                          src={interactive_edit}
                          alt="edit icon"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEdit(drawing);
                          }}
                        />
                      </Tooltip>
                      <Tooltip title={t("delete")}>
                        <img
                          src={TrashBin2}
                          alt="delete icon"
                          onClick={(e) => {
                            e.stopPropagation();
                            showDelete(drawing.name);
                          }}
                        />
                      </Tooltip>
                    </div>
                  </div>
                );
              })}
          </div>
          {/* end special maps */}

          {/* start shared maps */}
          <div className="box">
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                color: "#fff",
              }}
            >
              <div
                style={{ display: "flex", gap: "5px", alignItems: "center" }}
              >
                <img src={special_maps_logo} alt="maps logo" />
                <span className="interactive_map_span">
                  {t("shared", { ns: "sidemenu" })}
                </span>
              </div>

              <MdKeyboardArrowDown
                size={20}
                style={{
                  cursor: "pointer",
                  transform: `rotate(${!showSharedMaps ? "180deg" : 0})`,
                }}
                onClick={() => setShowSharedMaps(!showSharedMaps)}
                className="MdKeyboardArrowDown_icon"
              />
            </div>

            {showSharedMaps &&
              sharedDrawings.length > 0 &&
              sharedDrawings.map((drawing, indx) => {
                return (
                  <div
                    onClick={() => handleDrawingClick(drawing)}
                    className="generalSearchCard"
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      padding: "16px",
                      borderRadius: "16px",
                      background: "#FFFFFF99",
                    }}
                  >
                    <span className="interactive_drawing_label">
                      {" "}
                      {drawing.name}{" "}
                    </span>
                    <span className="interactive_drawing_label_2">
                      {drawing.creator_name}
                    </span>
                  </div>
                );
              })}
          </div>
          {/* end shared maps */}
        </div>
      </>

      <>
        <Modal
          title={<div className="custom-modal-title">حذف</div>}
          centered
          closable={false}
          visible={deleteModalVisible}
          className="shareModal sharmodalBookmark"
          //onOk={() => afterEditModal()}
          onCancel={() => setdeleteModalVisible(false)}
          // okText={t("yes")}
          // cancelText={t("no")}
          footer={
            <div className="footer_modal">
              <Button
                key="submit"
                type="primary"
                onClick={() => submitDeleteDrawing()}
                className="footer_buttonEdit_modal"
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "10px",
                }}
              >
                <FaCheck />
                {t("yes")}
              </Button>
              <Button
                key="cancel"
                onClick={() => setdeleteModalVisible(false)}
                className="footer_buttonCancel_modal"
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "10px",
                }}
              >
                <IoMdClose color="red" />
                {t("no")}
              </Button>
            </div>
          }
        >
          <div
            style={{
              textAlign: "center",
              fontSize: "16px",
              fontWeight: "bold",
              marginTop: "24px",
            }}
          >
            {t("deleteDrawingConfirmation") + activeDeleteName}
          </div>
          {/* <div
            style={{
              display: "flex",
              gap: "10px",
              marginTop: "10px",
              justifyContent: "end",
            }}
          >
            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => {
                submitDeleteDrawing();
              }}
            >
              {t("yes")}
            </Button>

            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => setdeleteModalVisible(false)}
            >
              {t("no")}
            </Button>
          </div> */}
        </Modal>
      </>

      {/* start share modal */}
      <div className="shared_container">
        <Modal
          className="shareModal"
          title={<div className="custom-modal-title">{t("share")}</div>}
          visible={isShareModalOpen}
          onOk={handleShareModalOk}
          onCancel={handleShareModalCancel}
          footer={null} // <-- Hide buttons
        >
          <FormGroup style={{ textAlign: "start" }}>
            <div className="shardContent">
              {/* <FormControlLabel
                control={
                  <Checkbox
                    checked={checked.public}
                    onChange={handleChangeShareOption}
                    name="public"
                    sx={{
                      color: "#fff",
                      "&.Mui-checked": {
                        color: "#fff",
                      },
                    }}
                  />
                }
                label={t("public")}
              /> */}
              <div style={{ display: "flex", alignItems: "center" }}>
                <input
                  type="checkbox"
                  // style={{ marginTop: "-10px" }}
                  checked={checked.public}
                  onChange={handleChangeShareOption}
                  name="public"
                  className="toc-gallery-content_checkbox"
                />
                <label className="checkBox_print">{t("public")}</label>
              </div>
              <div></div>
            </div>
            <div className="shardContent">
              {/* <FormControlLabel
                control={
                  <Checkbox
                    checked={checked.persons}
                    disabled={checked.public ? true : false}
                    onChange={handleChangeShareOption}
                    name="persons"
                    sx={{
                      color: "#fff",
                      "&.Mui-checked": {
                        color: "#fff",
                      },
                    }}
                  />
                }
                label={t("persons")}
              /> */}
              <div style={{ display: "flex", alignItems: "center" }}>
                <input
                  type="checkbox"
                  // style={{ marginTop: "-10px" }}
                  checked={checked.persons}
                  disabled={checked.public ? true : false}
                  onChange={handleChangeShareOption}
                  name="persons"
                  className="toc-gallery-content_checkbox"
                />
                <label className="checkBox_print">{t("persons")}</label>
              </div>
              {checked.persons && (
                <div style={{ marginTop: "10px" }}>
                  {/* <div style={{ display: "grid" }}>
                    <label
                      className="selectLabelStyle"
                      style={{ marginBlock: "0" }}
                    >
                      {t("search_select")}
                    </label>
                    <Select
                      id="select-user"
                      virtual={false}
                      suffixIcon={null}
                      onDropdownVisibleChange={(flag) => setOpen1(flag)}
                      // suffixIcon={
                      //   open1 ? (
                      //     <img src={arrow_drop_up} alt="" />
                      //   ) : (
                      //     <img src={arrow_drop_down} alt="" />
                      //   )
                      // }
                      open={openUserDropDown}
                      filterOption={false}
                      showSearch
                      allowClear
                      className="dont-show"
                      placeholder={t("name")}
                      onSearch={(value) => {
                        searchEntity(value, "select-user");
                      }}
                      value={null}
                      onSelect={() => setOpen1(true)}
                      onChange={(value) => {
                        handleSelectedUser(value);
                        setUsersList([]);
                        setOpenUserDropDown(false);
                        setOpen1(false);
                      }}
                    >
                      {usersList.length > 0 &&
                        usersList.map((user) => {
                          return (
                            <Select.Option key={user.id} value={user.id}>
                              {user.name}
                            </Select.Option>
                          );
                        })}
                    </Select>
                  </div> */}
                  <div className="select-cust select-cust_mapArea">
                    <label className="selectLabelStyle">
                      {" "}
                      {t("search_select")}
                    </label>
                    <Select
                      id="select-user"
                      virtual={false}
                      suffixIcon={<RiArrowDropDownFill size={30} />}
                      className="searchInput englishFont compareLayers-select"
                      showSearch
                      placeholder={t("name")}
                      onSearch={(value) => {
                        searchEntity(value, "select-user");
                      }}
                      value={null}
                      onChange={(value) => {
                        handleSelectedUser(value);
                        setUsersList([]);
                        setOpenUserDropDown(false);
                      }}
                      getPopupContainer={(trigger) => trigger.parentNode}
                      optionFilterProp="v"
                      filterOption={(input, option) =>
                        option.v?.indexOf(input) != -1
                      }
                    >
                      {/* {regionsData.map((region) => {
                        return (
                          <Select.Option
                            value={region.name}
                            id={region.id}
                            key={region.id}
                          >
                            {region.name}
                          </Select.Option>
                        );
                      })} */}
                      {usersList.length > 0 &&
                        usersList.map((user) => {
                          return (
                            <Select.Option
                              key={user.id}
                              value={user.id}
                              id={user.id}
                            >
                              {user.name}
                            </Select.Option>
                          );
                        })}
                    </Select>
                  </div>

                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      gap: "10px",
                      marginTop: "10px",
                      flexWrap: "wrap",
                    }}
                  >
                    {selectedUsers.length > 0 &&
                      selectedUsers.map((user) => (
                        <div
                          style={{
                            backgroundColor: "#284587",
                            color: "#fff",
                            borderRadius: "20px",
                            display: "flex",
                            alignItems: "center",
                            gap: "10px",
                          }}
                          className="shared_selectedUsers"
                        >
                          {user.name}
                          <IoMdClose
                            className="shared_selectedUsers_deleteIcon"
                            onClick={() => handleUserDeletion(user)}
                          />
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
            <div className="shardContent">
              {/* <FormControlLabel
                control={
                  <Checkbox
                    checked={checked.departments}
                    disabled={checked.public ? true : false}
                    onChange={handleChangeShareOption}
                    name="departments"
                    sx={{
                      color: "#fff",
                      "&.Mui-checked": {
                        color: "#fff",
                      },
                    }}
                  />
                }
                label={t("departments")}
              /> */}
              <div style={{ display: "flex", alignItems: "center" }}>
                <input
                  type="checkbox"
                  // style={{ marginTop: "-10px" }}
                  checked={checked.departments}
                  disabled={checked.public ? true : false}
                  onChange={handleChangeShareOption}
                  name="departments"
                  className="toc-gallery-content_checkbox"
                />
                <label className="checkBox_print">{t("departments")}</label>
              </div>
              {checked.departments && (
                <div style={{ marginTop: "10px" }}>
                  {/* <div style={{ display: "grid" }}>
                    <label
                      className="selectLabelStyle"
                      style={{ marginBlock: "0" }}
                    >
                      {t("search_select")}
                    </label>
                    <Select
                      id="select-department"
                      virtual={false}
                      suffixIcon={null}
                      onDropdownVisibleChange={(flag) => setOpen2(flag)}
                      // suffixIcon={
                      //   open2 ? (
                      //     <img src={arrow_drop_up} alt="" />
                      //   ) : (
                      //     <img src={arrow_drop_down} alt="" />
                      //   )
                      // }
                      open={opendeptDropDown}
                      showSearch
                      filterOption={false}
                      allowClear
                      className="dont-show"
                      placeholder={t("department")}
                      onSearch={(value) => {
                        searchEntity(value, "select-department");
                      }}
                      value={null}
                      onSelect={() => setOpen2(true)}
                      onChange={(value) => {
                        setOpen2(false);
                        handleSelectedDepartment(value);
                        setDepartmentsList([]);
                        setOpenDeptDropDown(false);
                      }}
                    >
                      {departmentsList.length > 0 &&
                        departmentsList.map((department) => {
                          return (
                            <Select.Option
                              key={department.id}
                              value={department.id}
                            >
                              {department.name}
                            </Select.Option>
                          );
                        })}
                    </Select>
                  </div> */}
                  <div className="select-cust select-cust_mapArea">
                    <label className="selectLabelStyle">
                      {" "}
                      {t("search_select")}
                    </label>
                    <Select
                      id="select-user"
                      virtual={false}
                      suffixIcon={<RiArrowDropDownFill size={30} />}
                      className="searchInput englishFont compareLayers-select"
                      showSearch
                      placeholder={t("department")}
                      onSearch={(value) => {
                        searchEntity(value, "select-department");
                      }}
                      value={null}
                      onChange={(value) => {
                        handleSelectedDepartment(value);
                        setDepartmentsList([]);
                        setOpenDeptDropDown(false);
                      }}
                      getPopupContainer={(trigger) => trigger.parentNode}
                      optionFilterProp="v"
                      filterOption={(input, option) =>
                        option.v?.indexOf(input) != -1
                      }
                    >
                      {/* {regionsData.map((region) => {
                        return (
                          <Select.Option
                            value={region.name}
                            id={region.id}
                            key={region.id}
                          >
                            {region.name}
                          </Select.Option>
                        );
                      })} */}
                      {departmentsList.length > 0 &&
                        departmentsList.map((user) => {
                          return (
                            <Select.Option
                              key={user.id}
                              value={user.id}
                              id={user.id}
                            >
                              {user.name}
                            </Select.Option>
                          );
                        })}
                    </Select>
                  </div>
                  {/* {selectedDepartments.length > 0 &&
                    selectedDepartments.map((department) => (
                      <div
                        style={{
                          display: "flex",
                          flexDirection: "row",
                          gap: "10px",
                          marginTop: "10px",
                        }}
                      >
                        <div
                          style={{
                            backgroundColor: "#fff",
                            padding: "5px 10px",
                            margin: "0 10px",
                            color: "#b45333",
                            borderRadius: "20px",
                            display: "flex",
                            alignItems: "center",
                            gap: "10px",
                          }}
                        >
                          {department.name}
                          <IoMdClose
                            color="#000"
                            style={{ cursor: "pointer" }}
                            onClick={() => handleDepartmentDeletion(department)}
                          />
                        </div>
                      </div>
                    ))} */}
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      gap: "10px",
                      marginTop: "10px",
                      flexWrap: "wrap",
                    }}
                  >
                    {selectedDepartments.length > 0 &&
                      selectedDepartments.map((department) => (
                        <div
                          style={{
                            backgroundColor: "#284587",
                            color: "#fff",
                            borderRadius: "20px",
                            display: "flex",
                            alignItems: "center",
                            gap: "10px",
                          }}
                          className="shared_selectedUsers"
                        >
                          {department.name}
                          <IoMdClose
                            className="shared_selectedUsers_deleteIcon"
                            onClick={() => handleUserDeletion(department)}
                          />
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          </FormGroup>

          <button
            className="SearchBtn"
            size="large"
            block
            onClick={handleShareMap}
          >
            {t("share")}
          </button>
        </Modal>
      </div>
      {/* end share modal */}

      <>
        <Modal
          title={t("drawnGraphicsWarning", { ns: "common" })}
          centered
          visible={drawnGraphicsModal}
          onCancel={() => {
            setDrawnGraphicsModal(false);
          }}
          okText={t("confirmSave")}
          cancelText={t("cancel")}
        >
          <div
            style={{
              display: "flex",
              gap: "10px",
              marginTop: "10px",
              justifyContent: "end",
            }}
          >
            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => {
                window.__isInteractiveMapSaved =
                  !window.__isInteractiveMapSaved;
                setDrawnGraphicsModal(false);
                activeAction == "draw"
                  ? drawMap(clickedMap)
                  : editDrawing(clickedMap);
              }}
            >
              {t("confirm", { ns: "common" })}
            </Button>

            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => {
                setDrawnGraphicsModal(false);
              }}
            >
              {t("cancel", { ns: "common" })}
            </Button>
          </div>
        </Modal>
      </>
    </>
  );
}
