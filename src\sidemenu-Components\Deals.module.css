.optionsList {
  background-color: white;
  z-index: 9999;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  padding-top: 0;
  display: flex;
  flex-direction: column;
  gap: 2px;
  border-radius: 28px 28px 0px 0px;
  animation: slideUp 0.5s ease-out forwards;
  /* height: 330px; */
}

.SearchLandTitle,
.SearchStreetTitle,
.DecimalCoordTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.SearchLandTitle button,
.SearchStreetTitle button,
.DecimalCoordTitle button {
  padding: 7px 20px 7px 20px;
  border-radius: 16px;
  background: #338c9a;
  border: none;
  outline: none;
  color: #fff;
}

.running {
  background: #67d4ea26;
  padding: 7px 0px;
  display: block;
  width: 112px;
  border-radius: 32px;
  text-align: center;
  color: #67d4ea;
  font-size: 11px;
  font-weight: 700;
}

.attributeContainer {
  border: 1px solid #efefef;
  padding: 0px 0px 0px;
  border-radius: 16px;
  background-color: #FFFFFF99;
}

.attributeContent {
  border-bottom: 1px solid #efefef;
}

.attributeContent:last-of-type {
  margin-bottom: 0px !important;
  border-bottom: none;
}

.attributeKey {
  font-size: 16px;
  font-weight: 400;
  /* text-align: right; */
  color: #284587;
}

.attributeValue {
  font-size: 16px;
  font-weight: 400;
  /* text-align: right; */
  color: #284587;
  text-wrap: auto;
}

.title {
  font-size: 16px !important;
  font-weight: 700 !important;
  color: #2b2b2b !important;
  /* Customize as needed */
  font-family: "Droid Arabic Kufi" !important;
  /* text-align: right; */
  margin-bottom: 0px !important;
  display: flex;
  align-items: center;
}

.title img {
  /* margin: 0px 5px; */
  cursor: pointer;
}

.title span {
  padding: 0px 7px;
}

.borderCotainer {
  display: flex;
  cursor: pointer;
  z-index: 2;
}

.border {
  width: 32px;
  height: 4px;
  border-radius: 100px;
  display: block;
  background-color: #a8a8a8;
  margin: auto;
  margin-top: 15px;
  margin-bottom: 10px;
}

.infoHeading {
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* margin-top: 15px; */
  /* margin-bottom: 15px; */
  /* margin-bottom: 10px; */
  margin-top: -15px;
  cursor: pointer;
}

.infoHeading h1 {
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
  color: #2b2b2b;
  margin: 0;
  /* margin-right: 8px; */
  padding: 0px 5px;
}

.infoHeading img {
  /* transform: rotate(180deg); */
  position: relative;
  top: 1px;
  margin: 0px 8px;
  cursor: pointer;
}

.QueryDataContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.QueryData {
  display: flex;
  align-items: center;
  width: 100%;
  height: 56px;
}

.selectedOption {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  width: 100%;
  /* Adjust as needed */
  padding: 8px 12px;
  cursor: pointer;
  border: 1px solid #a8a8a8;
  border-radius: 16px;
  transition: background-color 0.3s;
  font-size: 12px;
  padding: 16px;
  font-weight: 700;
  color: #a8a8a8;
  height: 56px;
}

.arrow {
  margin-right: 8px;
  font-size: 12px;
  transition: transform 0.3s ease;
  color: #a8a8a8;
}

.rotate {
  transform: rotate(180deg);
}

.card {
  border-radius: 16px;
  background-color: #ffffff;
  box-shadow: none;
  border: 1px solid #efefef;
  padding: 10px 12px;
  /* margin-top: 10px; */
}

.favoriteCardContent {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 10px 8px;
}

.favoriteInfo {
  display: flex;
  flex-direction: column;
}

.favoriteTitle,
.favoriteAddress {
  padding: 0;
  margin: 0;
  font-family: Droid Arabic Kufi !important;
  /* text-align: right; */
}

.favoriteTitle {
  font-size: 14px;
  font-weight: 700;
  line-height: 20px;
  color: #2b2b2b;
}

.favoriteAddress {
  font-size: 11px;
  font-weight: 700;
  color: #a8a8a8;
  margin-top: 5px;
}

.icon,
.icon_progress,
.icon_done {
  width: 112px;
  background-color: #1fbc3826;
  padding: 4px;
  border-radius: 32px;
  text-align: center;
}

.icon_progress {
  background-color: #67d4ea26;
}

.icon_progress .iconSpan {
  color: #67d4ea;
}

.icon_done {
  background-color: #ff3b3026;
}

.icon_done .iconSpan {
  color: #ff3b30;
}

.iconSpan {
  font-size: 11px;
  font-weight: 700;
  line-height: 12px;
  color: #1fbc38;
  letter-spacing: 0.4px;
}

.optionsDetails {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.optionsDetails span:first-of-type {
  font-size: 12px;
  font-weight: 700;
  color: #a8a8a8;
}

.optionsDetails span:last-of-type {
  font-size: 16px;
  font-weight: 400;
  text-align: right;
  color: #2b2b2b;
}

@keyframes slideUp {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }

  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.checkIcon {
  color: red !important;
  position: relative;
  top: 5px;
}

.checkIcon_PDF {
  color: red !important;
  margin-right: 2px;
}

.checkIcon_Land {
  color: red !important;
}

.PDFContent {
  padding: 15px 0px;
  border-radius: 16px;
  border: 1px solid #a8a8a8;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.PDFContent p {
  margin: 0;
  padding: 0;
  font-size: 12px;
  font-weight: 700;
  text-align: center;
  color: #a8a8a8;
}

.arrow {
  display: block;
  text-align: center;
}

.arrow {
  cursor: pointer;
  transition: transform 0.3s ease;
}

.arrow.rotate {
  transform: rotate(180deg);
}

.favoriteAddress {
  opacity: 0;
  transform: translateY(-20px);
  /* Optional: for sliding effect */
  animation: fadeIn 0.3s ease-in-out forwards;
  /* Add animation */
  display: flex;
  align-items: center;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(-20px);
    /* Start position */
  }

  100% {
    opacity: 1;
    transform: translateY(0);
    /* End position */
  }
}

.tooltipTarget {
  position: relative;
}

.tooltip {
  visibility: hidden;
  background-color: #ffe2b9;
  color: #fff;
  text-align: center;
  border-radius: 4px;
  padding: 5px 10px;
  position: absolute;
  bottom: 120%;
  /* Position above the image */
  left: 10%;
  transform: translateX(-50%);
  white-space: nowrap;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 13px;
  /* Adjust as needed */
  color: #ff9500;
  font-weight: 500;
}

.tooltipTarget:hover + .tooltip {
  visibility: visible;
  opacity: 1;
}

.fileInput {
  opacity: 0;
  position: absolute;
  z-index: -1;
}

.PDFContent label {
  cursor: pointer;
  margin: 0;
  width: 100%;
  padding: 25px;
  padding-bottom: 20px;
  font-size: 12px;
  font-weight: 700;
  text-align: center;
  color: #a8a8a8;
}

.PDFContent label:hover {
  text-decoration: none;
}

.filePreviewContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10px;
}

.filePreview {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100px;
}

.previewImage {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.pdfPreview {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.previewIcon {
  width: 60px;
  height: 60px;
}

.pdfPreview p {
  margin-top: 5px;
  font-size: 12px;
  color: #333;
}

.removeButton {
  margin-top: 5px;
  background-color: #ff4d4d;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  margin-bottom: 10px;
}

.removeButton:hover {
  background-color: #ff3333;
}

.checkIconContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px 0px;
}

/* Base styles for the content */
.attributeContent {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease;
  display: none;
}

/* When content is open */
.contentOpen {
  display: block;
  max-height: 500px; /* Set this to the approximate height of your content */
  opacity: 1;
}

/* When content is closed */
.contentClosed {
  max-height: 0;
  opacity: 0;
  display: none;
}

.dealsContent {
  overflow-x: hidden !important;
}

.show {
  overflow-y: hidden !important;
}

.hide {
  overflow-y: visible !important;
}

.imgDealsLoadings {
  height: 100% !important;
}
