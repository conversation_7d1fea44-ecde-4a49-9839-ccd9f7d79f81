import React, { useEffect, useState } from "react";
import { Form, Input, Row, Col, Button, message, Modal, Tooltip } from "antd";
import { Container } from "react-bootstrap";
import { isEmpty } from "lodash";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPlus,
  faEdit,
  faStar,
  faTrash,
} from "@fortawesome/free-solid-svg-icons";
import Upload from "../components/Upload/Upload";
import axios from "axios";
import { UploadOutlined } from "@ant-design/icons";
import Extent from "@arcgis/core/geometry/Extent";
import { useTranslation } from "react-i18next";
import { convertToArabic, getFileNameFromUrl } from "../helper/common_func";
import ImageGallery from "react-image-gallery"; // Import react-image-gallery
import "react-image-gallery/styles/css/image-gallery.css"; // Import styles

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FaRegEye } from "react-icons/fa";

import { FaBookmark } from "react-icons/fa6";
import { FiEdit } from "react-icons/fi";
import { MdDelete } from "react-icons/md";
import bookmark_icon from "../assets/icons/Bookmark.svg";
import edit_icon from "../assets/icons/Library.svg";
// import Property34 from "../assets/icons/Property34.svg";
import interactive_edit from "../assets/icons/interactive_edit.svg";
import delete_icon from "../assets/icons/Trash Bin 2.svg";
import PenImg from "../assets/icons/Pen.svg";
import closeModal from "../assets/icons/closeModal.svg";
import { Tabs } from "antd";
import { IoMdClose } from "react-icons/io";
const { TabPane } = Tabs;

export default function BookMark(props) {
  const { t } = useTranslation("common");

  const [formValues, setFormValues] = useState({
    bookmark: "",
    attachment: "",
  });
  const [bookmarks, setBookmarks] = useState([]);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [activeBookMark, setActiveBookMark] = useState(-1);
  const User = localStorage.user ? JSON.parse(localStorage.user) : null;
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [selectedAttachments, setSelectedAttachments] = useState([]);
  //
  const handleChangeInput = (e) => {
    if (e?.target?.name) {
      setFormValues({ ...formValues, [e.target.name]: e.target.value });
    }
  };

  useEffect(() => {
    window.DisableActiveTool();
    if (User) {
      axios
        .get(window.ApiUrl + "/Bookmark/GetAll?filter_key=user_id&q=" + User.id)
        .then(({ data }) => {
          setBookmarks(data.results || []);
        });
    } else {
      setBookmarks(
        (localStorage.bookmarks &&
          !isEmpty(JSON.parse(localStorage.bookmarks)) &&
          JSON.parse(localStorage.bookmarks)) ||
          []
      );
    }
  }, []);

  const zoomToBookmark = (bookmark, index) => {
    setActiveBookMark(index);

    if (typeof bookmark.extent == "string") {
      bookmark.extent = JSON.parse(bookmark.extent);
    }

    props.map.view.goTo(
      new Extent({
        xmin: bookmark.extent.xmin,
        ymin: bookmark.extent.ymin,
        xmax: bookmark.extent.xmax,
        ymax: bookmark.extent.ymax,
        spatialReference: bookmark.extent.spatialReference,
      })
    );
  };

  const addBookMark = (e) => {
    let temp = [...bookmarks];

    if (User) {
      axios
        .post(window.ApiUrl + "/api/Bookmark", {
          title: formValues.bookmark,
          extent: JSON.stringify(props.map.view.extent),
          attachment: formValues.attachment,
          user_id: User.id,
        })
        .then(
          ({ data }) => {
            temp.push(data);
            setBookmarks(temp);
            setFormValues({});
            message.success(t("saveLocationToBookMark"));
          },
          () => {
            message.error(t("validation.errorMessage"));
          }
        );
    } else {
      temp.push({
        title: formValues.bookmark,
        extent: JSON.stringify(props.map.view.extent),
        attachment: formValues.attachment,
      });
      localStorage.bookmarks = JSON.stringify(temp);
      setBookmarks(temp);
      setFormValues({});
      message.success(t("saveLocationToBookMark"));
    }
  };

  const removeBookMark = (bookmark, index) => {
    if (User) {
      axios.delete(window.ApiUrl + "/Bookmark/" + bookmark.id).then(
        () => {
          message.success(t("removed"));
          axios
            .get(
              window.ApiUrl + "/Bookmark/GetAll?filter_key=user_id&q=" + User.id
            )
            .then(({ data }) => {
              setBookmarks(data.results || []);
            });
        },
        () => {
          message.error(t("errorMessageFavorites"));
        }
      );
    } else {
      let arr = [...bookmarks];
      arr.splice(index - 1, 1);
      localStorage.bookmarks = JSON.stringify(arr);
      setBookmarks(arr);
      message.success(t("removed"));
    }

    setDeleteModalVisible(false);
  };

  const showEdit = (bookmark, index) => {
    zoomToBookmark(bookmark, index);
    setFormValues({
      ...formValues,
      editName: bookmark.title,
      attachment: bookmark.attachment,
    });
    setEditModalVisible(true);
  };

  const afterEditModal = () => {
    let updatedBookmarks = [...bookmarks];
    updatedBookmarks[activeBookMark].title = formValues.editName;
    updatedBookmarks[activeBookMark].attachment = formValues.attachment;
    updatedBookmarks[activeBookMark].extent = JSON.stringify(
      updatedBookmarks[activeBookMark].extent
    );

    if (User) {
      axios
        .put(
          window.ApiUrl +
            "/api/Bookmark/" +
            updatedBookmarks[activeBookMark].id,
          {
            ...updatedBookmarks[activeBookMark],
          }
        )
        .then(
          () => {
            setFormValues({});
            setEditModalVisible(false);
            message.success(t("edited"));
          },
          () => {
            message.error(t("validation.errorMessage"));
          }
        );
    } else {
      let temp = [...bookmarks];
      temp[activeBookMark].title = formValues.editName;
      temp[activeBookMark].attachment = formValues.attachment;

      localStorage.bookmarks = JSON.stringify(temp);
      setFormValues({});
      setEditModalVisible(false);
      message.success(t("edited"));
    }
  };

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setFormValues({
        ...formValues,
        bookmark: convertToArabic(formValues.bookmark),
      });
    }, 1000);

    return () => clearTimeout(delayDebounceFn);
  }, [formValues.bookmark]);

  const config = {
    headers: {
      "content-type": "multipart/form-data",
    },
  };

  const setFile = (e) => {
    const status = e.file.status;
    if (status === "done") {
      const formData = new FormData();
      formData.append(
        `file[${0}]`,
        e.fileList[e.fileList.length - 1].originFileObj
      );

      axios
        .post(window.ApiUrl + "/uploadMultifiles", formData, config)
        .then((res) => {
          const newAttachment = {
            path: res.data[0].data,
            fileName: getFileNameFromUrl(res.data[0].data),
          };
          setFormValues({
            ...formValues,
            attachment: res.data?.join(", "),
          });
        })
        .catch((err) => {
          message.error(t("sidemenu:uploadFilesError"));
        });
    }
  };

  // Prepare images for react-image-gallery
  const getGalleryItems = (attachments) => {
    if (!attachments || !Array.isArray(attachments)) return [];
    return attachments
      .filter((file) => {
        const fileName = file.fileName?.toLowerCase();
        return (
          fileName?.endsWith(".jpg") ||
          fileName?.endsWith(".jpeg") ||
          fileName?.endsWith(".png")
        );
      })
      .map((file) => ({
        original: `${window.filesURL}${file.path}`,
        thumbnail: `${window.filesURL}${file.path}`,
      }));
  };

  // Get PDF links
  const getPdfLinks = (attachments) => {
    if (!attachments || !Array.isArray(attachments)) return [];
    return attachments.filter((file) =>
      file.fileName?.toLowerCase().endsWith(".pdf")
    );
  };
  const handleViewAttachments = (bookmark) => {
    let attachments = bookmark.attachment;

    if (typeof attachments === "string") {
      attachments = attachments.split(", ").map((filePath) => ({
        path: filePath,
        fileName: getFileNameFromUrl(filePath),
      }));
    }

    setSelectedAttachments(attachments || []);
    setViewModalVisible(true);
  };

  return (
    <div className="coordinates mb-4 mt-4">
      <Container>
        <Form
          className="GeneralForm"
          layout="vertical"
          name="validate_other"
          onFinish={addBookMark}
        >
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              gap: "10px",
            }}
          >
            <Form.Item
              label={t("bookmark")}
              rules={[
                {
                  message: t("enterBookmark"),
                  required: true,
                },
              ]}
              className="select-cust"
            >
              <Input
                type="text"
                name="bookmark"
                onChange={handleChangeInput}
                value={formValues.bookmark}
                placeholder={t("sidemenu:entre-name")}
              />
            </Form.Item>
            <Form.Item>
              <Upload
                label={"إضافة صور/ملفات اخري"}
                name="attachment"
                fileType={"image/*,.kmz,.kml,.dwg,.pdf"}
                multiple={true}
                onInputChange={(value) => {
                  if (!value.split(", ")[0]) return;
                  setFormValues({
                    ...formValues,
                    attachment: value,
                  });
                  //message.success(t("sidemenu:uploadSuccess"));
                }}
                value={
                  (!editModalVisible &&
                    formValues.attachment
                      ?.split(", ")
                      .filter((r) => !isEmpty(r))) ||
                  []
                }
              />
            </Form.Item>
            <Button
              className="addMark mt-2"
              size="large"
              htmlType="submit"
              disabled={!formValues.bookmark}
            >
              اضافة
            </Button>
          </div>
        </Form>

        {bookmarks?.map((b, index) => (
          <div className="generalSearchCard" key={index}>
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                flexDirection: "row-reverse",
                padding: "15px",
                flex: "1",
              }}
            >
              <div
                onClick={() => zoomToBookmark(b, index)}
                style={{
                  display: "flex",
                  flexDirection: "row-reverse",
                  alignItems: "center",
                  gap: "10px",
                  flex: 1,
                }}
              >
                <img src={bookmark_icon} alt="" />
                <div
                  style={{
                    whiteSpace: "break-spaces",
                    wordBreak: "break-word",
                    paddingRight: "5px",
                  }}
                >
                  <p style={{ margin: "0px", color: "#284587" }}>{b.title}</p>
                </div>
              </div>
              <div
                style={{
                  display: "flex",
                  gap: "8px",
                  flexDirection: "row-reverse",
                }}
              >
                {/* ========================= */}
                <Tooltip title={t("attachment")}>
                  <img
                    src={edit_icon}
                    alt=""
                    onClick={() => handleViewAttachments(b)}
                    style={{ cursor: "pointer" }}
                  />
                </Tooltip>

                <Tooltip title={t("edit")}>
                  <img
                    src={interactive_edit}
                    alt=""
                    onClick={() => showEdit(b, index)}
                  />
                </Tooltip>

                <Tooltip title={t("delete")}>
                  <img
                    src={delete_icon}
                    alt=""
                    onClick={() => {
                      setDeleteModalVisible(true);
                    }}
                  />
                </Tooltip>

                <Modal
                  title={<div className="custom-modal-title">حذف</div>}
                  centered
                  closable={false}
                  visible={deleteModalVisible}
                  //onOk={() => removeBookMark(index)}
                  onCancel={() => setDeleteModalVisible(false)}
                  className="shareModal sharmodalBookmark"
                  footer={
                    <div className="footer_modal">
                      <Button
                        key="submit"
                        type="primary"
                        onClick={() => removeBookMark(b, index)}
                        className="footer_buttonEdit_modal"
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: "10px",
                        }}
                      >
                        {/* <img src={PenImg} alt="" /> */}
                        <FaCheck />
                        <span className="me-1">{t("yes")}</span>
                      </Button>
                      <Button
                        key="cancel"
                        onClick={() => {
                          setDeleteModalVisible(false);
                        }}
                        className="footer_buttonCancel_modal"
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: "10px",
                        }}
                      >
                        <IoMdClose color="red" />
                        <span className="me-1">{t("no")}</span>
                      </Button>
                    </div>
                  }
                >
                  <div
                    style={{
                      textAlign: "center",
                      fontSize: "16px",
                      fontWeight: "bold",
                      marginTop: "24px",
                    }}
                  >
                    {t("bookMarDeletionConfirmation")}
                  </div>
                </Modal>
              </div>
            </div>
            {/* Image Gallery for Images */}
            {getGalleryItems(b.attachment).length > 0 && (
              <div style={{ padding: "0 15px 15px" }}>
                <ImageGallery
                  items={getGalleryItems(b.attachment)}
                  showThumbnails={true}
                  showFullscreenButton={true}
                  showPlayButton={false}
                  originalWidth={50}
                  originalHeight={50}
                />
              </div>
            )}
            {/* PDF Links */}
            {getPdfLinks(b.attachment).length > 0 && (
              <div style={{ padding: "0 15px 15px" }}>
                <p>PDF Files:</p>
                {getPdfLinks(b.attachment).map((file, idx) => (
                  <a
                    key={idx}
                    href={`${window.filesURL}${file.path}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{ display: "block", marginBottom: "5px" }}
                  >
                    {file.fileName}
                  </a>
                ))}
              </div>
            )}
          </div>
        ))}
      </Container>
      <Modal
        className="shareModal sharmodalBookmark"
        title={
          <div className="custom-modal-title">{t("editBookmarkName")}</div>
        }
        centered
        visible={editModalVisible}
        onOk={() => afterEditModal()}
        onCancel={() => {
          setEditModalVisible(false);
          setFormValues({ bookmark: "", attachment: "" });
        }}
        closable={false}
        // okText={t("edit")}
        // cancelText={t("cancel")}
        footer={
          <div className="footer_modal">
            <Button
              key="submit"
              type="primary"
              onClick={() => afterEditModal()}
              className="footer_buttonEdit_modal"
            >
              <img src={PenImg} alt="" />
              <span className="me-1">{t("edit")}</span>
            </Button>
            <Button
              key="cancel"
              onClick={() => {
                setEditModalVisible(false);
                setFormValues({ bookmark: "", attachment: "" });
              }}
              className="footer_buttonCancel_modal"
            >
              <img src={closeModal} alt="" />
              <span className="me-1">{t("cancel")}</span>
            </Button>
          </div>
        }
      >
        <Container>
          <Form className="GeneralForm" layout="vertical" name="validate_other">
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: "10px",
                marginTop: "10px",
              }}
            >
              <Form.Item
                // label={t("bookmark")}
                label={
                  <div>
                    <span className="label_Bookmark">{t("bookmark")}</span>
                  </div>
                }
                rules={[
                  {
                    message: t("enterBookmark"),
                    required: true,
                  },
                ]}
                className="select-cust"
              >
                <Input
                  name="editName"
                  onChange={handleChangeInput}
                  value={formValues.editName}
                  placeholder={t("sidemenu:bookmarkName")}
                />
              </Form.Item>
              {editModalVisible && (
                <Form.Item>
                  <Upload
                    label={"إضافة صور/ملفات اخري"}
                    name="attachment"
                    fileType={"image/*,.kmz,.kml,.dwg,.pdf"}
                    multiple={true}
                    onInputChange={(value) => {
                      if (!value.split(", ")[0]) return;
                      setFormValues({
                        ...formValues,
                        attachment: value,
                      });
                      //message.success(t("sidemenu:uploadSuccess"));
                    }}
                    value={
                      formValues?.attachment
                        ?.split(", ")
                        .filter((r) => !isEmpty(r)) || []
                    }
                  />
                </Form.Item>
              )}
              {/* Image Gallery in Edit Modal */}
              {getGalleryItems(formValues.attachment).length > 0 && (
                <ImageGallery
                  items={getGalleryItems(formValues.attachment)}
                  showThumbnails={true}
                  showFullscreenButton={true}
                  showPlayButton={false}
                  originalWidth={50}
                  originalHeight={50}
                />
              )}
              {/* PDF Links in Edit Modal */}
              {getPdfLinks(formValues.attachment).length > 0 && (
                <div>
                  <p>PDF Files:</p>
                  {getPdfLinks(formValues.attachment).map((file, idx) => (
                    <a
                      key={idx}
                      href={`${window.filesURL}${file.path}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ display: "block", marginBottom: "5px" }}
                    >
                      {file.fileName}
                    </a>
                  ))}
                </div>
              )}
            </div>
          </Form>
        </Container>
      </Modal>

      {/* =================================== */}
      <Modal
        title="عرض المرفقات"
        centered
        visible={viewModalVisible}
        onCancel={() => setViewModalVisible(false)}
        footer={null}
        width={"50vw"}
      >
        <Tabs defaultActiveKey="1">
          <TabPane tab="الصور" key="1">
            {getGalleryItems(selectedAttachments).length > 0 ? (
              <ImageGallery
                items={getGalleryItems(selectedAttachments)}
                showThumbnails={true}
                showFullscreenButton={true}
                showPlayButton={false}
                originalWidth={50}
                originalHeight={50}
              />
            ) : (
              <p>لا توجد صور مرفقة</p>
            )}
          </TabPane>
          <TabPane tab="PDF" key="2">
            {getPdfLinks(selectedAttachments).length > 0 ? (
              getPdfLinks(selectedAttachments).map((file, idx) => (
                <a
                  key={idx}
                  href={`${window.filesURL}${file.path}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{ display: "block", marginBottom: "5px" }}
                >
                  {file.fileName}
                </a>
              ))
            ) : (
              <p>لا توجد ملفات PDF</p>
            )}
          </TabPane>
        </Tabs>
      </Modal>
    </div>
  );
}
