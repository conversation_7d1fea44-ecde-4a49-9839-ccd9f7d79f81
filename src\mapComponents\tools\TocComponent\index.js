// import React, { Component } from "react";
// import { LayerComponent } from "./layer_component";
// import { Slider } from "antd";

// import * as watchUtils from "@arcgis/core/core/watchUtils";
// // import { layersSetting } from "../../../helper/layers";
// import { withTranslation } from "react-i18next";
// import i18n from "../../../i18n";
// import closeIconFigma from "../../../assets/icons/closeIconFigma.svg";

// class TocComponent extends Component {
//   state = {};

//   constructor(props) {
//     super(props);

//     this.state = {
//       layers: props.map.__mapInfo.info,
//     };
//   }
//   startOperation() {
//     this.setState({
//       showPopUp: !this.state.showPopUp,
//     });
//   }
//   expand(layer, key) {
//     let { layers } = this.state;
//     let reqLayer = layers.$legends.find(
//       (lay) => lay.layerName === layer.layerName
//     );
//     reqLayer.show = !reqLayer.show;
//     this.setState({ layers });
//   }
//   changeLayer(layer, key) {
//     let { layers } = this.state;
//     let reqLayer = layers.$legends.find(
//       (lay) => lay.layerName === layer.layerName
//     );
//     reqLayer.visible = !reqLayer.visible;
//     this.setState({ layers });

//     let visibiles = this.state.layers.$legends
//       .filter((layer) => layer.visible)
//       .map((d) => d.layerId);

//     //this.props.map.mapVisibleLayerIDs = visibiles;

//     this.props.map
//       .findLayerById("baseMap")
//       .allSublayers._items.forEach((layer) => {
//         if (!layer.sublayers) {
//           let l = visibiles.find((x) => x == layer.id);
//           layer.visible = l != null ? true : false;
//         }
//       });
//   }

//   zoomToLayer(layer, key) {
//     if (layer && layer.minScale > 0 && layer.disable) {
//       var dpi = 96; // Set to resolution of your screen
//       var scale = layer.minScale;
//       //this.props.map.view.scale = scale;
//       var mapunitInMeters = 111319.5; // size of one degree at Equator. Change if you are using a projection with a different unit than degrees
//       var newRes = scale / (dpi * 39.37 * mapunitInMeters);
//       var newExtent = this.props.map.view.extent.expand(newRes * 200);
//       this.props.map.view.goTo(newExtent);

//       let tempLayer = this.props.map
//         .findLayerById("baseMap")
//         .allSublayers._items.find((x) => x.id == layer.layerId);

//       if (tempLayer) {
//         tempLayer.visible = true;
//         let { layers } = this.state;
//         layers.$legends[key].visible = true;
//         this.setState({ layers });
//       }
//     }
//   }
//   componentDidMount() {
//     var self = this;
//     let { layers } = this.state;
//     watchUtils.when(this.props.map.view, "stationary", (evt) => {
//       if (evt) {
//         var mapScale = this.props.map.view.scale;
//         // visable layers in thier scale
//         layers.$legends.forEach((layer) => {
//           let minScale = layer.minScale;
//           let maxScale = layer.maxScale;

//           if (
//             (maxScale <= mapScale || maxScale == 0) &&
//             (minScale >= mapScale || minScale == 0)
//           ) {
//             layer.disable = "enableLabel";
//           } else {
//             layer.disable = "disableLabel";
//           }
//         });

//         self.setState({ layers });
//       }
//     });
//   }

//   onSliderChange = (value) => {
//     this.props.map.findLayerById("baseMap").opacity = value / 100;
//   };

//   render() {
//     const { t } = this.props;
//     const legends = this.state.layers.$legends;
//     return (
//       <section className="toc">
//         <div>
//           <div className="Heading_tocComponent">
//             <img
//               onClick={this.props.closeToolsData}
//               src={closeIconFigma}
//               alt=""
//             />
//             <label>{t("mainLayer")}</label>
//           </div>
//           <div >
//             <Slider defaultValue={100} onChange={this.onSliderChange} />
//           </div>
//           <ul className="tocComponent_Ul">
//             {legends
//               .filter((lay) =>
//                 Object.keys(this.props.mainData.layers).includes(lay.layerName)
//               )
//               .filter(
//                 (lay) =>
//                   !lay.isHidden && this.props.mainData.layers[lay?.layerName]
//               )
//               .map((layer, key) => {
//                 return (
//                   <li
//                     style={{
//                       direction: i18n.language === "ar" ? "rtl" : "ltr",
//                     }}
//                   >
//                     <LayerComponent
//                       mainData={this.props.mainData}
//                       languageState={this.props.languageState}
//                       galleryHead
//                       {...{
//                         layer,
//                         changeLayer: this.changeLayer.bind(this, layer, key),
//                         zoomToLayer: this.zoomToLayer.bind(this, layer, key),
//                         expand: this.expand.bind(this, layer, key),
//                       }}
//                     />
//                   </li>
//                 );
//               })}
//           </ul>
//         </div>
//       </section>
//     );
//   }
// }
// export default withTranslation("common")(TocComponent);

import React, { Component } from "react";
import { LayerComponent } from "./layer_component";
import { Slider } from "antd";
import * as watchUtils from "@arcgis/core/core/watchUtils";
import { withTranslation } from "react-i18next";
import i18n from "../../../i18n";
import closeIconFigma from "../../../assets/icons/closeIconFigma.svg";

class TocComponent extends Component {
  state = {
    layers: this.props.map.__mapInfo.info,
    isDragging: false,
    diffX: 0,
    diffY: 0,
    position: { x: 0, y: 0 }, // initial position
  };

  componentDidMount() {
    //window.handles.remove("map-events");
    const { layers } = this.state;
    watchUtils.when(this.props.map.view, "stationary", (evt) => {
      if (evt) {
        const mapScale = this.props.map.view.scale;
        layers.$legends.forEach((layer) => {
          const { minScale, maxScale } = layer;
          layer.disable =
            (maxScale <= mapScale || maxScale === 0) &&
            (minScale >= mapScale || minScale === 0)
              ? "enableLabel"
              : "disableLabel";
        });
        this.setState({ layers });
      }
    });
    
  }

  onSliderChange = (value) => {
    this.props.map.findLayerById("baseMap").opacity = value / 100;
  };

  // Other methods (changeLayer, zoomToLayer, expand) remain the same...

  changeLayer(layer, key) {
    const { layers } = this.state;
    const reqLayer = layers.$legends.find(
      (lay) => lay.layerName === layer.layerName
    );
    reqLayer.visible = !reqLayer.visible;
    this.setState({ layers });

    const visibiles = layers.$legends
      .filter((l) => l.visible)
      .map((d) => d.layerId);

    this.props.map
      .findLayerById("baseMap")
      .allSublayers._items.forEach((layer) => {
        if (!layer.sublayers) {
          const found = visibiles.find((x) => x === layer.id);
          layer.visible = found != null;
        }
      });
  }

  zoomToLayer(layer, key) {
    if (layer && layer.minScale > 0 && layer.disable) {
      const dpi = 96;
      const scale = layer.minScale;
      const mapunitInMeters = 111319.5;
      const newRes = scale / (dpi * 39.37 * mapunitInMeters);
      const newExtent = this.props.map.view.extent.expand(newRes * 200);
      this.props.map.view.goTo(newExtent);

      const tempLayer = this.props.map
        .findLayerById("baseMap")
        .allSublayers._items.find((x) => x.id === layer.layerId);

      if (tempLayer) {
        tempLayer.visible = true;
        const { layers } = this.state;
        layers.$legends[key].visible = true;
        this.setState({ layers });
      }
    }
  }

  expand(layer, key) {
    const { layers } = this.state;
    const reqLayer = layers.$legends.find(
      (lay) => lay.layerName === layer.layerName
    );
    reqLayer.show = !reqLayer.show;
    this.setState({ layers });
  }

  // Drag handlers ONLY attached to Heading_tocComponent
  onMouseDown = (e) => {
    e.preventDefault();

    const diffX = e.clientX - this.state.position.x;
    const diffY = e.clientY - this.state.position.y;

    this.setState({
      isDragging: true,
      diffX,
      diffY,
    });

    document.addEventListener("mousemove", this.onMouseMove);
    document.addEventListener("mouseup", this.onMouseUp);
  };

  onMouseMove = (e) => {
    if (!this.state.isDragging) return;

    const { diffX, diffY } = this.state;
    const newX = e.clientX - diffX;
    const newY = e.clientY - diffY;

    // Get viewport size
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // Get component size
    const box = this.containerRef.getBoundingClientRect();
    const boxWidth = box.width;
    const boxHeight = box.height;

    // Clamp values within the viewport and stop at bottom - 200px
    const clampedX = Math.min(Math.max(0, newX), windowWidth - boxWidth);
    const clampedY = Math.min(
      Math.max(0, newY),
      windowHeight - boxHeight + 335
    );

    this.setState({
      position: {
        x: clampedX,
        y: clampedY,
      },
    });
  };

  onMouseUp = () => {
    this.setState({ isDragging: false });
    document.removeEventListener("mousemove", this.onMouseMove);
    document.removeEventListener("mouseup", this.onMouseUp);
  };

  render() {
    const { t } = this.props;
    const legends = this.state.layers.$legends;
    const { x, y, isDragging } = this.state.position;

    return (
      <section
        ref={(ref) => (this.containerRef = ref)}
        className="toc"
        style={{
          position: "absolute",
          top: this.state.position.y,
          left: this.state.position.x,
          zIndex: 1000,
          cursor: isDragging ? "grabbing" : "default",
          userSelect: "none",
          minWidth: "350px",
          background: "white",
          border: "1px solid #ccc",
          borderRadius: "16px",
          boxShadow: "0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%)",
          padding: "16px",
        }}
      >
        <div>
          <div
            className="Heading_tocComponent"
            onMouseDown={this.onMouseDown}
          >
            <img
              onClick={this.props.closeToolsData}
              src={closeIconFigma}
              alt=""
            />
            <label>{t("mainLayer")}</label>
          </div>
          <div className="opacityLayers">
            <span>الشفافية</span>
            <Slider className="sliderLayers" defaultValue={100} onChange={this.onSliderChange} />
          </div>
          <ul
            className="tocComponent_Ul"
            style={{ padding: "0 10px 10px 10px" }}
          >
            {legends
              .filter((lay) =>
                Object.keys(this.props.mainData.layers).includes(lay.layerName)
              )
              .filter(
                (lay) =>
                  !lay.isHidden && this.props.mainData.layers[lay?.layerName]
              )
              .map((layer, key) => (
                <li
                  key={key}
                  style={{
                    direction: i18n.language === "ar" ? "rtl" : "ltr",
                    listStyleType: "none",
                    marginBottom: 4,
                  }}
                >
                  <LayerComponent
                    mainData={this.props.mainData}
                    languageState={this.props.languageState}
                    galleryHead
                    {...{
                      layer,
                      changeLayer: this.changeLayer.bind(this, layer, key),
                      zoomToLayer: this.zoomToLayer.bind(this, layer, key),
                      expand: this.expand.bind(this, layer, key),
                    }}
                  />
                </li>
              ))}
          </ul>
        </div>
      </section>
    );
  }
}

export default withTranslation("common")(TocComponent);
