import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTranslation } from "react-i18next";
import { Fade } from "react-reveal";
import { faTimes } from "@fortawesome/free-solid-svg-icons";
import { Select } from "antd";
import { DownCircleFilled } from "@ant-design/icons";
import { useEffect, useRef, useState } from "react";
import Swipe from "@arcgis/core/widgets/Swipe";
import { getLayerId, showLoading } from "../../helper/common_func";
import MapImageLayer from "@arcgis/core/layers/MapImageLayer";
import * as watchUtils from "@arcgis/core/core/watchUtils";
import eventBus from "../../helper/EventBus";
import { RiArrowDropDownFill } from "react-icons/ri";
import Draggable from "react-draggable";
import { loginLayersSetting } from "../../helper/layers";
import closeIconFigma from "../../assets/icons/closeIconFigma.svg";
import arrow_drop_down from "../../assets/icons/arrow_drop_down.svg";
import arrow_drop_up from "../../assets/icons/arrow_drop_up.svg";



export default function CompareLayers(props) {
  const containerRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [diff, setDiff] = useState({ x: 0, y: 0 });
  const [position, setPosition] = useState({ x: 10, y: 0 });

  console.log("compare layers props", props);
  const { t, i18n } = useTranslation("map");
  const [trailingLayer, setTrailingLayer] = useState(null);
  const [leadingLayer, setLeadingLayer] = useState(null);
  const [layers, setLayers] = useState(null);
  const [open1, setOpen1] = useState(false);
  const [open2, setOpen2] = useState(false);
  const [compareClicked, setComparedClicked] = useState(false);
  let swipe, handle;

  const HasArabicCharacters = (text) => {
    var arregex = /[\u0600-\u06FF]/;
    return arregex.test(text);
  };
  useEffect(() => {
    // if (!props.map?.view) return;
    if (!props.map?.view || !props.mainData?.layers) return;

    handle = watchUtils.watch(props.map.view, ["updating"], (e) => {
      // Do something
      if (e == false) showLoading(e);
    });

    let compareLayerlist = [
      ...(Object.keys(props.mainData.layers || {})
        .filter((layer) =>
          HasArabicCharacters(props.mainData.layers[layer].arabicName)
        )
        .map((layer) => {
          return {
            value: layer,
            label: props.mainData.layers[layer].arabicName,
          };
        }) || []),
      ...(props.map.layers.items
        .filter((x) => x.id.indexOf("compareLayerList_") > -1)
        ?.map((layer) => {
          return {
            value: layer.id,
            label: layer.title,
            url: layer.url,
          };
        }) || []),
    ];

    setLayers(compareLayerlist);
  }, []);

  useEffect(() => {
    return () => {
      //destroy swipe control
      destroySwipe();
    };
  }, []);



const destroySwipe = () => {
  let swipeControl = props.map.view.ui._components.find(
    (x) =>
      x.widget._container &&
      x.widget._container.className.indexOf("swipe") > -1
  );

  if (swipeControl) {
    swipe?.leadingLayers?.removeAll();
    swipe?.trailingLayers?.removeAll();

    swipeControl.widget.destroy();
    props.map.view.ui.remove(swipeControl);

    const customEvent = new CustomEvent("showSwipLabels", {
      detail: { show: false },
    });
    document.dispatchEvent(customEvent);
  }
};


//=====================================================================
  const removeAllImportLayer = () => {
    props.map.layers.items
      .filter((x) => x.id.indexOf("compareLayerList_") > -1)
      ?.forEach((layer) => {
        props.map.findLayerById(layer.id).visible = false;
      });
  };

  const showCompare = () => {
    setComparedClicked(true)
  
      // Step 1: Remove previous swipe control and labels
  destroySwipe();


 // Clear swipe labels if exist
const labelContainer = document.getElementById("swipeLabelContainer");
if (labelContainer) {
  labelContainer.innerHTML = ""; // 
}

  // Step 2: Hide base map and clear imported compare layers

    var basemapLayer = props.map.findLayerById("baseMap");
    basemapLayer.visible = false;

    removeAllImportLayer();
    eventBus.dispatch("unCheckImportLayers", { message: {} });

  // Step 3: Remove previous compare layers from map
  const oldLeading = props.map.findLayerById("leadingLayer");
  const oldTrailing = props.map.findLayerById("trailingLayer");
  if (oldLeading) props.map.remove(oldLeading);
  if (oldTrailing) props.map.remove(oldTrailing);

  // Step 4: Create new compare layers

    let leadingLayerId = getLayerId(props.map.__mapInfo, leadingLayer.value);
    let trailingLayerId = getLayerId(props.map.__mapInfo, trailingLayer.value);

    let leadingL = leadingLayer.url
      ? new MapImageLayer({
          url: leadingLayer.url,
          id: "leadingLayer",
        })
      : new MapImageLayer({
          url: basemapLayer.url,
          sublayers: [
            {
              id: leadingLayerId,
              visible: true,
            },
          ],
          id: "leadingLayer",
        });

    let trailingL = trailingLayer.url
      ? new MapImageLayer({
          url: trailingLayer.url,
          id: "trailingLayer",
        })
      : new MapImageLayer({
          url: basemapLayer.url,
          sublayers: [
            {
              id: trailingLayerId,
              visible: true,
            },
          ],
          id: "trailingLayer",
        });

    props.map.add(leadingL);
    props.map.add(trailingL);

    Promise.all([leadingL.when(), trailingL.when()]).then(() => {
      // let leadingExtent = leadingL.fullExtent.clone();
      // let trailingExtent = trailingL.fullExtent.clone();
      // var combinedExtent = leadingExtent.union(trailingExtent);
      // props.map.view.goTo(combinedExtent);
      props.map.view.goTo(window.__fullExtent);
      //========== Back to maps 
          showLoading(false);

                props.map.view.goTo({
            center: props.map.view.center,
            zoom: props.map.view.zoom
          });
    });

    showLoading(true);

    swipe = new Swipe({
      view: props.map.view,
      leadingLayers: [leadingL],
      trailingLayers: [trailingL],
      direction: "horizontal", // swipe widget will move from top to bottom of view
      position: 50, // position set to middle of the view (50%)
    });

    props.map.view.ui.add(swipe);

    const customEvent = new CustomEvent("showSwipLabels", {
      detail: { show: true },
    });
    document.dispatchEvent(customEvent);
    //==================================
//=== Add New commpare
  // Step 6: Add new swipe labels

 if (labelContainer) {
    const newLeft = document.createElement("div");
    newLeft.id = "leftSwipeLabel";
    newLeft.style.cssText = "padding: 10px; position: absolute; top: 100px; z-index: 1000;";
    newLeft.innerText = leadingLayer.label || "Leading Layer";

    const newRight = document.createElement("div");
    newRight.id = "rightSwipeLabel";
    newRight.style.cssText = "padding: 10px; position: absolute; top: 100px; z-index: 1000;";
    newRight.innerText = trailingLayer.label || "Trailing Layer";

    labelContainer.appendChild(newLeft);
    labelContainer.appendChild(newRight);

      const viewWidth = props.map.view.width;
    const swipeX = (50 / 100) * viewWidth;
    newLeft.style.left = `${swipeX - newLeft.offsetWidth - 95}px`;
    newRight.style.left = `${swipeX + -7}px`;

    swipe.watch("position", (position) => {
      const swipeX = (position / 100) * viewWidth;
      newLeft.style.left = `${swipeX - newLeft.offsetWidth - 95}px`;
      newRight.style.left = `${swipeX + -7}px`;
    });
  } else {
    console.error("Swipe labels not found in DOM");
  }

  showLoading(false);
};

  const [x, setX] = useState(0);
  const [y, setY] = useState(0);

  const handleDrag = (e, position) => {
    setX(position.x);
    setY(position.y);
  };

  // Get header height once mounted
  const headerHeightRef = useRef(40);

  useEffect(() => {
    const header = containerRef.current?.querySelector(".Heading_tocComponent");
    if (header) {
      headerHeightRef.current = header.offsetHeight;
    }

    const handleMouseMove = (e) => {
      if (!isDragging || !containerRef.current) return;

      const newX = e.clientX - diff.x;
      const newY = e.clientY - diff.y;

      const box = containerRef.current.getBoundingClientRect();
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;

      const boxWidth = box?.width || 300;
      const boxHeight = box?.height;

      const clampedX = Math.max(0, Math.min(newX, windowWidth - boxWidth));
      const clampedY = Math.max(
        0,
        Math.min(newY, windowHeight - boxHeight + 150)
      );

      setPosition({ x: clampedX, y: clampedY });
    };

    const handleMouseUp = () => setIsDragging(false);

    window.addEventListener("mousemove", handleMouseMove);
    window.addEventListener("mouseup", handleMouseUp);
    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isDragging, diff]);

  const onMouseDown = (e) => {
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    setDiff({
      x: e.clientX - rect.left + 50,
      y: e.clientY - rect.top + 66,
    });
    setIsDragging(true);
  };

  //====================================
const resetToOriginalLayer = () => {
  destroySwipe(); //

  const oldLeading = props.map.findLayerById("leadingLayer");
  const oldTrailing = props.map.findLayerById("trailingLayer");
  if (oldLeading) props.map.remove(oldLeading);
  if (oldTrailing) props.map.remove(oldTrailing);

  const parcelsLayer = props.map.findLayerById("baseMap"); 
  if (parcelsLayer) parcelsLayer.visible = true;

  const labelContainer = document.getElementById("swipeLabelContainer");
  if (labelContainer) {
    labelContainer.innerHTML = "";
  }

  eventBus.dispatch("showOriginalParcelsData", { message: {} });
};
  return (
    // <Fade left collapse>
    // <Draggable
    //   position={{ x, y }}
    //   onDrag={handleDrag}
    //   bounds={{
    //     left:
    //       i18n.language === "ar"
    //         ? -(window.innerWidth - 400)
    //         : -window.innerWidth,
    //     top: -300,
    //     right: i18n.language === "ar" ? window.innerWidth - 400 : 0, // Subtract component width
    //     bottom: window.innerHeight - 350, // Subtract component height
    //   }}
    // >
    <div style={{ top: "100px", insetInlineEnd: "12px" }} className="drg">
      <Fade left collapse>
        <div
          className="layersMenu"
          // style={{
          //   display: "flex",
          //   alignItems: "center",
          //   justifyContent: "space-between",
          //   color: "#000",
          //   padding: "8px",
          // }}
        >
          <div
            ref={containerRef}
            className="fef"
            style={{
              position: "absolute",
              left: position.x,
              top: position.y,
              zIndex: 9999,
              cursor: isDragging ? "grabbing" : "default",
              userSelect: "none",
              minWidth: "350px",
              background: "white",
              border: "1px solid #ccc",
              borderRadius: "16px",
              boxShadow:
                "0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%)",
              padding: "16px",
            }}
          >
            <div
              onMouseDown={onMouseDown}
              className="Heading_tocComponent"
              style={{ flexDirection: "row-reverse",  }}
            >
              <img
                onClick={(e) => {
                  e.stopPropagation();
                  resetToOriginalLayer(); // back to baseMap
                  props.closeToolsData();
                  props.onClose();
                }}
                src={closeIconFigma}
                alt=""
              />
              <label>{t("mapToolsServices.compareLayers")}</label>
            </div>

            <section
              className=""
              style={{ direction: i18n.language === "ar" ? "rtl" : "ltr" }}
            >
              <div
                style={{
                  marginTop: "10px",
                  display: "flex",
                  flexDirection: "column",
                  gap: "10px",
                }}
              >
                <div className="select-cust">
                  <label className="selectLabelStyle">
                    {t("mapToolsServices.select first layer")}
                  </label>
                  <Select
                    onDropdownVisibleChange={(flag) => setOpen1(flag)}
                    onMouseDown={(e) => e.stopPropagation()}
                    onChange={(name, e) => (
                      setOpen1(false), setTrailingLayer(e)
                    )}
                    onSelect={() => setOpen1(true)}
                    placeholder={t("mapToolsServices.selectFirstLayerNote")}
                    style={{ width: 120 }}
                    // suffixIcon={<DownCircleFilled />}
                    //suffixIcon={<RiArrowDropDownFill size={30} />}
                    suffixIcon={
                      open1 ? (
                        <img src={arrow_drop_up} alt="" />
                      ) : (
                        <img src={arrow_drop_down} alt="" />
                      )
                    }
                    options={layers}
                    // options={[
                    //   { value: "sample 1" },
                    //   { value: "sample 2" },
                    //   { value: "sample 3" },
                    //   { value: "sample 4" },
                    //   { value: "sample 5" },
                    //   { value: "sample 6" },
                    //   { value: "sample 7" },
                    // ]}
                    className="compareLayers-select"
                  />
                </div>

                <div className="select-cust">
                  <label className="selectLabelStyle">
                    {t("mapToolsServices.select second layer")}
                  </label>
                  <Select
                    onDropdownVisibleChange={(flag) => setOpen2(flag)}
                    onMouseDown={(e) => e.stopPropagation()}
                    onChange={(name, e) => (
                      setOpen2(false), setLeadingLayer(e)
                    )}
                    placeholder={t("mapToolsServices.selectSeconedLayerNote")}
                    style={{ width: 120 }}
                    onSelect={() => setOpen2(true)}
                    // suffixIcon={<DownCircleFilled />}
                    //suffixIcon={<RiArrowDropDownFill size={30} />}
                    suffixIcon={
                      open2 ? (
                        <img src={arrow_drop_up} alt="" />
                      ) : (
                        <img src={arrow_drop_down} alt="" />
                      )
                    }
                    options={layers}
                    className="compareLayers-select"
                  />
                </div>

                <button
                  className="SearchBtn"
                  size="large"
                  htmlType="submit"
                  onClick={showCompare}
                  disabled={
                    !trailingLayer ||
                    !leadingLayer ||
                    leadingLayer == trailingLayer
                  }
                >
                  {t("mapToolsServices.compare")}
                </button>
              </div>
            </section>
          </div>
        </div>
      </Fade>
   <div id="swipeLabelContainer" style={{padding:compareClicked?"10px":0}}></div>
    </div>
    // </Draggable>

    // </Fade>
  );
}
