import React, { useEffect, useRef, useState } from "react";
import { Container } from "react-bootstrap";
import { useTranslation } from "react-i18next";
import axios from "axios";
import { Box, Grid, Dialog, Slide } from "@mui/material";
import UpdateDataLandView from "../sidemenu-Components/UpdateDataLand_view";
import debounce from "lodash.debounce";
import styles from "./Tracking.module.css";
import {
  convertToEnglish,
  getFeatureDomainName,
  getLayerId,
  highlightFeature,
  queryTask,
} from "../helper/common_func";
import { Form, Input } from "antd";
import { convertToArabic } from "../helper/common_func";
const host = window.ApiUrl; // Replace with actual API host

const UpdatingRequests = (props) => {
  const { t } = useTranslation("common");
  // const [isLoading, setIsLoading] = useState(false);
  const [tickets, setTickets] = useState([]);
  const [totalPages, setTotalPages] = useState(0);
  const [currentPageNo, setCurrentPageNo] = useState(0);
  const [searchValue, setSearchValue] = useState("");
  const [inputTimer, setInputTimer] = useState(null);
  const containerRef = useRef(null);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [features, setFeatures] = useState([]);

  const setTicket = (record) => {
    if (record?.id != selectedOption?.id) {
      setFeatures([]);
      setSelectedOption(record);
    } else if (record?.id == selectedOption?.id && features.length > 0) {
      setIsOpen(true);
    }
  };
  const handleInputChange = (value) => {
    setSearchValue(value);
  };

  const handleScroll = () => {
    const container = containerRef.current;
    if (container) {
      if (
        Math.abs(
          container.scrollHeight - container.scrollTop - container.clientHeight
        ) < 5
      ) {
        if (currentPageNo < totalPages) {
          getTickets(currentPageNo + 1);
        }
      }
    }
  };

  const debouncedHandleScroll = debounce(handleScroll, 200);

  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener("scroll", debouncedHandleScroll);
    }
    return () => {
      if (container) {
        container.removeEventListener("scroll", debouncedHandleScroll);
      }
      debouncedHandleScroll.cancel();
    };
  }, [debouncedHandleScroll]);

  useEffect(() => {
    window.DisableActiveTool();
  }, []);

  const getTickets = (pageNum) => {
    //  setIsLoading(true);
    pageNum = pageNum || 1;

    if (inputTimer) {
      clearTimeout(inputTimer);
    }

    setInputTimer(
      setTimeout(() => {
        axios
          .get(
            `${host}/Ticket?appId=5&pageIndex=${pageNum}&itemsPerPage=12${
              (searchValue && `&ticketId=${searchValue}`) || ""
            }`
            // ,
            // {
            //   headers: {
            //     authorization: `bearer ${props?.mainData?.user?.token}`,
            //   },
            // }
          )
          .then((res) => {
            const result = res.data.tickets;
            setCurrentPageNo(pageNum);
            setTotalPages(res.data.totalPages);
            if (!result.length) {
              setTickets([]);
            } else {
              setTickets(pageNum > 1 ? [...tickets, ...result] : [...result]);
            }
            // setIsLoading(false);
          })
          .catch(() => {
            // setIsLoading(false);
          });
      }, 500)
    );
  };

  useEffect(() => {
    getTickets();
  }, [searchValue]);

  useEffect(() => {
    if (selectedOption) {
      let supportingInfo = convertToEnglish(selectedOption.supportingInfo);
      if (supportingInfo) {
        //   setIsLoading(true);

        let arrays = supportingInfo.match(/\[.*?\]/g);
        let settings = {};
        let where = "1=1";
        let layerIndex = 0;

        if (eval(arrays[0]).length > 0) {
          where = `PARCEL_SPATIAL_ID IN (${eval(arrays[0]).join(",")})`;
          layerIndex = getLayerId(props.map.__mapInfo, "Landbase_Parcel");
          settings.url = `${window.mapUrl}/${layerIndex}`;
        } else if (eval(arrays[1]).length > 0) {
          where = `PLAN_SPATIAL_ID IN (${eval(arrays[1]).join(",")})`;
          layerIndex = getLayerId(props.map.__mapInfo, "Plan_Data");
          settings.url = `${window.mapUrl}/${layerIndex}`;
        } else if (eval(arrays[2]).length > 0) {
          where = ` OBJECTID IN (${eval(arrays[2]).join(",")})`;
          layerIndex = getLayerId(props.map.__mapInfo, "Street_Naming");
          settings.url = `${window.mapUrl}/${layerIndex}`;
        }

        if (settings.url) {
          settings = {
            ...settings,
            outFields: ["*"],
            //url: window.mapUrl,
            where: where,
            returnGeometry: true,
            callbackResult: async ({ features }) => {
              highlightFeature(features, props.map, {
                layerName: "ZoomGraphicLayer",
                noclear: false,
                isHighlighPolygonBorder: true,
                isZoom: true,
                zoomFactor: 120,
                zoom: 18,
                offset: 50,
              });
              features = await getFeatureDomainName(features, layerIndex);
              setFeatures(features);
              setIsOpen(true);
              //   setIsLoading(false);
            },
            callbackError: () => {
              setFeatures([]);
              setIsOpen(false);
              // setIsLoading(false);
            },
          };
          queryTask(settings);
        }
      }
    }
  }, [selectedOption]);

  return (
    <div
      className="MeasureTool"
      style={{
        width: "100%",
        direction: "rtl",
      }}
    >
      {!isOpen && (
        <div className="coordinates measurePage">
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              gap: "10px",
              overflowY: "hidden",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                // marginBottom: "15px",
              }}
            >
              {/* <h1
                style={{
                  fontSize: "18px",
                  margin: 0,
                  fontWeight: "bold",
                  color: "#333",
                }}
              >
                {t("common:requests") || "الطلبات"}
              </h1> */}
              {/* {isLoading && (
                <div
                  style={{
                    border: "4px solid #f3f3f3",
                    borderTop: "4px solid #3498db",
                    borderRadius: "50%",
                    width: "24px",
                    height: "24px",
                    animation: "spin 1s linear infinite",
                  }}
                />
              )} */}
            </div>

            <Form
              className="GeneralForm"
              layout="vertical"
              name="validate_other"
              // style={{ width: "100%", padding: "0 10px" }}
              // onFinish={addBookMark}
              style={{ width: "100%", padding: "0 10px" }}
            >
              <Form.Item
                label={"رقم الطلب"}
                rules={[
                  {
                    message: "ابحث برقم الطلب",
                    required: true,
                  },
                ]}
                className="select-cust"
              >
                <Input
                  type="search"
                  name="bookmark"
                  value={searchValue}
                  onChange={(e) => handleInputChange(e.target.value)}
                  placeholder={"ابحث برقم الطلب"}
                />
              </Form.Item>
            </Form>

            <div
              ref={containerRef}
              style={{
                maxHeight: "400px",
                width: "100%",
                padding: "0 10px",
                overflowY: "auto",
              }}
            >
              <Grid container spacing={1}>
                {tickets.map((r) => (
                  <Grid
                    item
                    xs={12}
                    onClick={() => {
                      setTicket(r);
                    }}
                    style={{
                      padding: "0 10px",
                    }}
                  >
                    <div
                      className={`generalSearchCard ${styles.card} ${
                        (selectedOption?.id == r.id && styles.active) || ""
                      }`}
                      style={{
                        height: "60px",
                      }}
                    >
                      <div
                        className={styles.favoriteCardContent}
                        style={{ height: "100%", flex: 1 }}
                      >
                        <div
                          className={styles.favoriteInfo}
                          style={{
                            display: "flex",
                            gap: "10px",
                            height: "100%",
                          }}
                        >
                          <div
                            style={{
                              padding: "0",
                              margin: "0",
                              color: "#284587",
                              fontSize: "16px",
                              fontWeight: "400",
                            }}
                          >
                            رقم الطلب:{" "}
                            {convertToArabic(r.title.match(/(\d+\/?)+/g)[0]) ||
                              ""}
                          </div>
                          {/* <div
                            style={{
                              padding: "0",
                              margin: "0",
                              color: "#284587",
                              fontSize: "14px",
                              fontWeight: "400",
                            }}
                          >
                            
                          </div> */}
                          <p className={styles.favoriteAddress}>
                            {/* {t("common:ticket_status")} :{" "} */}
                            <span
                              className={
                                r.isClosed ? styles.finished : styles.running
                              }
                              style={{
                                fontSize: "11px",
                                fontWeight: "400",
                              }}
                            >
                              {(r.isClosed && "منتهية") || "قيد المعالجة"}
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </Grid>
                ))}
                {tickets.length === 0 && (
                  // <Grid
                  //   item
                  //   xs={12}
                  //   className="text-center generalSearchCard"
                  //   style={{ color: "#a8a8a8" }}
                  // >
                  //   {t("generalSearch:empty")}
                  // </Grid>
                  <div
                    className="generalSearchCard"
                    style={{
                      width: "100%",
                      textAlign: "center",
                      marginInlineEnd: "10px",
                      padding: "20px",
                    }}
                  >
                    {t("generalSearch:empty")}
                  </div>
                )}
              </Grid>
            </div>
          </div>
        </div>
      )}
      {isOpen && (
        <UpdateDataLandView
          setIsOpen={setIsOpen}
          ticketData={selectedOption}
          features={features}
          map={props.map}
        />
      )}
      <style jsx>{`
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
};

export default UpdatingRequests;
